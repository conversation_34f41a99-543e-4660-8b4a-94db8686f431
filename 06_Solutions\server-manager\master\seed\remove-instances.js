const db = require('../db');

console.log('🔄 Starting removal of instances with IP ************...');

const deleteQuery = `DELETE FROM INSTANCE_DATA WHERE server_ip = ?`;

db.run(deleteQuery, ['************'], function(err) {
    if (err) {
        console.error('❌ Error removing instances:', err.message);
        process.exit(1);
    }
    
    console.log(`✅ Successfully removed ${this.changes} instances with IP ************`);
    
    // Verify the removal
    db.all('SELECT * FROM INSTANCE_DATA WHERE server_ip = ?', ['************'], (err, rows) => {
        if (err) {
            console.error('❌ Error verifying removal:', err.message);
            process.exit(1);
        }
        
        if (rows.length === 0) {
            console.log('✅ Verification successful - No instances found with IP ************');
        } else {
            console.error('❌ Verification failed - Some instances still exist');
            console.log('Remaining instances:', rows);
        }
        
        // Close the database connection
        db.close(() => {
            console.log('📡 Database connection closed.');
            process.exit(0);
        });
    });
});