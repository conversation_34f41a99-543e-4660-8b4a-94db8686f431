// db.js
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = process.env.DATABASE_PATH || path.resolve(__dirname, 'database.sqlite');

const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error("❌ Error connecting to the database:", err.message);
    } else {
        console.log("📡 Connected to the SQLite database.");
    }
});

// SQL definitions
const createInstanceDataTable = `
CREATE TABLE IF NOT EXISTS INSTANCE_DATA (
  ID INTEGER PRIMARY KEY AUTOINCREMENT,
  server_ip TEXT NOT NULL,
  instance_name TEXT NOT NULL,
  instance_title TEXT,
  instance_url TEXT NOT NULL,
  description TEXT,
  instance_owner TEXT,
  is_public INTEGER DEFAULT 0
);
`;

const createUsersTable = `
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    role TEXT NOT NULL,
    created_at DATETIME
);
`;

const createServersTable = `
CREATE TABLE IF NOT EXISTS servers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_ip TEXT NOT NULL UNIQUE,
    server_name TEXT NOT NULL
)`;

const initializeServer = `
INSERT OR IGNORE INTO servers (id, server_ip, server_name)
VALUES (0, ?, 'Master Server')`;

db.serialize(() => {
    db.run(createInstanceDataTable, (err) => {
        if (err) {
            console.error("❌ Error creating instance_data table:", err.message);
        } else {
            console.log("✅ Instance_data table is ready.");
        }
    });
    
    db.run(createServersTable, (err) => {
        if (err) {
            console.error("❌ Error creating servers table:", err.message);
        } else {
            console.log("✅ Servers table is ready.");
            // Initialize master server with dynamic IP
            const { getLocalIPAddress } = require('./utils/networkUtils');
            getLocalIPAddress().then(ip => {
                db.run(initializeServer, [ip], (err) => {
                    if (err) {
                        console.error("❌ Error initializing master server:", err.message);
                    } else {
                        console.log("✅ Master server initialized.");
                    }
                });
            });
        }
    });

    db.run(createUsersTable, (err) => {
        if (err) {
            console.error("❌ Error creating users table:", err.message);
        } else {
            console.log("✅ Users table is ready.");
        }
    });
});

module.exports = db;
