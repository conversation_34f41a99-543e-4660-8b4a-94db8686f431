<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { userState } from '../main';
import SearchBar from './SearchBar.vue';
import InstanceTable from './InstanceTable.vue';

const instanceTable = ref(null);
const allInstances = ref([]); // Holds instances from the API
const searchKeyword = ref('');
const VITE_MASTER_URL = import.meta.env.VITE_MASTER_URL;

// Fetch instances based on login state
const fetchInstances = async () => {
  try {
    // Decide the endpoint and options based on login state
    const isAdmin = userState.isLoggedIn;
    const endpoint = isAdmin
      ? `${VITE_MASTER_URL}/applications/admin`
      : `${VITE_MASTER_URL}/applications/public`;

    const options = isAdmin && userState.user?.token
      ? {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userState.user.token}`
        }
      }
      : { method: 'GET' };

    const response = await fetch(endpoint, options);
    if (!response.ok) {
      throw new Error('Failed to fetch instances.');
    }
    const data = await response.json();

    // Process the new response format
    const processedInstances = [];

    // Process the new nested response format
    // The response is now grouped by server IP with server_name and applications array
    Object.entries(data).forEach(([serverIp, serverData]) => {
      const { server_name, maintainer, created_at, applications } = serverData;

      // Process each application and add server information
      applications.forEach(app => {
        processedInstances.push({
          ...app,
          server_name: server_name,
          maintainer: maintainer,
          server_ip: serverIp, // Ensure server_ip is set correctly
          created_at: created_at // <-- Add this line
        });
      });
    });

    console.log('Processed instances:', processedInstances);
    allInstances.value = processedInstances;
  } catch (error) {
    console.error('Error fetching instances:', error);
  }
};

// Filter instances based on search keyword (global search)
const filteredInstances = computed(() => {
  if (!searchKeyword.value) {
    return allInstances.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return allInstances.value.filter(instance => {
    // Search in all text fields
    return Object.entries(instance).some(([key, value]) => {
      // Skip arrays and null values
      if (value === null || Array.isArray(value)) return false;

      return String(value).toLowerCase().includes(keyword);
    }) ||
      // Search in arrays (tags, databases, credentials)
      (instance.tags && instance.tags.some(tag => tag.toLowerCase().includes(keyword))) ||
      (instance.databases && instance.databases.some(db => db.toLowerCase().includes(keyword))) ||
      (instance.credentials && instance.credentials.some(cred => cred.toLowerCase().includes(keyword)));
  });
});

// Initial fetch when component mounts
onMounted(() => {
  fetchInstances();
});

// Re-fetch instances when login state changes
watch(
  () => userState.isLoggedIn,
  (newValue) => {
    fetchInstances();
    // Clear refresh status when logging out
    if (!newValue && instanceTable.value) {
      instanceTable.value.refreshStatus.clear();
    }
  }
);

// Add this function to handle server name and maintainer updates
const handleServerNameUpdate = async ({ serverIp, newName, newMaintainer }) => {
  try {
    // Only send changed fields
    const updateBody = {};
    const serverInstance = allInstances.value.find(instance => instance.server_ip === serverIp);
    if (!serverInstance) {
      throw new Error('Server not found');
    }
    const serverId = serverInstance.server_id;
    if (!serverId) {
      throw new Error('Server ID not found');
    }
    if (newName && newName !== serverInstance.server_name) {
      updateBody.server_name = newName;
    }
    if (newMaintainer && newMaintainer !== serverInstance.maintainer) {
      updateBody.maintainer = newMaintainer;
    }
    if (Object.keys(updateBody).length === 0) {
      // Nothing to update
      return;
    }
    const response = await fetch(`${VITE_MASTER_URL}/servers/${serverId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userState.user.token}`
      },
      body: JSON.stringify(updateBody)
    });
    const responseData = await response.json();
    console.log('API Response:', responseData);
    if (!response.ok) {
      throw new Error(`Failed to update server: ${responseData.message || response.statusText}`);
    }
    // Update the local state
    allInstances.value = allInstances.value.map(instance => {
      if (instance.server_ip === serverIp) {
        return {
          ...instance,
          ...(updateBody.server_name ? { server_name: newName } : {}),
          ...(updateBody.maintainer ? { maintainer: newMaintainer } : {})
        };
      }
      return instance;
    });
    // Refetch the instances to ensure we have the latest data
    await fetchInstances();
  } catch (error) {
    console.error('Error updating server:', error);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', await error.response.json());
    }
    throw error;
  }
};

// Add this function to handle instance updates
const handleInstanceUpdate = async (editedInstance) => {
  try {
    console.log('Sending update request for instance:', editedInstance);

    // Prepare updateData with only allowed fields and correct format
    const updateData = {};
    if (editedInstance.instance_title !== undefined) updateData.name = editedInstance.instance_title?.trim() || '';
    if (editedInstance.instance_owner !== undefined) updateData.owner = editedInstance.instance_owner?.trim() || '';
    if (editedInstance.description !== undefined) updateData.purpose = editedInstance.description?.trim() || '';
    if (editedInstance.status !== undefined) updateData.status = editedInstance.status;
    if (editedInstance.expected_deletion_date) {
      // Format date as yyyy-mm-dd
      let dateStr = editedInstance.expected_deletion_date;
      if (typeof dateStr === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        updateData.expected_deletion_date = dateStr;
      } else {
        const d = new Date(dateStr);
        if (!isNaN(d)) {
          updateData.expected_deletion_date = d.toISOString().slice(0, 10);
        }
      }
    }
    if (editedInstance.external_url !== undefined) updateData.external_url = editedInstance.external_url?.trim() || '';
    if (editedInstance.is_public !== undefined) updateData.public = editedInstance.is_public === 1 ? 1 : 0;

    console.log('Update data being sent:', updateData);

    const response = await fetch(`${VITE_MASTER_URL}/applications/${editedInstance.id}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userState.user.token}`
        },
        body: JSON.stringify(updateData)
      }
    );

    const responseData = await response.json();
    console.log('API Response:', responseData);

    if (!response.ok) {
      throw new Error(responseData.message || 'Failed to update instance');
    }

    // Update local state immediately
    allInstances.value = allInstances.value.map(instance => {
      if (instance.id === editedInstance.id) {
        return Object.assign({}, instance, updateData);
      }
      return instance;
    });

    // Then fetch fresh data from server
    await fetchInstances();
    return true;

  } catch (error) {
    console.error('Error updating instance:', error);
    if (error.response) {
      console.error('Response status:', error.response.status);
      const errorData = await error.response.json();
      console.error('Response data:', errorData);
    }
    return false;
  }
};

// Computed property for admin status
const isAdmin = computed(() => userState.isLoggedIn);
</script>
<template>
  <div class="dashboard-container">
    <SearchBar 
      :instance-table-ref="instanceTable" 
      v-model="searchKeyword"
      :is-admin="isAdmin"
      @refresh-complete="fetchInstances"
    />
    <InstanceTable 
      ref="instanceTable" 
      :instances="filteredInstances" 
      :is-admin="isAdmin"
      @update-server-name="handleServerNameUpdate"
      @update-instance="handleInstanceUpdate"
    />
  </div>
</template>

<style scoped>
.dashboard-container {
  width: 100%;
  height: 100%;
}
</style>



















