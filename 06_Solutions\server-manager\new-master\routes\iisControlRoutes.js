const express = require('express');
const router = express.Router();
const { getIISStatus, controlIIS } = require('../controllers/iisController');
// const authenticateAdmin = require('../middleware/authenticateAdmin');
const validateIPAddress = require('../middleware/validateIPAddress');

/**
 * @route   GET /iis-status
 * @desc    Get IIS service status for a server
 * @access  Private (Admin only)
 */
router.get('/status', 
    // authenticateAdmin, 
    getIISStatus);

/**
 * @route   POST /iis-control
 * @desc    Control IIS service (start/stop/restart)
 * @access  Private (Admin only)
 */
router.post('/control', 
    // authenticateAdmin, 
    validateIPAddress,
    controlIIS);

module.exports = router;