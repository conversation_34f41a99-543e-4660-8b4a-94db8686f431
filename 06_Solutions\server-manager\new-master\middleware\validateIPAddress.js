/**
 * Middleware to validate IP address format
 * Checks if IP addresses in request parameters, query or body are valid IPv4 addresses
 */
const validateIPAddress = (req, res, next) => {
    // Function to validate IPv4 format
    const isValidIPv4 = (ip) => {
        // Basic IPv4 format validation
        const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
        const match = ip.match(ipRegex);

        if (!match) return false;

        // Check each octet is between 0 and 255
        for (let i = 1; i <= 4; i++) {
            const octet = parseInt(match[i]);
            if (octet < 0 || octet > 255) return false;
        }

        return true;
    };

    // Define locations to check for IP addresses
    const ipLocations = [
        { source: req.query, field: 'server_ip', location: 'query' },
        { source: req.body, field: 'server_ip', location: 'request body' },
        { source: req.params, field: 'ip', location: 'URL' },
        { source: req.body, field: 'ip', location: 'request body' }
    ];

    // Check each location for IP addresses
    for (const { source, field, location } of ipLocations) {
        if (source && source[field] && !isValidIPv4(source[field])) {
            return res.status(400).json({
                success: false,
                error: `Invalid IP address format in ${location}. Must be between 0.0.0.0 and ***************`
            });
        }
    }

    // If all checks pass, proceed to the next middleware/route handler
    next();
};

module.exports = validateIPAddress;
