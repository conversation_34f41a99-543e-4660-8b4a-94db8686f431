const express = require('express');
const router = express.Router();
const {
    getAllArasCredentials,
    getArasCredentialById,
    createArasCredential,
    updateArasCredential,
    deleteArasCredential
} = require('../controllers/arasCredentialController');
// const authenticateAdmin = require('../middleware/authenticateAdmin');

/**
 * @route   GET /aras-credentials
 * @desc    Get all Aras credentials
 * @access  Private (Admin only)
 */
router.get('/',
    // authenticateAdmin,
    getAllArasCredentials);

/**
 * @route   GET /aras-credentials/:id
 * @desc    Get Aras credential by ID
 * @access  Private (Admin only)
 */
router.get('/:id',
    // authenticateAdmin,
    getArasCredentialById);

/**
 * @route   POST /aras-credentials
 * @desc    Create a new Aras credential
 * @access  Private (Admin only)
 */
router.post('/',
    // authenticateAdmin,
    createArasCredential);

/**
 * @route   PUT /aras-credentials/:id
 * @desc    Update an existing Aras credential
 * @access  Private (Admin only)
 */
router.put('/:id',
    // authenticateAdmin,
    updateArasCredential);

/**
 * @route   DELETE /aras-credentials/:id
 * @desc    Delete an Aras credential
 * @access  Private (Admin only)
 */
router.delete('/:id',
    // authenticateAdmin,
    deleteArasCredential);

module.exports = router;