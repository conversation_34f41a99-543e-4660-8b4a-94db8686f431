const db = require('../db/db');

/**
 * Get all Aras credentials
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllArasCredentials = (req, res) => {
    const query = `
        SELECT 
            ac.id, 
            ac.application_id, 
            ac.database_id, 
            ac.username, 
            ac.password,
            ad.database_name,
            a.name as application_name
        FROM aras_credentials ac
        JOIN aras_databases ad ON ac.database_id = ad.id
        JOIN applications a ON ac.application_id = a.id
        ORDER BY ac.application_id, ac.database_id
    `;

    db.all(query, [], (err, credentials) => {
        if (err) {
            console.error('Error fetching Aras credentials:', err.message);
            return res.status(500).json({ 
                success: false, 
                error: 'Failed to fetch Aras credentials' 
            });
        }

        res.json({
            success: true,
            count: credentials.length,
            data: credentials
        });
    });
};

/**
 * Get Aras credential by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getArasCredentialById = (req, res) => {
    const { id } = req.params;
    
    const query = `
        SELECT 
            ac.id, 
            ac.application_id, 
            ac.database_id, 
            ac.username, 
            ac.password,
            ad.database_name,
            a.name as application_name
        FROM aras_credentials ac
        JOIN aras_databases ad ON ac.database_id = ad.id
        JOIN applications a ON ac.application_id = a.id
        WHERE ac.id = ?
    `;
    
    db.get(query, [id], (err, credential) => {
        if (err) {
            console.error('Error fetching Aras credential:', err.message);
            return res.status(500).json({ 
                success: false, 
                error: 'Failed to fetch Aras credential' 
            });
        }
        
        if (!credential) {
            return res.status(404).json({
                success: false,
                error: 'Aras credential not found'
            });
        }

        res.json({
            success: true,
            data: credential
        });
    });
};

/**
 * Create a new Aras credential
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createArasCredential = (req, res) => {
    const { application_id, database_id, username, password } = req.body;

    // Validate required fields
    if (!application_id || !database_id || !username || !password) {
        return res.status(400).json({
            success: false,
            error: 'Application ID, database ID, username, and password are required'
        });
    }

    // Verify that the application and database exist
    const verifyQuery = `
        SELECT 
            a.id as app_exists, 
            d.id as db_exists,
            d.application_id as db_app_id
        FROM applications a
        LEFT JOIN aras_databases d ON d.id = ?
        WHERE a.id = ?
    `;

    db.get(verifyQuery, [database_id, application_id], (err, result) => {
        if (err) {
            console.error('Error verifying application and database:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to verify application and database'
            });
        }

        if (!result || !result.app_exists) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }

        if (!result.db_exists) {
            return res.status(404).json({
                success: false,
                error: 'Database not found'
            });
        }

        if (result.db_app_id !== application_id) {
            return res.status(400).json({
                success: false,
                error: 'The database does not belong to the specified application'
            });
        }

        // Check for duplicate credentials for the same application, database, username, and password
        const duplicateCheckQuery = `
            SELECT 1 FROM aras_credentials 
            WHERE application_id = ? AND database_id = ? AND username = ? AND password = ?
            LIMIT 1
        `;
        db.get(duplicateCheckQuery, [application_id, database_id, username, password], (err, duplicate) => {
            if (err) {
                console.error('Error checking for duplicate credentials:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to check for duplicate credentials'
                });
            }
            if (duplicate) {
                return res.status(400).json({
                    success: false,
                    error: 'Credentials with the same username and password already exist for this application and database.'
                });
            }

            // Insert the new credential
            const insertQuery = `
                INSERT INTO aras_credentials (application_id, database_id, username, password)
                VALUES (?, ?, ?, ?)
            `;
            db.run(insertQuery, [application_id, database_id, username, password], function (err) {
                if (err) {
                    console.error('Error creating Aras credential:', err.message);
                    return res.status(500).json({
                        success: false,
                        error: 'Failed to create Aras credential'
                    });
                }

                // Get the newly created credential
                db.get(
                    `SELECT
                        ac.id,
                        ac.application_id,
                        ac.database_id,
                        ac.username,
                        ac.password,
                        ad.database_name,
                        a.path as application_name
                    FROM aras_credentials ac
                    JOIN aras_databases ad ON ac.database_id = ad.id
                    JOIN applications a ON ac.application_id = a.id
                    WHERE ac.id = ?`,
                    [this.lastID],
                    (err, credential) => {
                        if (err) {
                            console.error('Error fetching created credential:', err.message);
                            return res.status(500).json({
                                success: false,
                                error: 'Credential created but failed to retrieve it'
                            });
                        }

                        res.status(201).json({
                            success: true,
                            message: 'Aras credential created successfully',
                            data: credential
                        });
                    }
                );
            });
        });
    });
};

/**
 * Update an existing Aras credential
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateArasCredential = (req, res) => {
    const { id } = req.params;
    const { username, password } = req.body;

    // Validate that at least one field is provided
    if (!username && !password) {
        return res.status(400).json({
            success: false,
            error: 'At least one field (username or password) must be provided'
        });
    }

    // First check if the credential exists
    db.get('SELECT id FROM aras_credentials WHERE id = ?', [id], (err, credential) => {
        if (err) {
            console.error('Error checking credential existence:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to update Aras credential'
            });
        }

        if (!credential) {
            return res.status(404).json({
                success: false,
                error: 'Aras credential not found'
            });
        }

        // Build the update query
        const updateFields = [];
        const params = [];

        if (username) {
            updateFields.push('username = ?');
            params.push(username);
        }

        if (password) {
            updateFields.push('password = ?');
            params.push(password);
        }

        // Add id as the last parameter
        params.push(id);

        const query = `UPDATE aras_credentials SET ${updateFields.join(', ')} WHERE id = ?`;

        db.run(query, params, function(err) {
            if (err) {
                console.error('Error updating Aras credential:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to update Aras credential'
                });
            }

            // If no rows were affected but no error occurred
            if (this.changes === 0) {
                return res.json({
                    success: true,
                    message: 'No changes were made to the Aras credential',
                    id: parseInt(id)
                });
            }

            // Get the updated credential
            db.get(
                `SELECT 
                    ac.id, 
                    ac.application_id, 
                    ac.database_id, 
                    ac.username, 
                    ac.password,
                    ad.database_name,
                    a.name as application_name
                FROM aras_credentials ac
                JOIN aras_databases ad ON ac.database_id = ad.id
                JOIN applications a ON ac.application_id = a.id
                WHERE ac.id = ?`, 
                [id], 
                (err, updatedCredential) => {
                    if (err) {
                        console.error('Error fetching updated credential:', err.message);
                        return res.status(500).json({
                            success: false,
                            error: 'Credential updated but failed to retrieve it'
                        });
                    }

                    res.json({
                        success: true,
                        message: 'Aras credential updated successfully',
                        data: updatedCredential
                    });
                }
            );
        });
    });
};

/**
 * Delete an Aras credential
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteArasCredential = (req, res) => {
    const { id } = req.params;

    // First check if the credential exists
    db.get('SELECT id FROM aras_credentials WHERE id = ?', [id], (err, credential) => {
        if (err) {
            console.error('Error checking credential existence:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to delete Aras credential'
            });
        }

        if (!credential) {
            return res.status(404).json({
                success: false,
                error: 'Aras credential not found'
            });
        }

        // Delete the credential
        const query = `DELETE FROM aras_credentials WHERE id = ?`;

        db.run(query, [id], function(err) {
            if (err) {
                console.error('Error deleting Aras credential:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to delete Aras credential'
                });
            }

            res.json({
                success: true,
                message: 'Aras credential deleted successfully',
                id: parseInt(id)
            });
        });
    });
};

module.exports = {
    getAllArasCredentials,
    getArasCredentialById,
    createArasCredential,
    updateArasCredential,
    deleteArasCredential
};