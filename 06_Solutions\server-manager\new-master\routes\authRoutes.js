const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const {
    initiateLogin,
    completeLogin
} = require('../controllers/authController.js');
const validateRequest = require('../middleware/validateRequest');

// Step 1: Client requests a challenge
router.post('/login-init', [
    body('identifier')
        .notEmpty()
        .withMessage('Username or email is required')
],
    validateRequest,
    initiateLogin);

// Step 2: Client sends response
router.post('/login', completeLogin);

module.exports = router;
