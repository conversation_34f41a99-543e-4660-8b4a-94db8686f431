const express = require('express');
const router = express.Router();
const {
    getAllServers,
    getServerById,
    createServer,
    updateServer,
    deleteServer
} = require('../controllers/serverController');
// const authenticateAdmin = require('../middleware/authenticateAdmin');
const validateIPAddress = require('../middleware/validateIPAddress');

/**
 * @route   GET /servers
 * @desc    Get all servers
 * @access  Public
 */
router.get('/', getAllServers);

/**
 * @route   GET /servers/:id
 * @desc    Get server by ID
 * @access  Public
 */
router.get('/:id', validateIPAddress, getServerById);

/**
 * @route   POST /servers
 * @desc    Create a new server
 * @access  Private (Admin only)
 */
router.post('/',
    // authenticateAdmin, 
    validateIPAddress,
    createServer);

/**
 * @route   PUT /servers/:id
 * @desc    Update an existing server
 * @access  Private (Admin only)
 */
router.put('/:id',
    // authenticateAdmin, 
    validateIPAddress,
    updateServer);

/**
 * @route   DELETE /servers/:id
 * @desc    Delete a server
 * @access  Private (Admin only)
 */
router.delete('/:id',
    // authenticateAdmin, 
    validateIPAddress,
    deleteServer);

module.exports = router;
