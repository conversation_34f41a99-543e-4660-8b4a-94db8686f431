const db = require('../db');

function initializeHealthTables() {
    return new Promise((resolve, reject) => {
        db.run(`
            CREATE TABLE IF NOT EXISTS health_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                server_ip TEXT,
                date DATE NOT NULL,
                time TIME NOT NULL,
                cpu_usage REAL,
                memory_used INTEGER,
                memory_total INTEGER,
                memory_usage REAL,
                disk_used INTEGER,
                disk_total INTEGER,
                disk_usage REAL,
                network_download REAL,
                network_upload REAL,
                gpu_data TEXT,
                server_type TEXT NOT NULL
            )
        `, (err) => {
            if (err) {
                console.error('Error creating health table:', err.message);
                reject(err);
                return;
            }
            console.log('Health table created or already exists');
            resolve();
        });
    });
}

function getAllHealthHistory() {
    return new Promise((resolve, reject) => {
        // Get all health data and group by server_ip
        db.all('SELECT * FROM health_data', [], (err, rows) => {
            if (err) {
                reject(err);
                return;
            }

            // Group data by server_ip
            const groupedData = rows.reduce((acc, row) => {
                // Remove server_type field from the row
                const { server_type, ...rowWithoutType } = row;

                // Initialize array for server_ip if it doesn't exist
                if (!acc[row.server_ip]) {
                    acc[row.server_ip] = [];
                }

                // Add the row to the corresponding server_ip array
                acc[row.server_ip].push(rowWithoutType);

                return acc;
            }, {});

            resolve(groupedData);
        });
    });
}

function cleanupOldHealthData(retentionDays = 30) {
    return new Promise((resolve, reject) => {
        // First, get all unique server IPs
        db.all('SELECT DISTINCT server_ip FROM health_data', [], (err, servers) => {
            if (err) {
                console.error('❌ Error fetching server IPs:', err.message);
                return reject(err);
            }

            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
            const cutoffDateStr = cutoffDate.toISOString().split('T')[0];

            // Process each server's data
            const cleanupPromises = servers.map(server => {
                return new Promise((resolveServer, rejectServer) => {
                    db.run(`
                        DELETE FROM health_data 
                        WHERE server_ip = ? 
                        AND date < ?
                    `, [server.server_ip, cutoffDateStr], function (err) {
                        if (err) {
                            console.error(`❌ Error cleaning up data for server ${server.server_ip}:`, err.message);
                            return rejectServer(err);
                        }

                        const deletedCount = this.changes;
                        console.log(`✅ Server ${server.server_ip}: Removed ${deletedCount} records older than ${cutoffDateStr}`);
                        resolveServer({
                            server_ip: server.server_ip,
                            recordsRemoved: deletedCount
                        });
                    });
                });
            });

            Promise.all(cleanupPromises)
                .then(results => {
                    const totalRemoved = results.reduce((sum, result) => sum + result.recordsRemoved, 0);
                    console.log(`✅ Total records removed: ${totalRemoved}`);
                    resolve(results);
                })
                .catch(error => {
                    console.error('❌ Error during cleanup:', error);
                    reject(error);
                });
        });
    });
}

module.exports = {
    initializeHealthTables,
    getAllHealthHistory,
    cleanupOldHealthData
};



