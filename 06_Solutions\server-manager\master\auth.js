// auth.js
const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const db = require('./db');
const crypto = require('crypto');
const sessions = new Map(); // Store challenges temporarily

const router = express.Router();

// Step 1: Client requests a challenge
router.post('/login-init', [
    body('identifier')
        .notEmpty()
        .withMessage('Username or email is required')
], (req, res) => {
    const { identifier } = req.body;
    
    // Generate random challenge
    const challenge = crypto.randomBytes(32).toString('hex');
    
    // Get user's email from database
    db.get(
        'SELECT email FROM users WHERE username = ? OR email = ?',
        [identifier, identifier],
        (err, user) => {
            if (err || !user) {
                return res.status(401).json({ error: 'Invalid credentials' });
            }

            // Store challenge with timestamp
            sessions.set(identifier, {
                challenge,
                timestamp: Date.now()
            });
            
            // Send both challenge and email
            res.json({ 
                challenge,
                email: user.email 
            });
        }
    );
});

// Step 2: Client sends response
router.post('/login', async (req, res) => {
    const { identifier, response } = req.body;
    
    const session = sessions.get(identifier);
    if (!session) {
        return res.status(401).json({ error: 'Invalid session' });
    }

    // Delete challenge immediately to prevent reuse
    sessions.delete(identifier);

    // Get user from database by username OR email
    db.get(
        'SELECT * FROM users WHERE username = ? OR email = ?', 
        [identifier, identifier], 
        async (err, user) => {
            if (err || !user) {
                return res.status(401).json({ error: 'Invalid credentials' });
            }

            // Always use email for hash verification
            const expectedResponse = calculateResponse(user.password, session.challenge);
            
            if (response === expectedResponse) {
                const token = jwt.sign(
                    { 
                        id: user.id, 
                        username: user.username,
                        email: user.email,
                        role: user.role  // Make sure role is included in the token
                    },
                    process.env.JWT_SECRET,
                    { expiresIn: '1h' }
                );
                res.json({ token });
            } else {
                res.status(401).json({ error: 'Invalid credentials' });
            }
        }
    );
});

function calculateResponse(storedHash, challenge) {
    return crypto
        .createHmac('sha256', storedHash)
        .update(challenge)
        .digest('hex');
}

// Add this new endpoint to get username from identifier
router.post('/get-username', async (req, res) => {
    const { identifier } = req.body;
    
    db.get(
        'SELECT username FROM users WHERE username = ? OR email = ?',
        [identifier, identifier],
        (err, user) => {
            if (err || !user) {
                return res.status(404).json({ error: 'User not found' });
            }
            res.json({ username: user.username });
        }
    );
});

module.exports = router;
