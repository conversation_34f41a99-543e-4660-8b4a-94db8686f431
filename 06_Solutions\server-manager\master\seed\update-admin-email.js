const db = require('../db');

console.log('🔄 Starting to update admin email...');

const updateQuery = `UPDATE users SET email = ? WHERE username = ?`;

db.run(updateQuery, ['<EMAIL>', 'admin'], function(err) {
    if (err) {
        console.error('❌ Error updating admin email:', err.message);
        process.exit(1);
    }
    
    console.log(`✅ Successfully updated ${this.changes} user's email`);
    
    // Verify the update
    db.get('SELECT username, email, role FROM users WHERE username = ?', ['admin'], (err, row) => {
        if (err) {
            console.error('❌ Error verifying update:', err.message);
            process.exit(1);
        }
        
        if (row && row.email === '<EMAIL>') {
            console.log('✅ Verification successful');
            console.log('📝 Current admin data:', row);
        } else {
            console.error('❌ Verification failed - Update was not successful');
        }
        
        // Close the database connection
        db.close(() => {
            console.log('📡 Database connection closed.');
            process.exit(0);
        });
    });
});