<script setup>
import { ref, inject, getCurrentInstance } from 'vue';
import { userState, APP_VERSION } from '../main';
import CryptoJS from 'crypto-js';

const VITE_MASTER_URL = import.meta.env.VITE_MASTER_URL;
const app = getCurrentInstance();
const alert = inject('alert') || app?.appContext.config.globalProperties.$alert;
const isVisible = ref(false);
const version = APP_VERSION;

const identifier = ref('');
const password = ref('');
const loginFormRef = ref(null); // Reference to the form element

const hashPassword = (plainPassword, identifier) => {
  // Use identifier (email or username) directly for salt
  const salt = CryptoJS.SHA256(identifier).toString();
  let hashedPassword = plainPassword;
  for (let i = 0; i < 1000; i++) {
    hashedPassword = CryptoJS.SHA256(hashedPassword + salt).toString();
  }
  return hashedPassword;
};

const toggleDrawer = () => {
  if (!userState.isLoggedIn) {
    isVisible.value = !isVisible.value;
  }
};

const handleSignIn = async () => {
  if (!identifier.value || !password.value) {
    alert?.show('Please enter both details.', 'error', 3000);
    return;
  }

  try {
    // Step 1: Get challenge and user email
    const initResponse = await fetch(`${VITE_MASTER_URL}/auth/login-init`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ identifier: identifier.value })
    });

    if (!initResponse.ok) {
      throw new Error('Failed to initialize login');
    }

    const { challenge, email } = await initResponse.json();

    // Always use email for password hashing
    const passwordHash = hashPassword(password.value, email);

    // Calculate challenge response
    const response = CryptoJS.HmacSHA256(challenge, passwordHash).toString();

    // Step 2: Send response
    const loginResponse = await fetch(`${VITE_MASTER_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        identifier: identifier.value,
        response: response
      })
    });

    if (!loginResponse.ok) {
      throw new Error('Invalid credentials');
    }

    const data = await loginResponse.json();

    userState.isLoggedIn = true;
    userState.user = {
      username: data.token.username,
      email: data.token.email,
      token: data.token,
      loginTime: new Date().toISOString()
    };

    isVisible.value = false;
    alert?.show('Access granted.', 'success', 3000);
  } catch (error) {
    console.error('Login error:', error);
    alert?.show('Login error. Please try again.', 'error', 3000);
  }
};

defineExpose({ toggleDrawer });
</script>

<template>
  <div class="login-container">
    <div class="login-drawer" :class="{ 'drawer-open': isVisible }">
      <div class="drawer-header">
        <h2>Login</h2>
        <button class="close-btn" @click="toggleDrawer">
          <i class="pi pi-times"></i>
        </button>
      </div>
      <div class="drawer-content">
        <!-- <div class="branding">
          <img src="/servertap-logo.svg" alt="ServerTap" class="servertap-logo" />
        </div> -->
        <form ref="loginFormRef" class="login-form" @submit.prevent="handleSignIn">
          <div class="form-group">
            <label for="identifier">Username or Email</label>
            <input type="text" id="identifier" v-model="identifier" placeholder="Enter your username or email"
              class="form-input" />
          </div>
          <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" v-model="password" placeholder="Enter your password"
              class="form-input" />
          </div>
          <button type="submit" class="submit-btn">
            Sign In
          </button>
        </form>
      </div>
      <div class="drawer-footer">
        <div class="footer-content">
          <p class="version">{{ version }}</p>
          <div class="company-brand">
            <span>Powered by</span>
            <img src="/steepgraph-logo.svg" alt="Steepgraph Systems" class="steepgraph-logo" />
          </div>
          <p class="copyright">© {{ new Date().getFullYear() }} Steepgraph Systems Pvt. Ltd.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.login-drawer {
  position: fixed;
  top: 0;
  right: -350px;
  /* Reduced from -400px */
  width: 300px;
  /* Reduced from 350px */
  height: 100vh;
  background-color: white;
  z-index: 1001;
  transition: right 0.3s ease;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
}

.drawer-open {
  right: 0;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  /* Reduced from 1.5rem */
  border-bottom: 1px solid #dddae4;
}

.drawer-header h2 {
  color: #211761;
  margin: 0;
  font-size: 1rem;
  /* Reduced from default h2 size */
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #211761;
  cursor: pointer;
  font-size: 1rem;
  /* Reduced from 1.2rem */
  padding: 0.4rem;
  /* Reduced from 0.5rem */
  transition: color 0.2s;
  border-radius: 3rem;
  /* Added border-radius to match other components */
  width: 28px;
  /* Added fixed width for consistent circular shape */
  height: 28px;
  /* Added fixed height for consistent circular shape */
  display: flex;
  /* Added for better icon centering */
  align-items: center;
  /* Added for better icon centering */
  justify-content: center;
  /* Added for better icon centering */
}

.close-btn:hover {
  color: #5237cc;
  background-color: rgba(82, 55, 204, 0.05);
  /* Added hover background effect */
}

.drawer-content {
  padding: 1.5rem;
  /* Reduced from 2rem */
  padding-bottom: 3rem;
  /* Reduced from 4rem */
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  /* Reduced from 1.5rem */
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  /* Reduced from 0.5rem */
}

.form-group label {
  color: #211761;
  font-weight: 500;
  font-size: 0.875rem;
  /* Added 14px size */
}

.form-input {
  padding: 0.5rem 0.75rem;
  /* Reduced from 0.75rem */
  border: 2px solid #211761;
  border-radius: 3rem;
  /* Changed from 0.4rem to 3rem to match other components */
  font-family: 'Montserrat', sans-serif;
  font-size: 0.875rem;
  /* Added 14px size */
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #5237cc;
}

.form-input::placeholder {
  color: #666;
  font-size: 0.875rem;
  /* Added 14px size */
}

.submit-btn {
  padding: 0.5rem 0.75rem;
  /* Reduced from 0.75rem */
  background-color: #211761;
  color: white;
  border: none;
  border-radius: 3rem;
  /* Changed from 0.4rem to 3rem to match other components */
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.875rem;
  /* Added 14px size */
}

.submit-btn:hover {
  background-color: #5237cc;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.75rem;
  text-align: center;
  border-top: 1px solid #dddae4;
  background-color: white;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
    gap: 0.5rem;
  }
  
  .company-brand {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* gap: 0.25rem; */
  }
  
  .company-brand span {
    color: var(--text-secondary);
    font-size: 0.75rem;
  }
  
  .steepgraph-logo {
    height: 24px;
    width: auto;
    opacity: 0.9;
  }
  
  .version {
    color: #666;
    font-size: 0.7rem;
    margin: 0;
    font-family: 'Courier New', monospace;
  }
  
  .copyright {
    color: var(--text-secondary);
    font-size: 0.75rem;
    margin: 0;
  }
  
  /* Responsive adjustments */
  @media (max-width: 480px) {
    .login-drawer {
      width: 280px;
      /* Slightly smaller on mobile */
    }
  
    .drawer-content {
      padding: 1rem;
    }
  
    .branding {
      margin-bottom: 1rem;
    }
  
    .servertap-logo {
      height: 24px;
      /* Even smaller on mobile */
    }
  
    .steepgraph-logo {
      height: 28px;
      /* Mobile size */
    }
  
    .steepgraph-logo-small {
      height: 18px;
      /* Mobile size */
    }
  }
</style>
