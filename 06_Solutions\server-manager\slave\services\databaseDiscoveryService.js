const axios = require('axios');
const { getInstancesFromXML } = require('./instances');
const xml2js = require('xml2js');

/**
 * Fetches database list for a given Aras application path
 * @param {string} appPath - The application path
 * @returns {Promise<Array>} - Array of database names
 */
async function getDatabasesForApp(appPath) {
    try {
        // Normalize app path to ensure it starts with a slash
        let normalizedPath = appPath.startsWith('/') ? appPath : `/${appPath}`;
        
        // For client paths, remove /client for the URL but keep original path for mapping
        const isClientPath = normalizedPath.toLowerCase().endsWith('/client');
        const urlPath = isClientPath 
            ? normalizedPath.substring(0, normalizedPath.length - 7) 
            : normalizedPath;
        
        // Construct the dblist URL - ensure "server" is lowercase
        const url = `http://localhost${urlPath}/server/dblist.aspx`;
        
        console.log(`Fetching databases from URL: ${url}`);
        
        const response = await axios.get(url, {
            timeout: 5000,
            headers: {
                'Accept': 'application/xml'
            }
        });
        
        // Parse XML response
        const parser = new xml2js.Parser({ explicitArray: false });
        const result = await parser.parseStringPromise(response.data);
        
        if (!result || !result.DBList) {
            console.log(`No DBList found for ${appPath}`);
            return [];
        }
        
        // Handle single DB or multiple DBs
        if (Array.isArray(result.DBList.DB)) {
            const databases = result.DBList.DB.map(db => db.$.id);
            console.log(`Found databases for ${appPath}:`, databases);
            return databases;
        } else if (result.DBList.DB) {
            const database = [result.DBList.DB.$.id];
            console.log(`Found database for ${appPath}:`, database);
            return database;
        }
        
        console.log(`No databases found for ${appPath}`);
        return [];
    } catch (error) {
        console.error(`Error fetching databases for ${appPath}:`, error.message);
        return [];
    }
}

/**
 * Discovers databases for all Aras instances
 * @returns {Promise<Object>} - Object with app paths and their databases
 */
async function discoverDatabases() {
    try {
        // Get all instances first
        const { instances } = await getInstancesFromXML();
        
        // Process each instance to get its databases
        const results = {};
        
        for (const instance of instances) {
            const databases = await getDatabasesForApp(instance.app_path);
            // Use the original app_path as the key in results
            results[instance.app_path] = databases;
        }
        
        return {
            success: true,
            data: results
        };
    } catch (error) {
        console.error('Database discovery failed:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

module.exports = {
    discoverDatabases,
    getDatabasesForApp
};
