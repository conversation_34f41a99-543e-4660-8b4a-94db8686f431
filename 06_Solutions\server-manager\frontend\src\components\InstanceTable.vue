<script setup>
import { ref, computed, inject, getCurrentInstance, onMounted, nextTick, watch } from 'vue';
import SortableColumnHeader from './SortableColumnHeader.vue';
import EditInstanceDialog from './EditInstanceDialog.vue';
import EditServerNameDialog from './EditServerNameDialog.vue';

const app = getCurrentInstance();
const alert = inject('alert') || app?.appContext.config.globalProperties.$alert;

const props = defineProps({
  instances: {
    type: Array,
    default: () => []
  },
  isAdmin: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['updateServerName', 'updateInstance']);

// Use instances from props
const instances = computed(() => props.instances);

// Add a property to store all instances
const allInstances = ref([]);

// When loading instances, store them in both places
const loadInstances = (data) => {
  allInstances.value = [...data];
  instances.value = [...data];
};

// Sorting state
const currentSort = ref({
  key: null,
  direction: 'asc',
  serverIp: null
});

// Group instances by server IP
const groupedByIP = computed(() => {
  // Group by server_ip and attach server-level info
  return instances.value.reduce((acc, instance) => {
    if (!acc[instance.server_ip]) {
      acc[instance.server_ip] = {
        server_name: instance.server_name,
        maintainer: instance.maintainer,
        created_at: instance.created_at,
        instances: []
      };
    }
    acc[instance.server_ip].instances.push(instance);
    return acc;
  }, {});
});

// Sort groups based on current sort state
const sortedGroups = computed(() => {
  const result = {};
  for (const [ip, group] of Object.entries(groupedByIP.value)) {
    const groupInstances = group.instances;
    if (ip === currentSort.value.serverIp && currentSort.value.key) {
      result[ip] = {
        ...group,
        instances: [...groupInstances].sort((a, b) => {
          // For "public", force numeric conversion
          const key = currentSort.value.key;

          // Handle array fields specially
          if (['tags', 'databases', 'credentials'].includes(key)) {
            const aVal = a[key] && a[key].length > 0 ? a[key].join(',') : '';
            const bVal = b[key] && b[key].length > 0 ? b[key].join(',') : '';
            const comparison = aVal.toString().localeCompare(bVal.toString(), undefined, {
              numeric: true,
              sensitivity: 'base'
            });
            return currentSort.value.direction === 'asc' ? comparison : -comparison;
          }

          const aVal = key === 'public' ? Number(a[key]) : (a[key] || '');
          const bVal = key === 'public' ? Number(b[key]) : (b[key] || '');

          if (key === 'public') {
            return currentSort.value.direction === 'asc' ? aVal - bVal : bVal - aVal;
          }

          const comparison = aVal.toString().localeCompare(bVal.toString(), undefined, {
            numeric: true,
            sensitivity: 'base'
          });
          return currentSort.value.direction === 'asc' ? comparison : -comparison;
        })
      };
    } else {
      result[ip] = {
        ...group,
        instances: [...groupInstances]
      };
    }
  }
  return result;
});

// Expanded sections state for each server IP
const expandedSections = ref({});

// Initialize expanded sections when instances change
watch(() => groupedByIP.value, (newGroups) => {
  // Add any new server IPs to expandedSections
  Object.keys(newGroups).forEach(ip => {
    if (expandedSections.value[ip] === undefined) {
      expandedSections.value[ip] = false;
    }
  });
}, { immediate: true });

const toggleSection = (serverIp) => {
  expandedSections.value[serverIp] = !expandedSections.value[serverIp];
};

const handleSort = (key, serverIp) => {
  if (currentSort.value.key === key && currentSort.value.serverIp === serverIp) {
    currentSort.value.direction = currentSort.value.direction === 'asc' ? 'desc' : 'asc';
  } else {
    currentSort.value.key = key;
    currentSort.value.direction = 'asc';
    currentSort.value.serverIp = serverIp;
  }
};

// Selection state for servers
const selectedServers = ref(new Set());
const toggleServerSelection = (serverIp) => {
  if (selectedServers.value.has(serverIp)) {
    selectedServers.value.delete(serverIp);
  } else {
    selectedServers.value.add(serverIp);
  }
};

const refreshStatus = ref(new Map()); // Tracks refresh status for each server

// Add method to update refresh status
const updateRefreshStatus = (serverIp, status, error = null) => {
  const newMap = new Map(refreshStatus.value);
  newMap.set(serverIp, { status, error, timestamp: Date.now() });
  refreshStatus.value = newMap;
};

// Dialog state
const editDialog = ref(null);
const serverNameEditDialog = ref(null);
const currentEditInstance = ref(null);
const currentEditServerIp = ref(null);

// Handle edit button click
const handleEdit = (instance) => {
  currentEditInstance.value = { ...instance };
  editDialog.value.toggleDialog();
};

// Handle server name edit button click
const handleServerNameEdit = (serverIp) => {
  currentEditServerIp.value = serverIp;
  serverNameEditDialog.value.toggleDialog();
};

const handleSaveEdit = async (editedInstance) => {
  try {
    console.log("Attempting to update instance:", editedInstance);
    const result = await emit('updateInstance', editedInstance);
    // Only close dialog and show success if updateInstance returns true
    if (result && (result.then ? await result : result)) {
      editDialog.value.toggleDialog();
      alert?.show('Instance updated successfully!', 'success', 3000);
    }
  } catch (error) {
    console.error('Failed to update instance:', error);
    alert?.show('Failed to update instance. Please try again.', 'error', 3000);
  }
};

// Add validation error handler
const handleValidationError = (errorMessage) => {
  alert?.show(errorMessage, 'error', 5000);
};

const handleServerNameSave = async (newName) => {
  try {
    // Don't close dialog until update is successful
    await emit('updateServerName', {
      serverIp: currentEditServerIp.value,
      newName: newName
    });
    // After successful update, close dialog and show success message
    serverNameEditDialog.value.toggleDialog();
    alert?.show('Server name updated successfully!', 'success', 3000);
  } catch (error) {
    console.error('Failed to update server name:', error);
    alert?.show('Failed to update server name. Please try again.', 'error', 3000);
  }
};

// Helper function for non-admin URL button
const openUrl = (url) => {
  window.open(url, '_blank');
};

// Function to calculate and set column widths
const setColumnWidths = async () => {
  await nextTick();

  // Find all server names and get the maximum width
  const serverNames = document.querySelectorAll('.server-name');
  let maxServerNameWidth = 0;
  serverNames.forEach(name => {
    maxServerNameWidth = Math.max(maxServerNameWidth, name.offsetWidth);
  });

  // Find all server IPs and get the maximum width
  const serverIPs = document.querySelectorAll('.server-ip');
  let maxServerIPWidth = 0;
  serverIPs.forEach(ip => {
    maxServerIPWidth = Math.max(maxServerIPWidth, ip.offsetWidth);
  });

  // Set the CSS variables
  document.documentElement.style.setProperty('--max-server-name-width', `${maxServerNameWidth + 20}px`);
  document.documentElement.style.setProperty('--max-server-ip-width', `${maxServerIPWidth + 20}px`);
};

// Watch for changes in instances and call setColumnWidths
watch(() => props.instances, async () => {
  await setColumnWidths();
}, { deep: true });

// Set initial widths after mount
onMounted(async () => {
  await setColumnWidths();
});

// --- Double-click handler for opening environment in new window ---
const handleRowDblClick = (instance) => {
  if (!instance || !instance.server_ip || !instance.path) return;
  // Remove leading slash from path if present
  let path = instance.path.startsWith('/') ? instance.path.slice(1) : instance.path;
  const url = `http://${instance.server_ip}/${path}`;
  window.open(url, '_blank');
};

// Add this method to the <script setup> section:
const openEnvironment = (instance) => {
  if (!instance.server_ip || !instance.path) return;
  // Remove leading slash from path if present
  let path = instance.path.startsWith('/') ? instance.path.substring(1) : instance.path;
  const url = `http://${instance.server_ip}/${path}`;
  window.open(url, '_blank');
};

// // Format created_at field to desired format
// const formatCreatedAt = (createdAt) => {
//   if (!createdAt) return '-';
//   // Parse as local date
//   const date = new Date(createdAt.replace(/-/g, '/'));
//   if (isNaN(date.getTime())) return '-';
//   const pad = (n) => n.toString().padStart(2, '0');
//   return `${pad(date.getDate())}-${pad(date.getMonth() + 1)}-${date.getFullYear().toString().slice(-2)} ${pad(date.getHours())}:${pad(date.getMinutes())}`;
// };

const formatCreatedAtDate = (createdAt) => {
  if (!createdAt) return '-';
  const date = new Date(createdAt.replace(/-/g, '/'));
  if (isNaN(date.getTime())) return '-';
  const pad = (n) => n.toString().padStart(2, '0');
  return `${pad(date.getDate())}-${pad(date.getMonth() + 1)}-${date.getFullYear().toString().slice(-2)}`;
};
const formatCreatedAtTime = (createdAt) => {
  if (!createdAt) return '';
  const date = new Date(createdAt.replace(/-/g, '/'));
  if (isNaN(date.getTime())) return '';
  const pad = (n) => n.toString().padStart(2, '0');
  return ` ${pad(date.getHours())}:${pad(date.getMinutes())}`;
};

defineExpose({
  instances,
  allInstances,
  selectedServers,
  refreshStatus,
  updateRefreshStatus
});
</script>

<template>
  <div class="instance-table-container">
    <div class="table-container">
      <!-- Show message when no instances are available -->
      <div v-if="!instances.length" class="no-instances-message">
        <i class="pi pi-info-circle"></i>
        <span>No instances available{{ isAdmin ? '' : ' for public view' }}</span>
      </div>

      <!-- Show message when search returns no results -->
      <div v-else-if="instances.length > 0 && Object.keys(sortedGroups).length === 0" class="no-instances-message">
        <i class="pi pi-search"></i>
        <span>No results found for your search</span>
      </div>

      <!-- Existing instances display -->
      <div v-else v-for="(group, serverIp) in sortedGroups" :key="serverIp" class="server-group">
        <div class="server-ip-header" @click="toggleSection(serverIp)">
          <div class="server-ip-title-wrapper">
            <input type="checkbox" :checked="selectedServers.has(serverIp)"
              @click.stop="toggleServerSelection(serverIp)" class="server-checkbox">
            <h2 class="server-ip-title">
              <i :class="['pi', expandedSections[serverIp] ? 'pi-chevron-down' : 'pi-chevron-right']"></i>
              <div class="server-info-container">
                <div class="server-name-wrapper">
                  <span class="server-name">{{ group.server_name || 'Server:' }}</span>
                </div>
                <div class="server-ip-wrapper">
                  <span class="server-ip">({{ serverIp }})</span>
                </div>
                <!-- Maintainer name with user icon -->
                <div class="maintainer-wrapper">
                  <i class="pi pi-user"></i>
                  <span class="maintainer-name">{{ group.maintainer || '-' }}</span>
                  <span class="maintainer-label">maintainer</span>
                </div>
                <!-- Created at with calendar icon -->
                <div class="created-at-wrapper">
                  <i class="pi pi-calendar"></i>
                  <span class="created-at-date">{{ formatCreatedAtDate(group.created_at) }}</span>
                  <span class="created-at-time">{{ formatCreatedAtTime(group.created_at) }}</span>
                  <span class="created-at-label">created at</span>
                </div>
                <div class="server-edit-wrapper">
                  <button v-if="isAdmin" class="edit-server-name-btn" @click.stop="handleServerNameEdit(serverIp)"
                    title="Edit server name">
                    <i class="pi pi-pencil"></i>
                  </button>
                </div>
                <div class="refresh-status-wrapper" v-if="isAdmin && refreshStatus.has(serverIp)">
                  <span class="refresh-status" :class="{
                    'success': refreshStatus.get(serverIp).status === 'success',
                    'error': refreshStatus.get(serverIp).status === 'error' || refreshStatus.get(serverIp).status === 'failed',
                    'refreshing': refreshStatus.get(serverIp).status === 'refreshing'
                  }" :title="refreshStatus.get(serverIp).error || ''">
                    <template v-if="refreshStatus.get(serverIp).status === 'refreshing'">
                      <i class="pi pi-sync rotating"></i>
                      Refreshing
                    </template>
                    <template v-else-if="refreshStatus.get(serverIp).status === 'success'">
                      <i class="pi pi-check"></i>
                      Refreshed
                    </template>
                    <template
                      v-else-if="refreshStatus.get(serverIp).status === 'error' || refreshStatus.get(serverIp).status === 'failed'">
                      <i class="pi pi-times"></i>
                      Failed
                    </template>
                  </span>
                </div>
              </div>
            </h2>
          </div>
        </div>

        <!-- Updated table with horizontal scroll -->
        <div v-show="expandedSections[serverIp]" class="table-wrapper">
          <div class="horizontal-scroll-container">
            <table class="instance-table">
              <colgroup>
                <!-- Edit button column rendered only for admin -->
                <col v-if="isAdmin" style="width: 40px">
                <!-- Conditionally render is_public column if admin -->
                <col v-if="isAdmin" style="width: 40px">
                <col style="width: 180px"> <!-- name -->
                <col style="width: 180px"> <!-- environment link -->
                <col style="width: 150px"> <!-- owner -->
                <col style="width: 200px"> <!-- purpose -->
                <col style="width: 120px"> <!-- status -->
                <col style="width: 180px"> <!-- external url -->
                <col style="width: 180px"> <!-- expected deletion date - increased from 150px -->
                <col style="width: 150px"> <!-- tags -->
                <col style="width: 120px"> <!-- version -->
                <col style="width: 150px"> <!-- build number - increased from 120px -->
                <col style="width: 180px"> <!-- databases -->
                <col style="width: 150px"> <!-- credentials -->
              </colgroup>
              <thead>
                <tr>
                  <!-- Edit button header visible only for admin -->
                  <th v-if="isAdmin" style="width: 40px; padding: 0.5rem"></th>
                  <!-- Centered is_public column header -->
                  <th v-if="isAdmin" style="width: 40px; padding: 0.5rem">
                    <div class="centered-header">
                      <SortableColumnHeader label="" sort-key="public" :current-sort="currentSort" :server-ip="serverIp"
                        @sort="(key) => handleSort(key, serverIp)" class="is-public-header" />
                    </div>
                  </th>
                  <th>
                    <SortableColumnHeader label="Name" sort-key="name" :current-sort="currentSort" :server-ip="serverIp"
                      @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                  <th>
                    <SortableColumnHeader label="Environment" sort-key="path" :current-sort="currentSort"
                      :server-ip="serverIp" @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                  <th>
                    <SortableColumnHeader label="Owner" sort-key="owner" :current-sort="currentSort"
                      :server-ip="serverIp" @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                  <th>
                    <SortableColumnHeader label="Purpose" sort-key="purpose" :current-sort="currentSort"
                      :server-ip="serverIp" @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                  <th>
                    <SortableColumnHeader label="Status" sort-key="status" :current-sort="currentSort"
                      :server-ip="serverIp" @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                  <th>
                    <SortableColumnHeader label="External URL" sort-key="external_url" :current-sort="currentSort"
                      :server-ip="serverIp" @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                  <th>
                    <SortableColumnHeader label="Deletion Date" sort-key="expected_deletion_date"
                      :current-sort="currentSort" :server-ip="serverIp" @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                  <th>
                    <SortableColumnHeader label="Tags" sort-key="tags" :current-sort="currentSort" :server-ip="serverIp"
                      @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                  <th>
                    <SortableColumnHeader label="Version" sort-key="version" :current-sort="currentSort"
                      :server-ip="serverIp" @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                  <th>
                    <SortableColumnHeader label="Build Number" sort-key="build_number" :current-sort="currentSort"
                      :server-ip="serverIp" @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                  <th>
                    <SortableColumnHeader label="Databases" sort-key="databases" :current-sort="currentSort"
                      :server-ip="serverIp" @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                  <th>
                    <SortableColumnHeader label="Credentials" sort-key="credentials" :current-sort="currentSort"
                      :server-ip="serverIp" @sort="(key) => handleSort(key, serverIp)" />
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="instance in group.instances" :key="instance.id" @dblclick="openEnvironment(instance)">
                  <!-- Edit button visible only for admin -->
                  <td v-if="isAdmin" class="edit-cell">
                    <button class="edit-btn" @click="handleEdit(instance)" title="Edit instance">
                      <i class="pi pi-pencil"></i>
                    </button>
                  </td>
                  <!-- Conditionally show is_public cell for admin -->
                  <td v-if="isAdmin" class="status-cell">
                    <span class="status-indicator" :class="{ 'is-public': Number(instance.public) === 1 }"
                      :title="Number(instance.public) === 1 ? 'Public Instance' : 'Private Instance'">
                      <i class="pi" :class="Number(instance.public) === 1 ? 'pi-globe' : 'pi-lock'"></i>
                    </span>
                  </td>
                  <td class="wrap-text">{{ instance.name || '-' }}</td>
                  <td class="wrap-text truncate-text" :title="instance.path || '-'">{{ instance.path || '-' }}</td>
                  <td class="wrap-text">{{ instance.owner || '-' }}</td>
                  <td class="wrap-text">{{ instance.purpose || '-' }}</td>
                  <td class="wrap-text">{{ instance.status || '-' }}</td>
                  <td class="wrap-text">
                    <a v-if="instance.external_url" :href="instance.external_url" target="_blank" class="instance-url">
                      {{ instance.external_url }}
                    </a>
                    <span v-else>-</span>
                  </td>
                  <td class="wrap-text">{{ instance.expected_deletion_date || '-' }}</td>
                  <td class="wrap-text">
                    <div class="tags-container">
                      <span v-for="(tag, index) in instance.tags" :key="index" class="tag"
                        style="position: relative; cursor: pointer;" @mouseenter="tag.showTooltip = true"
                        @mouseleave="tag.showTooltip = false">
                        {{ tag.name }}
                        <span v-if="tag.showTooltip" class="tag-tooltip" style="position: absolute; 
                          left: 0; 
                          top: auto; 
                          bottom: 100%; 
                          background: #e6f7ff; 
                          color: #003366; 
                          padding: 6px; 
                          border-radius: 6px; 
                          font-size: 0.75rem; 
                          z-index: 99999; 
                          box-shadow: 0 2px 8px rgba(33,23,97,0.10); 
                          height: auto; 
                          width: auto; 
                          text-align: left; 
                          pointer-events:visible; 
                          border: 1px solid #b3e0ff;">
                          {{ tag.description }}
                        </span>
                      </span>
                      <span v-if="!instance.tags || instance.tags.length === 0">-</span>
                    </div>
                  </td>
                  <td class="wrap-text">{{ instance.version || '-' }}</td>
                  <td class="wrap-text">{{ instance.build_number || '-' }}</td>
                  <td class="wrap-text">
                    <div class="database-container">
                      <span v-for="(db, index) in instance.databases" :key="index" class="database">{{ db }}</span>
                      <span v-if="!instance.databases || instance.databases.length === 0">-</span>
                    </div>
                  </td>
                  <td class="wrap-text">
                    <div class="credentials-container">
                      <span v-for="(cred, index) in instance.credentials" :key="index" class="credential">{{ cred
                      }}</span>
                      <span v-if="!instance.credentials || instance.credentials.length === 0">-</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <EditServerNameDialog ref="serverNameEditDialog" :server-ip="currentEditServerIp"
      :current-server-name="sortedGroups[currentEditServerIp]?.[0]?.server_name || ''" @save="handleServerNameSave" />

    <EditInstanceDialog ref="editDialog" :instance="currentEditInstance" @save="handleSaveEdit"
      @validation-error="handleValidationError" />
  </div>
</template>

<style scoped>
.table-container {
  width: 100%;
  padding: 0 2rem 2rem 2rem;
  background-color: transparent;
  max-width: 2000px;
  margin-top: 0;
  overflow-x: visible;
}

.server-group {
  margin-bottom: 0;
}

.server-ip-header {
  cursor: pointer;
  user-select: none;
  padding: 0.25rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
  min-height: 36px;
  display: flex;
  align-items: center;
  margin-bottom: 0rem;
}

.server-ip-header:hover {
  background-color: #5237cc0d;
}

.server-ip-title-wrapper {
  gap: 0.75rem;
  width: 100%;
}

.server-checkbox {
  width: 1rem;
  height: 1rem;
  accent-color: #5237cc;
  cursor: pointer;
  flex-shrink: 0;
}

.server-ip-title {
  cursor: pointer;
  color: #211761;
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.server-info-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.horizontal-scroll-container {
  overflow-x: auto;
  width: 100%;
}

.table-wrapper {
  transition: all 0.3s ease;
  overflow-x: auto;
  padding: 0 1rem 0.75rem 1rem;
  margin-top: 0;
}

.instance-table {
  table-layout: fixed;
  width: 100%;
  min-width: 1200px;
  border-collapse: collapse;
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: #dddae4 0px 2px 2px 0px;
  font-size: 0.875rem;
}

.instance-table th,
.instance-table td {
  padding: 0.6rem 1rem;
  text-align: left;
  vertical-align: middle;
}

.wrap-text {
  white-space: normal;
  word-break: break-word;
}

.tags-container,
.database-container,
.credentials-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag,
.database,
.credential {
  background-color: #f0f0f0;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.75rem;
  white-space: nowrap;
}

.tag {
  background-color: #e6f7ff;
  color: #0066cc;
}

.database {
  background-color: #f6ffed;
  color: #52c41a;
}

.credential {
  background-color: #fff7e6;
  color: #fa8c16;
}

.server-name-wrapper {
  flex: 0 0 auto;
  min-width: var(--max-server-name-width, 200px);
  padding-right: 0px;
}

.server-name {
  font-weight: 500;
  color: #211761;
  white-space: nowrap;
  padding-right: 0px;
}

.server-ip-wrapper {
  flex: 0 0 auto;
  min-width: var(--max-server-ip-width, 150px);
  /* Will be set dynamically */
  display: flex;
  /* justify-content: center;  */
  align-items: center;
  padding-left: 0px;
  /* Added small padding */
}

.server-ip {
  color: #666;
  white-space: nowrap;
  text-align: center;
}

.pi {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

.table-wrapper {
  transition: all 0.3s ease;
  overflow-x: auto;
  padding: 0 1rem 0.75rem 1rem;
  margin-top: 0;
}

.instance-table {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: #dddae4 0px 2px 2px 0px;
  font-size: 0.875rem;
}

.instance-table th,
.instance-table td {
  padding: 0.6rem 1rem;
  text-align: left;
  vertical-align: middle;
}

.wrap-text {
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.3;
  min-height: 2.2rem;
}

.instance-table th {
  background-color: #211761;
  color: white;
  font-weight: 500;
  user-select: none;
  padding: 0.5rem 1rem;
  text-align: center;
  /* Center header text */
  font-size: 0.875rem;
}

.instance-table td {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #dddae4;
  color: #4c475e;
}

.instance-url {
  color: #5237cc;
  text-decoration: none;
  word-break: break-all;
  font-size: 0.875rem;
}

.instance-url:hover {
  text-decoration: underline;
  color: #211761;
}

.instance-table tbody tr:hover {
  background-color: rgba(82, 55, 204, 0.05);
}

.instance-table tbody tr:last-child td {
  border-bottom: none;
}

.pi {
  transition: transform 0.2s ease;
}

.edit-cell {
  padding: 0;
  vertical-align: middle;
  text-align: center;
  height: 100%;
}

.edit-btn {
  background: none;
  border: none;
  color: #5237cc;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.edit-btn:hover {
  background-color: rgba(82, 55, 204, 0.1);
  color: #211761;
}

.edit-btn i {
  font-size: 0.75rem;
}

.status-cell {
  padding: 0;
  vertical-align: middle;
  text-align: center;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  color: #666;
  font-size: 0.8rem;
  transition: all 0.2s;
}

.status-indicator.is-public {
  color: #28a745;
}

.status-indicator:not(.is-public) {
  color: #dc3545;
}

.status-indicator i {
  font-size: 0.75rem;
}

.edit-server-name-btn {
  background: none;
  border: none;
  color: #5237cc;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  padding: 0;
}

.edit-server-name-btn:hover {
  background-color: rgba(82, 55, 204, 0.1);
}

.edit-server-name-btn i {
  font-size: 0.75rem;
}

/* Styling for the URL button in public view */
.url-btn {
  width: auto;
  height: auto;
  padding: 0.2rem 0.6rem;
  border: 0;
  border-radius: 3rem;
  background-color: #211761;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  /* display: inline-flex; */
  /* align-items: center;
                              justify-content: center; */
  transition: background-color 0.2s;
}

.url-btn:hover {
  background-color: var(--hover-bg-button);
  color: white;
}

/* Add these new styles */
.no-instances-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 1rem;
  color: #6c757d;
  font-size: 1.1rem;
  gap: 0.5rem;
}

.no-instances-message i {
  font-size: 1.2rem;
  color: #6c757d;
}

.centered-header {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.is-public-header {
  justify-content: center;
}

.server-ip-header {
  display: flex;
  align-items: center;
  width: 100%;
  /* Remove any border-related properties */
}

.server-ip-title-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  /* Remove any border-related properties */
}

.server-ip-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  padding: 0.25rem;
  /* Reduced from 0.5rem */
  cursor: pointer;
  /* Remove any border-related properties */
}

.server-info-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.server-name-wrapper {
  flex: 0 0 auto;
  min-width: var(--max-server-name-width, 200px);
  /* Will be set dynamically */
  padding-right: 0px;
  /* Added small padding */
}

.server-name {
  font-weight: 500;
  color: #211761;
  white-space: nowrap;
  padding-right: 0px;
  /* Reduced from 0.5rem */
}

.server-ip-wrapper {
  flex: 0 0 auto;
  min-width: var(--max-server-ip-width, 150px);
  /* Will be set dynamically */
  display: flex;
  /* justify-content: center;  */
  align-items: center;
  padding-left: 0px;
  /* Added small padding */
}

.server-ip {
  color: #666;
  white-space: nowrap;
  text-align: center;
}

.server-edit-wrapper {
  flex: 0 0 0px;
  display: flex;
  justify-content: center;
}

.edit-server-name-btn {
  background: none;
  border: none;
  color: #5237cc;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  padding: 0;
}

.edit-server-name-btn:hover {
  background-color: rgba(82, 55, 204, 0.1);
}

.edit-server-name-btn i {
  font-size: 0.75rem;
}

.server-checkbox {
  margin: 0;
  cursor: pointer;
  width: 16px;
  height: 16px;
}

.refresh-status-wrapper {
  margin-left: 12px;
  display: flex;
  align-items: center;
}

.refresh-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
}

.refresh-status.success {
  background-color: #4bb5431a;
  color: #4BB543;
}

.refresh-status.error {
  background-color: #ff00001a;
  color: #FF0000;
}

.refresh-status.refreshing {
  background-color: rgba(82, 55, 204, 0.1);
  color: var(--primary-light);
}

.refresh-status i {
  font-size: 0.875rem;
}

.pi-sync.rotating {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Add these new styles for horizontal scrolling */
.horizontal-scroll-container {
  overflow-x: auto;
  width: 100%;
}

/* Add styles for tags, databases, and credentials */
.tags-container,
.database-container,
.credentials-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag,
.database,
.credential {
  background-color: #f0f0f0;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.75rem;
  white-space: nowrap;
}

.tag {
  background-color: #e6f7ff;
  color: #0066cc;
}

.database {
  background-color: #f6ffed;
  color: #52c41a;
}

.credential {
  background-color: #fff7e6;
  color: #fa8c16;
}

/* Ensure table takes full width of container */
.instance-table {
  min-width: 100%;
  width: max-content;
}

/* Add these styles to ensure headers don't wrap */
.instance-table th {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Add this new style for text truncation with ellipsis */
.truncate-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
  /* Match the column width */
}

.tag-tooltip {
  animation: fadeIn 0.15s;
  /* Custom light background and dark font */
  background: #e6f7ff !important;
  color: #003366 !important;
  border: 1px solid #b3e0ff;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.maintainer-wrapper {
  background: linear-gradient(90deg, #e6e6ff 0%, #f3f0ff 100%);
  border-radius: 16px;
  padding: 2px 12px 2px 8px;
  font-size: 0.91rem;
  font-weight: 500;
  color: #5237cc;
  box-shadow: 0 1px 4px 0 rgba(82, 55, 204, 0.08);
  transition: box-shadow 0.2s;
  min-width: 90px;
  gap: 0.4rem;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.maintainer-wrapper i {
  color: #5237cc;
  font-size: 1rem;
  margin-right: 2px;
}

.maintainer-name {
  color: #3a2b8c;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.created-at-wrapper {
  background: linear-gradient(90deg, #f7f7fa 0%, #e6e6ff 100%);
  border-radius: 16px;
  padding: 2px 12px 2px 8px;
  font-size: 0.91rem;
  font-weight: 500;
  color: #6c63b5;
  box-shadow: 0 1px 4px 0 rgba(82, 55, 204, 0.07);
  min-width: 110px;
  /* gap: 0.4rem; */
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.created-at-wrapper i {
  flex: 0 0 auto;
  color: #5237cc;
  font-size: 1rem;
  margin-right: 2px;
}

.created-at-date {
  color: #5a4fb2;
  font-weight: 500;
  letter-spacing: 0.01em;
  margin-right: 2px;
}

.created-at-time {
  color: #b08cff;
  font-weight: 500;
  font-size: 0.97em;
  margin-right: 4px;
  margin-left: 2px;
  background: #f3f0ff;
  border-radius: 6px;
  padding: 1px 7px;
  letter-spacing: 0.01em;
}

.created-at {
  color: #5a4fb2;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.maintainer-label {
  font-size: 0.72em;
  color: #8d8bb3;
  margin-left: 4px;
  font-weight: 400;
  letter-spacing: 0.01em;
  align-self: flex-end;
  padding-bottom: 2px;
}

.created-at-label {
  font-size: 0.72em;
  color: #a09fd1;
  margin-left: 4px;
  font-weight: 400;
  letter-spacing: 0.01em;
  align-self: flex-end;
  padding-bottom: 2px;
}
</style>
