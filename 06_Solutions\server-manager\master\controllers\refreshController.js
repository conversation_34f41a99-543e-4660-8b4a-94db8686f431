// controllers/refreshController.js
const axios = require('axios');
const db = require('../db');
const { updateInstancesForServer } = require('../services/instanceService');

/**
 * Auto refresh controller for the cron job.
 * Fetches instance data from all servers (master and slaves).
 */
async function refreshInstances(req, res) {
    db.all('SELECT * FROM servers', async (err, servers) => {
        if (err) {
            console.error("Error retrieving servers:", err.message);
            return res.status(500).json({ error: err.message });
        }

        const successful = [];
        const failed = [];

        // Process all servers (both master and slaves) uniformly
        const serverPromises = servers.map(async (server) => {
            const url = `http://${server.server_ip}:3000/instances`;
            try {
                console.log(`Fetching instances from ${server.server_name} (${server.server_ip})`);
                const response = await axios.get(url, {
                    timeout: parseInt(process.env.SLAVE_TIMEOUT_MS, 10) || 5000
                });
                await updateInstancesForServer(server.id, response.data.instances);
                successful.push({
                    id: server.id,
                    ip: server.server_ip,
                    name: server.server_name
                });
            } catch (error) {
                failed.push({ 
                    id: server.id,
                    ip: server.server_ip,
                    name: server.server_name,
                    error: error.message
                });
            }
        });

        await Promise.all(serverPromises);
        res.json({ message: "Refresh completed", successful, failed });
    });
}

/**
 * Streaming refresh controller (SSE) for manual admin refresh.
 * Streams incremental updates from all servers (master and slaves).
 */
function refreshInstancesStream(req, res) {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    db.all('SELECT * FROM servers', (err, servers) => {
        if (err) {
            res.write(`data: ${JSON.stringify({ error: err.message })}\n\n`);
            return res.end();
        }

        const refreshPromises = servers.map((server) => {
            const url = `http://${server.server_ip}:3000/instances`;
            return axios.get(url, {
                timeout: parseInt(process.env.MANUAL_TIMEOUT_MS, 10) || 10000
            })
                .then(async response => {
                    await updateInstancesForServer(server.server_ip, response.data.instances);
                    res.write(`data: ${JSON.stringify({ 
                        server_ip: server.server_ip,
                        server_name: server.server_name,
                        server_type: server.server_type,
                        status: 'success' 
                    })}\n\n`);
                    return { server_ip: server.server_ip, status: 'success' };
                })
                .catch(error => {
                    res.write(`data: ${JSON.stringify({ 
                        server_ip: server.server_ip,
                        server_name: server.server_name,
                        server_type: server.server_type,
                        status: 'failed',
                        error: error.message 
                    })}\n\n`);
                    return {
                        server_ip: server.server_ip,
                        status: 'failed',
                        error: error.message
                    };
                });
        });

        Promise.allSettled(refreshPromises).then(() => {
            res.write(`data: ${JSON.stringify({ message: "Refresh process completed." })}\n\n`);
            res.end();
        });
    });
}

module.exports = { refreshInstances, refreshInstancesStream };
