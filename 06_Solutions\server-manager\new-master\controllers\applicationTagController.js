const db = require('../db/db');

/**
 * Get all tags for a specific application
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getApplicationTags = (req, res) => {
    const { applicationId } = req.params;

    // First check if the application exists
    db.get('SELECT 1 FROM applications WHERE id = ?', [applicationId], (err, application) => {
        if (err) {
            console.error('Error checking application existence:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to fetch tags for this application'
            });
        }

        if (!application) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }

        // Application exists, now get its tags
        const query = `
            SELECT t.id, t.name, t.description
            FROM tags t
            JOIN application_tags at ON t.id = at.tag_id
            WHERE at.application_id = ?
            ORDER BY t.name ASC
        `;

        db.all(query, [applicationId], (err, tags) => {
            if (err) {
                console.error('Error fetching application tags:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to fetch tags for this application'
                });
            }

            // Return an error if no tags are found
            if (!tags || tags.length === 0) {
                return res.status(404).json({
                    success: false,
                    error: 'No tags found for this application'
                });
            }

            res.json({
                success: true,
                count: tags.length,
                data: tags
            });
        });
    });
};

/**
 * Add a tag to an application
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addTagToApplication = (req, res) => {
    const { applicationId, tagId } = req.body;

    if (!applicationId || !tagId) {
        return res.status(400).json({
            success: false,
            error: 'Application ID and Tag ID are required'
        });
    }

    // First check if the application and tag exist
    const checkQuery = `
        SELECT 
            (SELECT 1 FROM applications WHERE id = ?) as application_exists,
            (SELECT 1 FROM tags WHERE id = ?) as tag_exists
    `;

    db.get(checkQuery, [applicationId, tagId], (err, result) => {
        if (err) {
            console.error('Error checking application and tag:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to add tag to application'
            });
        }

        if (!result.application_exists) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }

        if (!result.tag_exists) {
            return res.status(404).json({
                success: false,
                error: 'Tag not found'
            });
        }

        // Check if the association already exists
        db.get(
            'SELECT 1 FROM application_tags WHERE application_id = ? AND tag_id = ?',
            [applicationId, tagId],
            (err, association) => {
                if (err) {
                    console.error('Error checking tag association:', err.message);
                    return res.status(500).json({
                        success: false,
                        error: 'Failed to add tag to application'
                    });
                }

                if (association) {
                    return res.status(409).json({
                        success: false,
                        error: 'Tag is already associated with this application'
                    });
                }

                // Add the tag to the application
                const query = `INSERT INTO application_tags (application_id, tag_id) VALUES (?, ?)`;

                db.run(query, [applicationId, tagId], function (err) {
                    if (err) {
                        console.error('Error adding tag to application:', err.message);
                        return res.status(500).json({
                            success: false,
                            error: 'Failed to add tag to application'
                        });
                    }

                    res.status(201).json({
                        success: true,
                        message: 'Tag added to application successfully',
                        data: {
                            applicationId: parseInt(applicationId),
                            tagId: parseInt(tagId)
                        }
                    });
                });
            }
        );
    });
};

/**
 * Remove a tag from an application
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const removeTagFromApplication = (req, res) => {
    const { applicationId, tagId } = req.params;

    // First check if the association exists
    db.get(
        'SELECT 1 FROM application_tags WHERE application_id = ? AND tag_id = ?',
        [applicationId, tagId],
        (err, association) => {
            if (err) {
                console.error('Error checking tag association:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to remove tag from application'
                });
            }

            if (!association) {
                return res.status(404).json({
                    success: false,
                    error: 'Tag is not associated with this application'
                });
            }

            // Remove the tag from the application
            const query = `DELETE FROM application_tags WHERE application_id = ? AND tag_id = ?`;

            db.run(query, [applicationId, tagId], function(err) {
                if (err) {
                    console.error('Error removing tag from application:', err.message);
                    return res.status(500).json({
                        success: false,
                        error: 'Failed to remove tag from application'
                    });
                }

                res.json({
                    success: true,
                    message: 'Tag removed from application successfully',
                    data: {
                        applicationId: parseInt(applicationId),
                        tagId: parseInt(tagId)
                    }
                });
            });
        }
    );
};

module.exports = {
    getApplicationTags,
    addTagToApplication,
    removeTagFromApplication
};
