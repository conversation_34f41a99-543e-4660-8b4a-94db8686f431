const express = require('express');
const router = express.Router();
const { getPublicApplications, getAllApplications, updateApplication } = require('../controllers/applicationController');
// const authenticateAdmin = require('../middleware/authenticateAdmin');

/**
 * @route   GET /applications/public
 * @desc    Get all public applications
 * @access  Public
 */
router.get('/public', getPublicApplications);

/**
 * @route   GET /applications/admin
 * @desc    Get all applications (admin only)
 * @access  Private (Admin)
 */
router.get('/admin',
    // authenticateAdmin,
    getAllApplications);

/**
 * @route   PUT /applications/:id
 * @desc    Update application details
 * @access  Private (Admin)
 */
router.put('/:id',
    // authenticateAdmin,
    updateApplication);

module.exports = router;
