import { createRouter, createWebHistory } from "vue-router";
import { ref } from "vue";
import { userState } from "../main";
import ReportsPage from "../components/ReportsPage.vue";
import DashboardPage from "../components/DashboardPage.vue";
import AboutPage from "../components/AboutPage.vue";
import MonitoringPage from "../components/MonitoringPage.vue";
import DatabasePage from "../components/DatabasePage.vue";
import IISControl from '../components/IISControl.vue';

export const isLoading = ref(false);
export const isInitialLoad = ref(true);

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/",
      redirect: "/dashboard"
    },
    {
      path: "/dashboard",
      name: "Dashboard",
      component: DashboardPage
    },
    {
      path: "/monitoring",
      name: "Monitoring",
      component: MonitoringPage,
      meta: {
        requiresAuth: true
      }
    },
    {
      path: "/database",
      name: "Database",
      component: DatabasePage,
      meta: {
        requiresAuth: true
      }
    },
    {
      path: "/documentation",
      name: "Documentation",
      component: ReportsPage,
      meta: {
        title: "Documentation"
      }
    },
    {
      path: "/about",
      name: "About",
      component: AboutPage
    },
    {
      path: '/iis-control',
      name: 'iis-control',
      component: IISControl,
      meta: { requiresAuth: true }
    }
  ]
});

router.beforeEach((to, from, next) => {
  // Check for protected routes that require admin access
  if ((to.path === "/database" || to.path === "/monitoring" || to.path === "/iis-control") &&
    (!userState.isLoggedIn || !userState.user?.token)) {
    userState.isLoggedIn = false;
    userState.user = null;
    next("/");
    return;
  }

  isLoading.value = true;
  setTimeout(() => {
    next();
  }, 50);
});

router.afterEach(() => {
  // Reduced delay from 1000ms to 300ms
  setTimeout(() => {
    isLoading.value = false;
    isInitialLoad.value = false;
  }, 1000);
});

export default router;
