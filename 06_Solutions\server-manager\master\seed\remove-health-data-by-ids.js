const db = require('../db');

// Get IDs from command line arguments
const ids = process.argv.slice(2);

if (ids.length === 0) {
    console.error('❌ Please provide at least one ID to delete');
    console.log('Usage: node remove-health-data-by-ids.js id1 id2 id3 ...');
    process.exit(1);
}

console.log(`🔄 Starting removal of health data records for IDs: ${ids.join(', ')}...`);

// Convert IDs to numbers and validate
const validIds = ids.map(id => {
    const numId = parseInt(id);
    if (isNaN(numId)) {
        console.error(`❌ Invalid ID format: ${id}`);
        process.exit(1);
    }
    return numId;
});

// Create placeholders for SQL query
const placeholders = validIds.map(() => '?').join(',');
const deleteQuery = `DELETE FROM health_data WHERE id IN (${placeholders})`;

db.run(deleteQuery, validIds, function(err) {
    if (err) {
        console.error('❌ Error removing health data records:', err.message);
        process.exit(1);
    }
    
    console.log(`✅ Successfully removed ${this.changes} health data records`);
    
    // Verify the removal
    const verifyQuery = `SELECT id FROM health_data WHERE id IN (${placeholders})`;
    db.all(verifyQuery, validIds, (err, rows) => {
        if (err) {
            console.error('❌ Error verifying removal:', err.message);
            process.exit(1);
        }
        
        if (rows.length === 0) {
            console.log('✅ Verification successful - All specified records were removed');
            
            // Show remaining count
            db.get('SELECT COUNT(*) as count FROM health_data', (err, row) => {
                if (err) {
                    console.error('❌ Error counting remaining records:', err.message);
                } else {
                    console.log(`📊 Remaining health data records: ${row.count}`);
                }
                
                // Close the database connection
                db.close(() => {
                    console.log('📡 Database connection closed.');
                    process.exit(0);
                });
            });
        } else {
            console.error('❌ Verification failed - Some records still exist');
            console.log('Remaining records:', rows.map(row => row.id));
            db.close(() => {
                console.log('📡 Database connection closed.');
                process.exit(1);
            });
        }
    });
});