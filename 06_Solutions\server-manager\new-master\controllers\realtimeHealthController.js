const db = require('../db/db');

/**
 * @desc    Get server information for WebSocket connections
 * @route   GET /realtime-health
 * @access  Public
 */
async function getInitialHealthData(req, res) {
    try {
        // Get all servers from database
        db.all('SELECT id, ip, server_name FROM servers', (err, servers) => {
            if (err) {
                console.error('Error fetching servers:', err);
                return res.status(500).json({ 
                    error: 'Failed to fetch servers',
                    details: err.message 
                });
            }
            
            if (!servers || servers.length === 0) {
                return res.status(404).json({ 
                    error: 'No servers found in database' 
                });
            }
            
            // Return server information for WebSocket connection
            const result = servers.map(server => ({
                id: server.id,
                ip: server.ip,
                name: server.server_name
            }));
            
            res.status(200).json({
                // timestamp: new Date().toISOString(),
                servers: result,
                websocketUrl: `ws://${req.headers.host}`
            });
        });
    } catch (error) {
        console.error('Error fetching server information:', error);
        res.status(500).json({ 
            error: 'Failed to fetch server information',
            details: error.message 
        });
    }
}

module.exports = {
    getInitialHealthData
};