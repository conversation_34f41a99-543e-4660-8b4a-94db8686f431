require('dotenv').config();
const express = require('express');
const morgan = require('morgan');
const cors = require('cors');
const { handleGetRequest } = require('./services/instances');
const { getHealthData } = require('./services/healthService');
const WebSocketServer = require('./services/websocketClient');
const { executeIISCommand, getIISStatus } = require('./services/iisService');
const { getArasVersionInfo } = require('./services/arasVersionService');
const { discoverDatabases } = require('./services/databaseDiscoveryService');
const { getServiceStatus, executeServiceCommand } = require('./services/windowsServiceManager');
const app = express();
const PORT = process.env.PORT;
const WS_PORT = process.env.WS_PORT;
app.use(express.json());
app.use(morgan('dev'));
app.use(cors());
// also check port 5000 or not

app.use((req, res, next) => {
    const requestIP = req.ip.includes('::ffff:') ? req.ip.split('::ffff:')[1] : req.ip;

    if (requestIP === process.env.MASTER_SERVER_IP) {
        next();  // → Grant Access
    } else {
        res.status(403).json({ error: 'Access denied. Unauthorized IP address.' });  // → Deny Access
    }
});
app.get('/instances', handleGetRequest);
app.get('/health', async (req, res) => {
    try {
        const healthData = await getHealthData();
        res.json(healthData);
    } catch (error) {
        res.status(500).json({ error: 'Failed to collect health data' });
    }
});
app.get('/iis-status', async (req, res) => {
    try {
        const status = await getIISStatus();
        res.json(status);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/iis-control', async (req, res) => {
    const { action } = req.body;
    if (!action) {
        return res.status(400).json({ error: 'Action is required' });
    }
    try {
        const result = await executeIISCommand(action);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.get('/aras-version', async (req, res) => {
    const { appPath, appPaths } = req.query;

    // Handle multiple paths if provided
    if (appPaths) {
        try {
            const pathsArray = Array.isArray(appPaths) ? appPaths : JSON.parse(appPaths);

            if (!Array.isArray(pathsArray)) {
                return res.status(400).json({ error: 'appPaths must be an array' });
            }

            const results = {};
            const promises = pathsArray.map(async (path) => {
                try {
                    const versionInfo = await getArasVersionInfo(path);
                    results[path] = versionInfo;
                } catch (error) {
                    console.error(`Error getting Aras version for ${path}:`, error);
                    results[path] = {
                        error: error.message,
                        version: null,
                        build: null
                    };
                }
            });

            await Promise.all(promises);
            res.json(results);
        } catch (error) {
            console.error('Error processing multiple app paths:', error);
            res.status(400).json({ error: 'Invalid appPaths format' });
        }
    }
    // Maintain backward compatibility for single path
    else if (appPath) {

        try {
            const versionInfo = await getArasVersionInfo(appPath);
            res.json(versionInfo);
        } catch (error) {
            console.error(`Error getting Aras version for ${appPath}:`, error);
            res.status(500).json({
                error: error.message,
                version: null,
                build: null
            });
        }
    } else {
        return res.status(400).json({ error: 'Application path is required (appPath or appPaths)' });
    }
});

app.get('/aras-database-discovery', async (req, res) => {
    try {
        const result = await discoverDatabases();
        if (result.success) {
            res.json(result.data);
        } else {
            res.status(500).json({ error: result.error });
        }
    } catch (error) {
        console.error('Error in database discovery endpoint:', error);
        res.status(500).json({ error: 'Failed to discover databases' });
    }
});

// Get status of a Windows service
app.get('/service-status', async (req, res) => {
    const { service_name } = req.query;

    if (!service_name) {
        return res.status(400).json({
            success: false,
            error: 'service_name parameter is required'
        });
    }

    try {
        const status = await getServiceStatus(service_name);
        res.json(status);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Control a Windows service (start/stop/restart)
app.post('/service-control', async (req, res) => {
    const { service_name, action } = req.body;

    if (!service_name || !action) {
        return res.status(400).json({
            success: false,
            error: 'service_name and action are required'
        });
    }

    try {
        const result = await executeServiceCommand(service_name, action);
        res.json(result);
    } catch (error) {
        console.error(`Error executing ${action} on service ${service_name}:`, error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// WebSocketServer
const wsServer = new WebSocketServer(WS_PORT);

app.listen(PORT, () => {
    console.log(`⚡ Slave server backend is running on port ${PORT}`);
    console.log(`🔗 WebSocket server is running on port ${WS_PORT}`);
});
