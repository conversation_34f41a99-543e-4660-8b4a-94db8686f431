<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  serverIp: {
    type: String,
    required: true
  },
  currentServerName: {
    type: String,
    required: true
  },
  maintainer: {
    type: String,
    required: true
  }
});

const isVisible = ref(false);
const editedServerName = ref('');
const editedMaintainer = ref('');

// Update the edited server name and maintainer whenever the dialog opens or current name/maintainer changes
watch(
  [() => props.currentServerName, () => props.maintainer, () => isVisible.value],
  ([newName, newMaintainer, isVisible]) => {
    if (isVisible) {
      editedServerName.value = newName;
      editedMaintainer.value = newMaintainer;
    }
  }
);

const toggleDialog = () => {
  isVisible.value = !isVisible.value;
  if (isVisible.value) {
    editedServerName.value = props.currentServerName;
    editedMaintainer.value = props.maintainer;
  }
};

const handleSave = () => {
  // Don't save if the name is empty or unchanged and maintainer is empty or unchanged
  if (!editedServerName.value.trim() || !editedMaintainer.value.trim() ||
    (editedServerName.value === props.currentServerName && editedMaintainer.value === props.maintainer)) {
    toggleDialog();
    return;
  }

  emit('save', { serverName: editedServerName.value, maintainer: editedMaintainer.value });
  // Remove the toggleDialog() call from here since it will be handled by the parent
};

const emit = defineEmits(['save']);

defineExpose({ toggleDialog });
</script>

<template>
  <Teleport to="body">
    <transition enter-active-class="animate__animated animate__fadeIn"
      leave-active-class="animate__animated animate__fadeOut">
      <div v-if="isVisible" class="dialog-overlay">
        <div class="edit-dialog animate__animated animate__zoomIn">
          <div class="dialog-header">
            <h2>Edit Server Name</h2>
            <button class="close-btn" @click="toggleDialog">
              <i class="pi pi-times"></i>
            </button>
          </div>

          <div class="dialog-content">
            <div class="server-info">
              <div class="info-row">
                <i class="pi pi-server"></i>
                <span class="server-ip">{{ serverIp }}</span>
              </div>
            </div>
            <div class="form-group">
              <label>Server Name</label>
              <div class="input-wrapper">
                <i class="pi pi-pencil"></i>
                <input type="text" v-model="editedServerName" class="form-control" placeholder="Enter server name">
              </div>
            </div>
            <div class="form-group">
              <label>Maintainer Name</label>
              <div class="input-wrapper">
                <i class="pi pi-user"></i>
                <input type="text" v-model="editedMaintainer" class="form-control" placeholder="Enter maintainer name">
              </div>
              <small class="info-text">
                <i class="pi pi-info-circle"></i>
                Changes will be reflected across the application
              </small>
            </div>
          </div>

          <div class="dialog-footer">
            <button class="cancel-btn" @click="toggleDialog">Cancel</button>
            <button class="save-btn" @click="handleSave">
              <i class="pi pi-check"></i>
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </transition>
  </Teleport>
</template>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-dialog {
  background: white;
  border-radius: 0.75rem; /* Reduced from 12px */
  width: 90%;
  max-width: 400px; /* Reduced from 450px */
  box-shadow: 0 4px 16px rgba(33, 23, 97, 0.12);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem; /* Reduced from 1.25rem 1.5rem */
  border-bottom: 1px solid #dddae4;
}

.dialog-header h2 {
  margin: 0;
  color: #211761;
  font-size: 1rem; /* Reduced from 1.25rem */
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  width: 28px; /* Reduced from 32px */
  height: 28px; /* Reduced from 32px */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-btn i {
  font-size: 0.875rem; /* Added explicit size */
}

.close-btn:hover {
  background-color: rgba(82, 55, 204, 0.05);
  color: #211761;
}

.server-info {
  background-color: rgba(82, 55, 204, 0.05);
  padding: 0.75rem 1rem; /* Reduced from 1rem 1.5rem */
  margin-bottom: 1rem; /* Reduced from 1.5rem */
}

.info-row {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Reduced from 0.75rem */
}

.info-row i {
  color: #5237cc;
  font-size: 0.875rem; /* Reduced from 1.1rem */
}

.server-ip {
  color: #211761;
  font-weight: 500;
  font-size: 0.875rem; /* Added explicit size */
}

.dialog-content {
  padding: 1rem; /* Reduced from 1.5rem */
}

.form-group {
  margin-bottom: 0.75rem; /* Reduced from 1rem */
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem; /* Reduced from 0.75rem */
  color: #211761;
  font-weight: 500;
  font-size: 0.875rem; /* Added explicit size */
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper i {
  position: absolute;
  left: 0.75rem; /* Reduced from 1rem */
  color: #5237cc;
  font-size: 0.875rem; /* Added explicit size */
}

.form-control {
  width: 100%;
  padding: 0.625rem 0.75rem 0.625rem 2rem; /* Reduced from 0.875rem */
  border: 2px solid #dddae4;
  border-radius: 2rem; /* Reduced from 3rem */
  font-size: 0.875rem; /* Reduced from 1rem */
  transition: all 0.2s;
  color: #211761;
}

.form-control:focus {
  outline: none;
  border-color: #5237cc;
  box-shadow: 0 0 0 3px rgba(82, 55, 204, 0.1);
}

.form-control::placeholder {
  color: #a8a4b7;
  font-size: 0.875rem; /* Added explicit size */
}

.info-text {
  display: flex;
  align-items: center;
  gap: 0.4rem; /* Reduced from 0.5rem */
  margin-top: 0.5rem; /* Reduced from 0.75rem */
  color: #666;
  font-size: 0.75rem; /* Reduced from 0.875rem */
}

.info-text i {
  color: #5237cc;
  font-size: 0.75rem; /* Reduced from 0.875rem */
}

.dialog-footer {
  padding: 0.75rem 1rem; /* Reduced from 1.25rem 1.5rem */
  border-top: 1px solid #dddae4;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem; /* Reduced from 1rem */
}

.cancel-btn, .save-btn {
  padding: 0.5rem 1rem; /* Reduced from 0.75rem 1.5rem */
  border-radius: 2rem; /* Reduced from 3rem */
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  font-size: 0.875rem; /* Reduced from 0.9375rem */
}

.cancel-btn {
  background: none;
  border: 2px solid #dddae4;
  color: #666;
}

.cancel-btn:hover {
  border-color: #211761;
  color: #211761;
}

.save-btn {
  display: flex;
  align-items: center;
  gap: 0.4rem; /* Reduced from 0.5rem */
  background: #211761;
  border: none;
  color: white;
}

.save-btn:hover {
  background: #5237cc;
}

.save-btn i {
  font-size: 0.75rem; /* Reduced from 0.875rem */
}

/* Add responsive adjustments */
@media (max-width: 480px) {
  .edit-dialog {
    width: 95%;
    max-width: none;
  }

  .dialog-header,
  .dialog-content,
  .dialog-footer,
  .server-info {
    padding: 0.625rem;
  }
}
</style>







