const axios = require('axios');
const db = require('../db/db');
const SLAVE_PORT = process.env.SLAVE_PORT;

/**
 * Get IIS service status for a server or all servers
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getIISStatus = async (req, res) => {
    const { server_ip } = req.query;

    // If server_ip is not provided, return status for all servers
    if (!server_ip) {
        try {
            // Get all servers from database
            const servers = await new Promise((resolve, reject) => {
                db.all('SELECT * FROM servers', (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                });
            });

            if (!servers || servers.length === 0) {
                return res.status(404).json({ 
                    success: false, 
                    error: 'No servers found' 
                });
            }

            // Get status for each server in parallel
            const statusPromises = servers.map(async (server) => {
                try {
                    const response = await axios.get(`http://${server.ip}:${SLAVE_PORT}/iis-status`, {
                        timeout: 3000
                    });
                    
                    return {
                        server_id: server.id,
                        server_ip: server.ip,
                        server_name: server.server_name || 'Unknown',
                        status: response.data.status,
                        success: true
                    };
                } catch (error) {
                    return {
                        server_id: server.id,
                        server_ip: server.ip,
                        server_name: server.server_name || 'Unknown',
                        status: 'unknown',
                        success: false,
                        error: error.message
                    };
                }
            });

            const results = await Promise.all(statusPromises);

            return res.json({
                success: true,
                date: new Date().toLocaleDateString(),
                current_time: new Date().toLocaleTimeString(),
                servers: results,
                summary: {
                    total: results.length,
                    working: results.filter(s => s.success && s.status !== 'false').length,
                    unknown: results.filter(s => !s.success).length
                }
            });
        } catch (error) {
            console.error('IIS Status Error:', error);
            return res.status(500).json({ 
                success: false, 
                error: error.message 
            });
        }
    } else {
        // Get status for a single server
        try {
            // Get server from database
            const server = await new Promise((resolve, reject) => {
                db.get('SELECT * FROM servers WHERE ip = ?', [server_ip], (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                });
            });

            if (!server) {
                return res.status(404).json({ 
                    success: false, 
                    error: 'Server not found' 
                });
            }

            // Get remote status
            try {
                const response = await axios.get(`http://${server_ip}:${SLAVE_PORT}/iis-status`, {
                    timeout: 3000
                });
                return res.json(response.data);
            } catch (error) {
                return res.status(500).json({ 
                    success: false, 
                    error: `Failed to get status from server ${server_ip}: ${error.message}` 
                });
            }
        } catch (error) {
            console.error('IIS Status Error:', error);
            return res.status(500).json({ 
                success: false, 
                error: error.message 
            });
        }
    }
};

/**
 * Control IIS service (start/stop/restart)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const controlIIS = async (req, res) => {
    const { server_ip, action } = req.body;

    if (!server_ip || !action) {
        return res.status(400).json({ 
            success: false, 
            error: 'server_ip and action are required' 
        });
    }

    // Validate action
    const validActions = ['start', 'stop', 'restart'];
    if (!validActions.includes(action)) {
        return res.status(400).json({ 
            success: false, 
            error: 'Invalid action. Must be one of: start, stop, restart' 
        });
    }

    try {
        // Get server from database
        const server = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM servers WHERE ip = ?', [server_ip], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (!server) {
            return res.status(404).json({ 
                success: false, 
                error: 'Server not found' 
            });
        }

        // Execute remote command
        try {
            const response = await axios.post(
                `http://${server_ip}:${SLAVE_PORT}/iis-control`, 
                { action },
                { 
                    timeout: 30000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );
            return res.json(response.data);
        } catch (error) {
            return res.status(500).json({ 
                success: false, 
                error: `Failed to execute ${action} on server ${server_ip}: ${error.message}` 
            });
        }
    } catch (error) {
        console.error('IIS Control Error:', error);
        return res.status(500).json({ 
            success: false, 
            error: error.message 
        });
    }
};

module.exports = {
    getIISStatus,
    controlIIS
};

