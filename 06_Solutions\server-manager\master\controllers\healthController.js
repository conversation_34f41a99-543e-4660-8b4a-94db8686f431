async function checkSlaveStatus(req, res) {
    try {
        const results = await Promise.all(slaveServers.map(async (slave) => {
            try {
                await axios.get(`http://${slave.server_ip}:3000/health`, {
                    timeout: 5000
                });
                return {
                    server_ip: slave.server_ip,
                    status: 'online',
                    latency: response.duration
                };
            } catch (error) {
                return {
                    server_ip: slave.server_ip,
                    status: 'offline',
                    error: error.code || error.message
                };
            }
        }));
        
        res.json({
            timestamp: new Date().toISOString(),
            results
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
}