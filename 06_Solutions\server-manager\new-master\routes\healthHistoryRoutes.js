const express = require('express');
const router = express.Router();
const { 
    getHealthHistory, 
    collectHealthData
} = require('../controllers/healthHistoryController');
// const authenticateAdmin = require('../middleware/authenticateAdmin');

/**
 * @route   GET /health-history
 * @desc    Get health history for all servers
 * @access  Private (Admin only)
 */
router.get('/', 
    // authenticateAdmin, 
    getHealthHistory);

/**
 * @route   POST /health-history/collect
 * @desc    Collect and store health data from all servers
 * @access  Private (Admin only)
 */
router.get('/collect', 
    // authenticateAdmin, 
    collectHealthData);

module.exports = router;