const db = require('../db/db');

/**
 * Get all public applications (where public = 1) grouped by server
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getPublicApplications = (req, res) => {
    // First get all servers with their details
    const serversQuery = `SELECT id, ip, server_name, maintainer, created_at FROM servers`;

    db.all(serversQuery, [], (err, servers) => {
        if (err) {
            console.error('Error fetching servers:', err.message);
            return res.status(500).json({ error: 'Failed to fetch servers' });
        }

        // Get all public applications with their related data
        const applicationsQuery = `
            SELECT
                a.*,
                s.ip as server_ip,
                GROUP_CONCAT(DISTINCT t.id || ':' || t.name || ':' || t.description) as tags,
                GROUP_CONCAT(DISTINCT ad.id || ':' || ad.database_name) as databases,
                av.aras_version as version,
                bn.build_number as build_number
            FROM applications a
            LEFT JOIN servers s ON a.server_id = s.id
            LEFT JOIN application_tags at ON a.id = at.application_id
            LEFT JOIN tags t ON at.tag_id = t.id
            LEFT JOIN aras_databases ad ON a.id = ad.application_id
            LEFT JOIN aras_versions av ON a.id = av.application_id
            LEFT JOIN aras_versions bn ON a.id = bn.application_id
            WHERE a.public = 1
            GROUP BY a.id
        `;

        db.all(applicationsQuery, [], (err, applications) => {
            if (err) {
                console.error('Error fetching public applications:', err.message);
                return res.status(500).json({ error: 'Failed to fetch public applications' });
            }

            // Process the results to format tags and databases as arrays
            const processedApplications = applications.map(app => {
                // tags: "1:ERP:Enterprise Resource Planning,2:Production:Production Tag"
                let tags = [];
                if (app.tags) {
                    tags = app.tags
                        .split(',')
                        .filter(tagStr => tagStr && tagStr.includes(':')) // Only non-empty and contains colon
                        .map(tagStr => {
                        const [id, name, description] = tagStr.split(':');
                            if (!id || !name || !description) return null;
                            return { id: parseInt(id), name, description };
                        })
                        .filter(Boolean); // Remove any nulls
                }
                return {
                    ...app,
                    tags,
                    databases: app.databases
                        ? app.databases.split(',').filter(Boolean).map(dbStr => {
                            const [id, name] = dbStr.split(':');
                            if (!id || !name) return null;
                            return { id: parseInt(id), name };
                        }).filter(Boolean)
                        : []
                };
            });

            // Fetch credentials for each application
            const getCredentials = (appId) => {
                return new Promise((resolve, reject) => {
                    const credQuery = `
                        SELECT ac.username, ac.password, ac.database_id, ad.database_name
                        FROM aras_credentials ac
                        JOIN aras_databases ad ON ac.database_id = ad.id
                        WHERE ac.application_id = ?
                    `;

                    db.all(credQuery, [appId], (err, credentials) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve(credentials);
                        }
                    });
                });
            };

            // Fetch credentials for all applications
            Promise.all(processedApplications.map(app =>
                getCredentials(app.id)
                    .then(credentials => {
                        app.credentials = credentials;
                        return app;
                    })
                    .catch(err => {
                        console.error(`Error fetching credentials for app ${app.id}:`, err);
                        app.credentials = [];
                        return app;
                    })
            ))
                .then(appsWithCredentials => {
                    // Group applications by server IP
                    const serverGroups = {};

                    // Create server groups with empty applications arrays
                    servers.forEach(server => {
                        serverGroups[server.ip] = {
                            server_name: server.server_name || 'Unknown',
                            maintainer: server.maintainer || 'Not specified',
                            created_at: server.created_at || null,
                            applications: []
                        };
                    });

                    // Add applications to their respective servers
                    appsWithCredentials.forEach(app => {
                        if (app.server_ip && serverGroups[app.server_ip]) {
                            serverGroups[app.server_ip].applications.push(app);
                        }
                    });

                    // Filter out servers with no applications
                    const result = {};
                    Object.entries(serverGroups).forEach(([ip, data]) => {
                        if (data.applications.length > 0) {
                            result[ip] = data;
                        }
                    });

                    res.json(result);
                })
                .catch(err => {
                    console.error('Error processing applications with credentials:', err);
                    res.status(500).json({ error: 'Failed to process applications with credentials' });
                });
        });
    });
};

/**
 * Get all applications (admin only) grouped by server
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllApplications = (req, res) => {
    // First get all servers with their details
    const serversQuery = `SELECT id, ip, server_name, maintainer, created_at FROM servers`;

    db.all(serversQuery, [], (err, servers) => {
        if (err) {
            console.error('Error fetching servers:', err.message);
            return res.status(500).json({ error: 'Failed to fetch servers' });
        }

        // Get all applications with their related data
        const applicationsQuery = `
            SELECT
                a.*,
                s.ip as server_ip,
                GROUP_CONCAT(DISTINCT t.id || ':' || t.name || ':' || t.description) as tags,
                GROUP_CONCAT(DISTINCT ad.id || ':' || ad.database_name) as databases,
                av.aras_version as version,
                bn.build_number as build_number
            FROM applications a
            LEFT JOIN servers s ON a.server_id = s.id
            LEFT JOIN application_tags at ON a.id = at.application_id
            LEFT JOIN tags t ON at.tag_id = t.id
            LEFT JOIN aras_databases ad ON a.id = ad.application_id
            LEFT JOIN aras_versions av ON a.id = av.application_id
            LEFT JOIN aras_versions bn ON a.id = bn.application_id
            GROUP BY a.id
        `;

        db.all(applicationsQuery, [], (err, applications) => {
            if (err) {
                console.error('Error fetching all applications:', err.message);
                return res.status(500).json({ error: 'Failed to fetch applications' });
            }

            // Process the results to format tags and databases as arrays
            const processedApplications = applications.map(app => {
                let tags = [];
                if (app.tags) {
                    tags = app.tags
                        .split(',')
                        .filter(tagStr => tagStr && tagStr.includes(':')) // Only non-empty and contains colon
                        .map(tagStr => {
                        const [id, name, description] = tagStr.split(':');
                            if (!id || !name || !description) return null;
                            return { id: parseInt(id), name, description };
                        })
                        .filter(Boolean); // Remove any nulls
                }
                return {
                    ...app,
                    tags,
                    databases: app.databases
                        ? app.databases.split(',').filter(Boolean).map(dbStr => {
                            const [id, name] = dbStr.split(':');
                            if (!id || !name) return null;
                            return { id: parseInt(id), name };
                        }).filter(Boolean)
                        : []
                };
            });

            // Fetch credentials for each application
            const getCredentials = (appId) => {
                return new Promise((resolve, reject) => {
                    const credQuery = `
                        SELECT ac.username, ac.password, ac.database_id, ad.database_name
                        FROM aras_credentials ac
                        JOIN aras_databases ad ON ac.database_id = ad.id
                        WHERE ac.application_id = ?
                    `;

                    db.all(credQuery, [appId], (err, credentials) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve(credentials);
                        }
                    });
                });
            };

            // Fetch credentials for all applications
            Promise.all(processedApplications.map(app =>
                getCredentials(app.id)
                    .then(credentials => {
                        app.credentials = credentials;
                        return app;
                    })
                    .catch(err => {
                        console.error(`Error fetching credentials for app ${app.id}:`, err);
                        app.credentials = [];
                        return app;
                    })
            ))
                .then(appsWithCredentials => {
                    // Group applications by server IP
                    const serverGroups = {};

                    // Create server groups with empty applications arrays
                    servers.forEach(server => {
                        serverGroups[server.ip] = {
                            server_name: server.server_name || 'Unknown',
                            maintainer: server.maintainer || 'Not specified',
                            created_at: server.created_at || null,
                            applications: []
                        };
                    });

                    // Add applications to their respective servers
                    appsWithCredentials.forEach(app => {
                        if (app.server_ip && serverGroups[app.server_ip]) {
                            serverGroups[app.server_ip].applications.push(app);
                        }
                    });

                    // Filter out servers with no applications
                    const result = {};
                    Object.entries(serverGroups).forEach(([ip, data]) => {
                        if (data.applications.length > 0) {
                            result[ip] = data;
                        }
                    });

                    res.json(result);
                })
                .catch(err => {
                    console.error('Error processing applications with credentials:', err);
                    res.status(500).json({ error: 'Failed to process applications with credentials' });
                });
        });
    });
};

/**
 * Update application details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateApplication = (req, res) => {
    const { id } = req.params;
    const { name, owner, purpose, status, expected_deletion_date, external_link, public } = req.body;

    // First check if the application exists
    db.get('SELECT id FROM applications WHERE id = ?', [id], (err, application) => {
        if (err) {
            console.error('Error checking application existence:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to update application'
            });
        }

        if (!application) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }

        // Validate date format if provided
        if (expected_deletion_date !== undefined && expected_deletion_date !== '') {
            // Check if the date matches YYYY-MM-DD format
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(expected_deletion_date)) {
                return res.status(400).json({
                    success: false,
                    error: 'Invalid date format. Expected format: YYYY-MM-DD'
                });
            }

            // Validate if it's a valid date
            const dateObj = new Date(expected_deletion_date);
            if (isNaN(dateObj.getTime())) {
                return res.status(400).json({
                    success: false,
                    error: 'Invalid date. Please provide a valid date'
                });
            }
        }

        // Build dynamic update query based on provided fields
        let updateFields = [];
        let params = [];

        // Handle each field - note that we explicitly check if the field exists in the request
        // to allow setting fields to empty values
        if (name !== undefined) {
            updateFields.push('name = ?');
            params.push(name);
        }

        if (owner !== undefined) {
            updateFields.push('owner = ?');
            params.push(owner);
        }

        if (purpose !== undefined) {
            updateFields.push('purpose = ?');
            params.push(purpose);
        }

        if (status !== undefined) {
            updateFields.push('status = ?');
            params.push(status);
        }

        if (expected_deletion_date !== undefined) {
            updateFields.push('expected_deletion_date = ?');
            params.push(expected_deletion_date);
        }

        if (external_link !== undefined) {
            updateFields.push('external_link = ?');
            params.push(external_link);
        }

        if (public !== undefined) {
            updateFields.push('public = ?');
            params.push(public ? 1 : 0);
        }

        // If no fields to update, return early
        if (updateFields.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'No fields provided for update'
            });
        }

        // Add id as the last parameter
        params.push(id);

        const query = `UPDATE applications SET ${updateFields.join(', ')} WHERE id = ?`;

        db.run(query, params, function (err) {
            if (err) {
                console.error('Error updating application:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to update application'
                });
            }

            // If no rows were affected but no error occurred
            if (this.changes === 0) {
                return res.json({
                    success: true,
                    message: 'No changes were made to the application',
                    data: { id: parseInt(id) }
                });
            }

            // Get the updated application
            db.get(
                'SELECT * FROM applications WHERE id = ?',
                [id],
                (err, updatedApplication) => {
                    if (err) {
                        console.error('Error fetching updated application:', err.message);
                        return res.status(500).json({
                            success: false,
                            error: 'Application updated but failed to retrieve details'
                        });
                    }

                    res.json({
                        success: true,
                        message: 'Application updated successfully',
                        data: updatedApplication
                    });
                }
            );
        });
    });
};

module.exports = {
    getPublicApplications,
    getAllApplications,
    updateApplication
};


