// controllers/instanceController.js
const db = require('../db');

/**
 * Retrieves public instances (where is_public is true).
 * Accessible without authentication.
 */
function getPublicInstances(req, res) {
    const sql = `
        SELECT 
            i.*,
            COALESCE(s.server_name, 'Unknown Server') as server_name,
            s.id as server_id,
            s.server_type
        FROM INSTANCE_DATA i
        LEFT JOIN servers s ON i.server_ip = s.server_ip
        WHERE i.is_public = 1
    `;
    
    db.all(sql, [], (err, rows) => {
        if (err) {
            console.error("Error retrieving public instances:", err.message);
            return res.status(500).json({ error: err.message });
        }
        res.json({ instances: rows });
    });
}

/**
 * Retrieves all instances.
 * This endpoint is for admin users.
 */
function getAllInstances(req, res) {
    const sql = `
        SELECT 
            i.*,
            COALESCE(s.server_name, 'Unknown Server') as server_name,
            s.id as server_id,
            s.server_type
        FROM INSTANCE_DATA i
        LEFT JOIN servers s ON i.server_ip = s.server_ip
    `;
    
    db.all(sql, [], (err, rows) => {
        if (err) {
            console.error("Error retrieving all instances:", err.message);
            return res.status(500).json({ error: err.message });
        }
        res.json({ instances: rows });
    });
}

module.exports = { getPublicInstances, getAllInstances };
