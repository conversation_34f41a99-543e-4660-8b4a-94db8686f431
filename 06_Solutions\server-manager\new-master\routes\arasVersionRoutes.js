const express = require('express');
const router = express.Router();
// const authenticateAdmin = require('../middleware/authenticateAdmin');
const { updateArasVersionsAuto, updateArasVersionById } = require('../controllers/arasVersionController');

/**
 * @route   GET /aras-versions/manual
 * @desc    Manually fetch and update Aras versions (SSE) - Admin only
 * @access  Private (Admin only)
 */
router.get('/', 
    // authenticateAdmin, 
    updateArasVersionsAuto);

/**
 * @route   PUT /aras-versions/:id
 * @desc    Update an existing Aras version entry by ID
 * @access  Private (Admin only)
 */
router.put('/:application_id',
    // authenticateAdmin, 
    updateArasVersionById);


module.exports = router;
