const axios = require('axios');
const db = require('../db/db');
const SLAVE_PORT = process.env.SLAVE_PORT;

/**
 * Discover databases for all servers and update the database
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const discoverDatabases = async (req, res) => {
    try {
        // Get all servers from database
        const servers = await new Promise((resolve, reject) => {
            db.all('SELECT id, ip FROM servers', (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        if (!servers || servers.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'No servers found'
            });
        }

        const results = {
            success: [],
            failed: []
        };

        // Process each server
        for (const server of servers) {
            try {
                // Call the slave server's database-discovery endpoint
                const response = await axios.get(`http://${server.ip}:${SLAVE_PORT}/aras-database-discovery`, {
                    timeout: 5000
                });

                const databaseData = response.data;
                
                // Calculate total databases found
                let totalDatabases = 0;
                let appsWithDatabases = 0;
                let appsWithoutDatabases = 0;
                
                Object.entries(databaseData).forEach(([appPath, databases]) => {
                    if (databases && databases.length > 0) {
                        totalDatabases += databases.length;
                        appsWithDatabases++;
                    } else {
                        appsWithoutDatabases++;
                    }
                });
                
                // Process the response data
                await updateDatabasesForServer(server.id, databaseData);
                
                results.success.push({
                    server_ip: server.ip,
                    total_apps: Object.keys(databaseData).length,
                    apps_with_databases: appsWithDatabases,
                    apps_without_databases: appsWithoutDatabases,
                    total_databases_found: totalDatabases
                });
            } catch (error) {
                console.error(`Error discovering databases for server ${server.ip}:`, error.message);
                results.failed.push({
                    server_ip: server.ip,
                    error: error.message
                });
            }
        }

        res.json({
            success: true,
            results: {
                total: servers.length,
                successful: results.success.length,
                failed: results.failed.length,
                details: {
                    success: results.success,
                    failed: results.failed
                }
            }
        });
    } catch (error) {
        console.error('Database discovery failed:', error.message);
        res.status(500).json({
            success: false,
            error: 'Database discovery failed',
            details: error.message
        });
    }
};

/**
 * Update databases for a specific server
 * @param {number} serverId - Server ID
 * @param {Object} databaseData - Database data from slave server
 */
async function updateDatabasesForServer(serverId, databaseData) {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            db.run('BEGIN TRANSACTION');

            // Get all applications for this server
            db.all('SELECT id, path FROM applications WHERE server_id = ?', [serverId], (err, applications) => {
                if (err) {
                    db.run('ROLLBACK');
                    return reject(err);
                }

                const appPromises = [];

                // Process each application
                applications.forEach(app => {
                    // Check if this application path exists in the response
                    if (databaseData.hasOwnProperty(app.path)) {
                        const databaseNames = databaseData[app.path];
                        
                        appPromises.push(
                            new Promise((resolveApp) => {
                                // Get existing databases for this application
                                db.all('SELECT id, database_name FROM aras_databases WHERE application_id = ?', 
                                    [app.id], (err, existingDatabases) => {
                                    if (err) {
                                        console.error(`Error getting existing databases: ${err.message}`);
                                        return resolveApp();
                                    }

                                    const existingDbNames = new Set(existingDatabases.map(db => db.database_name));
                                    const existingDbMap = new Map();
                                    existingDatabases.forEach(db => {
                                        existingDbMap.set(db.database_name, db.id);
                                    });
                                    
                                    const dbPromises = [];

                                    // Add new databases
                                    databaseNames.forEach(dbName => {
                                        if (!existingDbNames.has(dbName)) {
                                            dbPromises.push(
                                                new Promise((resolveDb) => {
                                                    db.run(
                                                        'INSERT INTO aras_databases (application_id, database_name) VALUES (?, ?)',
                                                        [app.id, dbName],
                                                        (err) => {
                                                            if (err) {
                                                                console.error(`Error inserting database: ${err.message}`);
                                                            }
                                                            resolveDb();
                                                        }
                                                    );
                                                })
                                            );
                                        }
                                    });

                                    // Remove databases that no longer exist
                                    existingDatabases.forEach(existingDb => {
                                        if (!databaseNames.includes(existingDb.database_name)) {
                                            dbPromises.push(
                                                new Promise((resolveDb) => {
                                                    db.run(
                                                        'DELETE FROM aras_databases WHERE id = ?',
                                                        [existingDb.id],
                                                        (err) => {
                                                            if (err) {
                                                                console.error(`Error deleting database: ${err.message}`);
                                                            }
                                                            resolveDb();
                                                        }
                                                    );
                                                })
                                            );
                                        }
                                    });

                                    Promise.all(dbPromises).then(() => resolveApp());
                                });
                            })
                        );
                    }
                });

                Promise.all(appPromises)
                    .then(() => {
                        db.run('COMMIT', (err) => {
                            if (err) {
                                console.error(`Error committing transaction: ${err.message}`);
                                db.run('ROLLBACK');
                                reject(err);
                            } else {
                                resolve();
                            }
                        });
                    })
                    .catch(err => {
                        console.error(`Error processing applications: ${err.message}`);
                        db.run('ROLLBACK');
                        reject(err);
                    });
            });
        });
    });
}

module.exports = {
    discoverDatabases
};
