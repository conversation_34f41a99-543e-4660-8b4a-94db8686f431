<script setup>
const props = defineProps({
  label: {
    type: String,
    required: true
  },
  sortKey: {
    type: String,
    required: true
  },
  currentSort: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['sort']);

const handleSort = () => {
  emit('sort', props.sortKey);
};

const getSortIcon = () => {
  if (props.currentSort.key !== props.sortKey) return 'pi-sort';
  return props.currentSort.direction === 'asc' ? 'pi-sort-up' : 'pi-sort-down';
};
</script>

<template>
  <div class="sortable-header" @click="handleSort">
    {{ label }}
    <i :class="['pi', getSortIcon()]"></i>
  </div>
</template>

<style scoped>
.sortable-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  user-select: none;
}

.sortable-header:hover {
  opacity: 0.9;
}

.pi {
  font-size: 0.8rem;
}
</style>

