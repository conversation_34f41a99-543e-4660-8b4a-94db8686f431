const si = require('systeminformation');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

function getDiskIO() {
    return new Promise((resolve) => {
        // Create a temporary PowerShell script file
        const tempScriptPath = path.join(os.tmpdir(), 'get_disk_io.ps1');
        const scriptContent = `
            try {
                $diskStats = Get-WmiObject -Class Win32_PerfFormattedData_PerfDisk_PhysicalDisk -Filter "Name='_Total'"
                if ($diskStats -ne $null) {
                    $read = [math]::Round($diskStats.DiskReadBytesPersec / 1024, 1)
                    $write = [math]::Round($diskStats.DiskWriteBytesPersec / 1024, 1)
                    Write-Output "$read|$write"
                } else { Write-Output "0|0" }
            } catch { Write-Output "0|0" }
        `; 

        // Write the script to a temporary file
        fs.writeFileSync(tempScriptPath, scriptContent);

        // Execute the script file
        exec(`powershell -ExecutionPolicy Bypass -File "${tempScriptPath}"`, (error, stdout, stderr) => {
            // Clean up the temporary file
            try {
                fs.unlinkSync(tempScriptPath);
            } catch (e) {
                console.error('Error deleting temporary script:', e);
            }
            if (error || stderr) {
                return resolve({ read_speed: 0, write_speed: 0 });
            }
            try {
                const output = stdout.trim();
                const parts = output.split('|');
                const read = parseFloat(parts[0]) || 0;
                const write = parseFloat(parts[1]) || 0;
                resolve({ read_speed: read, write_speed: write });
            } catch (err) {
                console.error('Error parsing disk I/O data:', err);
                resolve({ read_speed: 0, write_speed: 0 });
            }
        });
    });
}

async function getHealthData() {
    try {
        const [cpu, mem, disk, networkStats, diskIO] = await Promise.all([si.currentLoad(), si.mem(), si.fsSize(), si.networkStats(), getDiskIO()]);
        const now = new Date();
        const date = now.toISOString().split('T')[0];
        const time = now.toTimeString().split(' ')[0];
        const networkUsage = networkStats.reduce((acc, net) => ({ rx_sec: acc.rx_sec + (net.rx_sec || 0), tx_sec: acc.tx_sec + (net.tx_sec || 0) }), { rx_sec: 0, tx_sec: 0 });
        return {
            date,
            time,
            cpu: {
                usage: Math.round(cpu.currentLoad * 10) / 10
            },
            memory: {
                used: Math.round(mem.used / (1024 * 1024 * 1024) * 100) / 100,
                total: Math.round(mem.total / (1024 * 1024 * 1024) * 100) / 100
            },
            disk: {
                used: Math.round(disk[0].used / (1024 * 1024 * 1024) * 100) / 100,
                total: Math.round(disk[0].size / (1024 * 1024 * 1024) * 100) / 100,
                read_speed: diskIO.read_speed,
                write_speed: diskIO.write_speed // Make sure this is included
            },
            network: {
                download_speed: Math.round((networkUsage.rx_sec / (1024 * 1024)) * 100) / 100,
                upload_speed: Math.round((networkUsage.tx_sec / (1024 * 1024)) * 100) / 100
            }
        };
    } catch (error) {
        console.error('Error collecting health data:', error);
        const now = new Date();
        return {
            date: now.toISOString().split('T')[0],
            time: now.toTimeString().split(' ')[0],
            cpu: { usage: 0 },
            memory: { used: 0, total: 0 },
            disk: { used: 0, total: 0, read_speed: 0, write_speed: 0 },
            network: { download_speed: 0, upload_speed: 0 }
        };
    }
}

module.exports = { getHealthData };