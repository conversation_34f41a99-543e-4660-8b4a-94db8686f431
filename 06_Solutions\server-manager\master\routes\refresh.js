// routes/refresh.js
const express = require('express');
const router = express.Router();
const authenticateAdmin = require('../middlewares/authenticateAdmin');
const { refreshInstances, refreshInstancesStream } = require('../controllers/refreshController');

// Manual refresh endpoint (SSE): only accessible to admin.
router.get('/refresh', authenticateAdmin, refreshInstancesStream);

// Auto refresh endpoint: open endpoint that returns JSON.
router.get('/refresh/auto', refreshInstances);

module.exports = router;
