<script setup>
import { ref, inject, getCurrentInstance, onMounted, onUnmounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import LoginDrawer from './LoginDrawer.vue';
import { userState } from '../main';

const router = useRouter();
const app = getCurrentInstance();
const alert = inject('alert') || app?.appContext.config.globalProperties.$alert;
const loginDrawer = ref(null);

const menuItems = [
  { path: '/dashboard', shortcut: '1' },
  { path: '/monitoring', shortcut: '2' },
  { path: '/documentation', shortcut: '3' },
  { path: '/iis-control', shortcut: '4' },
  { path: '/about', shortcut: '5' },
  { path: '/admin', shortcut: '6' }
];

const handleKeyboardShortcut = (event) => {
  // Handle login shortcut
  if (event.altKey && event.key === 'l') {
    event.preventDefault();
    handleLoginClick();
    return;
  }

  // Handle navigation shortcuts with immediate execution
  if (event.altKey && /^[1-6]$/.test(event.key)) {
    event.preventDefault();
    const menuItem = menuItems[parseInt(event.key) - 1];
    if (menuItem) {
      router.push(menuItem.path).catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          throw err;
        }
      });
    }
  }
};

// Add error handling for router navigation
// const navigateTo = (path) => {
//   router.push(path).catch(err => {
//     if (err.name !== 'NavigationDuplicated') {
//       throw err;
//     }
//   });
// };

const handleLoginClick = () => {
  if (userState.isLoggedIn) {
    userState.isLoggedIn = false;
    userState.user = null;
    localStorage.removeItem('userState');
    alert?.show('Session ended.', 'success', 3000);
    router.push('/');
  } else {
    loginDrawer.value.toggleDrawer();
  }
};

// const isAdmin = computed(() => {
//   return userState.isLoggedIn && userState.user?.token?.role === 'admin';
// });

onMounted(() => {
  window.addEventListener('keydown', handleKeyboardShortcut);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyboardShortcut);
});
</script>

<template>
  <div>
    <nav class="navbar">
      <div class="left-section">
        <div class="logo">
          <a href="/">
            <img src="/servertap-logo.svg" alt="Logo" type="image/svg+xml" class="logo-img" />
          </a>
        </div>
        <div class="nav-menu">
          <router-link to="/dashboard" class="nav-item" :class="{ 'router-link-active': $route.path === '/dashboard' }"
            title="Dashboard (Alt + 1)">
            <i class="pi pi-chart-line"></i>
            <span>Dashboard</span>
          </router-link>
          <router-link v-if="userState.isLoggedIn" to="/monitoring" class="nav-item"
            :class="{ 'router-link-active': $route.path === '/monitoring' }" title="Monitoring (Alt + 2)">
            <i class="pi pi-chart-bar"></i>
            <span>Monitoring</span>
          </router-link>
          <router-link v-if="userState.isLoggedIn" to="/database" class="nav-item"
            :class="{ 'router-link-active': $route.path === '/admin' }" title="Admin (Alt + 3)">
            <i class="pi pi-database"></i>
            <span>Admin</span>
          </router-link>
          <router-link v-if="userState.isLoggedIn" to="/iis-control" class="nav-item"
            :class="{ 'router-link-active': $route.path === '/iis-control' }" title="IIS Control (Alt + 4)">
            <i class="pi pi-server"></i>
            <span>IIS Control</span>
          </router-link>
          <router-link to="/documentation" class="nav-item"
            :class="{ 'router-link-active': $route.path === '/documentation' }" title="Documentation (Alt + 5)">
            <i class="pi pi-book"></i>
            <span>Documentation</span>
          </router-link>
          <router-link to="/about" class="nav-item" :class="{ 'router-link-active': $route.path === '/about' }"
            title="About (Alt + 6">
            <i class="pi pi-info-circle"></i>
            <span>About</span>
          </router-link>
        </div>
      </div>
      <div class="nav-right">
        <button class="login-btn" @click="handleLoginClick" title="Login/Logout (Alt + L)">
          <i class="pi pi-user"></i>
          <span class="login-text">{{ userState.isLoggedIn ? 'Logout' : 'Login' }}</span>
        </button>
      </div>
    </nav>
    <LoginDrawer ref="loginDrawer" />
  </div>
</template>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.4rem 0;
  height: 48px;
  background-color: var(--bg-primary);
  box-shadow: 0 1px 8px -1px var(--shadow-color),
    0 2px 4px -1px var(--shadow-color);
}

.left-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding-left: 2rem;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-img {
  height: 2rem;
  width: auto;
}

.nav-menu {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  padding: 0.4rem;
  border-radius: 0.4rem;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.nav-item i {
  font-size: 0.9rem;
}

.nav-item:hover {
  color: var(--bg-secondary);
  background-color: rgba(82, 55, 204, 0.1);
}

.nav-item.router-link-active {
  color: var(--bg-secondary);
  background-color: rgba(82, 55, 204, 0.15);
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-right: 0.75rem;
}

.login-btn {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  min-width: 90px;
  /* Reduced from 120px */
    padding: 0.3rem 0.8rem;
    /* Reduced from 0.5rem 1.25rem */
    background-color: var(--bg-secondary);
    color: white;
    border: none;
    border-radius: 3rem;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    font-size: 0.85rem;
    /* Same as nav-item font size */
      justify-content: center;
    }
    
    .login-btn:hover {
      background-color: var(--hover-bg-button);
    }
    
    .login-text {
      padding: 0 0.1 rem;
      /* Added small padding */
      }
      
      @media (max-width: 1024px) {
        .nav-menu {
          gap: 1rem;
        }
      
        .nav-item {
          padding: 0.3rem;
          font-size: 0.75rem;
        }
      
        .login-btn {
          padding: 0.3rem 0.75rem;
          /* Reduced padding */
            font-size: 0.75rem;
            /* Match nav-item font size */
            min-width: 90px;
            /* Reduced for smaller screens */
          }
          }
          
          @media (max-width: 768px) {
            .navbar {
              height: 48px;
            }
          
            .desktop-menu {
              display: none;
            }
          
            .left-section {
              gap: 1rem;
              padding-left: 1rem;
            }
          
            .login-btn {
              min-width: 0;
              padding: 0.3rem;
            }
          
            .login-text {
              display: none;
            }
          
            .login-btn i {
              margin: 0;
            }
          }
          
          @media (max-width: 480px) {
            .navbar {
              padding: 0.3rem 0;
            }
          
            .left-section {
              padding-left: 0.5rem;
            }
          
            .logo-img {
              height: 1.75rem;
            }
          
            .nav-right {
              margin-right: 0.5rem;
            }
          }
</style>

