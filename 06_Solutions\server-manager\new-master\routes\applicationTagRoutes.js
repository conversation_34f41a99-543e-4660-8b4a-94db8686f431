const express = require('express');
const router = express.Router();
const {
    removeTagFromApplication,
    getApplicationTags,
    addTagToApplication
} = require('../controllers/applicationTagController');
// const authenticateAdmin = require('../middleware/authenticateAdmin');

/**
 * @route   GET /application-tags/:applicationId
 * @desc    Get all tags for a specific application
 * @access  Public
 */
router.get('/:applicationId', getApplicationTags);

/**
 * @route   POST /application-tags
 * @desc    Add a tag to an application
 * @access  Private (Admin only)
 */
router.post('/',
    // authenticateAdmin,
    addTagToApplication);

/**
 * @route   DELETE /application-tags/:applicationId/:tagId
 * @desc    Remove a tag from an application
 * @access  Private (Admin only)
 */
router.delete('/:applicationId/:tagId',
    // authenticateAdmin,
    removeTagFromApplication);

module.exports = router;
