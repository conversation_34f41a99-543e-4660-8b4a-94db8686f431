const express = require('express');
const router = express.Router();
const { refreshInstances, refreshInstancesAuto } = require('../controllers/refreshController');
// const authenticateAdmin = require('../middleware/authenticateAdmin');

/**
 * @route   GET /refresh
 * @desc    Manual refresh endpoint (SSE) - Admin only
 * @access  Private (Admin only)
 */
router.get('/', 
    // authenticateAdmin, 
    refreshInstances);

/**
 * @route   GET /refresh-auto
 * @desc    Auto refresh endpoint for cron jobs
 * @access  Public
 */
router.get('/auto', refreshInstancesAuto);

module.exports = router;