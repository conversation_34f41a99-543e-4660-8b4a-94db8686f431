const WebSocket = require('ws');

const ws = new WebSocket('ws://localhost:3000');

ws.on('open', () => {
    console.log('🔌 Connected to master server');
    // Subscribe as a monitoring client
    ws.send(JSON.stringify({ type: 'client_subscribe' }));
});

ws.on('message', (data) => {
    const message = JSON.parse(data);
    switch (message.type) {
        case 'health_update':
            console.log(`💓 Health update from ${message.serverId}:`, {
                cpu: message.healthData.cpu?.usage,
                memory: message.healthData.memory?.used,
                time: new Date().toLocaleTimeString()
            });
            break;
        case 'slave_status':
            console.log(`📡 Slave ${message.serverId} is ${message.status}`);
            break;
    }
});

ws.on('close', () => console.log('❌ Disconnected from master'));
ws.on('error', console.error);