const db = require('../db');

console.log('🔄 Starting removal of specific health data tables...');

// List of tables to remove
const tablesToRemove = [
    'health_data_192_168_0_114',
    'health_data_192_168_0_152',
    'health_data_192_168_0_230'
];

// Function to drop a specific table
const removeTable = (tableName) => {
    return new Promise((resolve, reject) => {
        db.run(`DROP TABLE IF EXISTS ${tableName}`, function (err) {
            if (err) {
                console.error(`❌ Error removing ${tableName} table:`, err.message);
                reject(err);
            } else {
                console.log(`✅ Successfully removed ${tableName} table`);
                resolve();
            }
        });
    });
};

// Function to verify table is removed
const verifyTableRemoved = (tableName) => {
    return new Promise((resolve, reject) => {
        db.all(`
            SELECT name 
            FROM sqlite_master 
            WHERE type='table' 
            AND name=?
        `, [tableName], (err, rows) => {
            if (err) {
                console.error(`❌ Error verifying ${tableName} removal:`, err.message);
                reject(err);
            } else {
                if (rows.length === 0) {
                    console.log(`✅ Verification successful - ${tableName} no longer exists`);
                    resolve();
                } else {
                    console.error(`❌ Verification failed - ${tableName} still exists`);
                    reject(new Error(`Table ${tableName} still exists`));
                }
            }
        });
    });
};

// Execute the removal process for all tables
Promise.all(tablesToRemove.map(table => 
    removeTable(table)
        .then(() => verifyTableRemoved(table))
))
    .then(() => {
        console.log('📡 All specified health data tables removed successfully');
        db.close(() => {
            console.log('📡 Database connection closed');
            process.exit(0);
        });
    })
    .catch(error => {
        console.error('❌ Error during table removal process:', error.message);
        db.close(() => {
            console.log('📡 Database connection closed');
            process.exit(1);
        });
    });