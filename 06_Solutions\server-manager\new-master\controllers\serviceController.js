const db = require('../db/db');
const SLAVE_PORT = process.env.SLAVE_PORT;

/**
 * Get all services for all servers or a specific server
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllServices = (req, res) => {
    const { server_id } = req.query;
    
    let query, params = [];
    
    if (server_id) {
        // Get services for a specific server
        query = `
            SELECT ss.*, s.ip as server_ip, s.server_name
            FROM server_services ss
            JOIN servers s ON ss.server_id = s.id
            WHERE ss.server_id = ?
            ORDER BY ss.service_name ASC
        `;
        params = [server_id];
    } else {
        // Get all services for all servers
        query = `
            SELECT ss.*, s.ip as server_ip, s.server_name
            FROM server_services ss
            JOIN servers s ON ss.server_id = s.id
            ORDER BY s.server_name ASC, ss.service_name ASC
        `;
    }

    db.all(query, params, (err, services) => {
        if (err) {
            console.error('Error fetching services:', err.message);
            return res.status(500).json({ 
                success: false, 
                error: 'Failed to fetch services' 
            });
        }

        res.json({
            success: true,
            count: services.length,
            data: services
        });
    });
};

/**
 * Create a new service entry
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createService = (req, res) => {
    const { server_id, service_name } = req.body;
    
    if (!server_id || !service_name) {
        return res.status(400).json({
            success: false,
            error: 'Server ID and service name are required'
        });
    }
    
    // Check if server exists
    db.get('SELECT id FROM servers WHERE id = ?', [server_id], (err, server) => {
        if (err) {
            console.error('Error checking server:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to check server existence'
            });
        }
        
        if (!server) {
            return res.status(404).json({
                success: false,
                error: 'Server not found'
            });
        }
        
        // Insert the new service
        const query = 'INSERT INTO server_services (server_id, service_name) VALUES (?, ?)';
        
        db.run(query, [server_id, service_name], function(err) {
            if (err) {
                // Check for unique constraint violation
                if (err.message.includes('UNIQUE constraint failed')) {
                    return res.status(409).json({
                        success: false,
                        error: 'This service already exists for this server'
                    });
                }
                
                console.error('Error creating service:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to create service'
                });
            }
            
            // Get the created service with server details
            db.get(`
                SELECT ss.*, s.ip as server_ip, s.server_name
                FROM server_services ss
                JOIN servers s ON ss.server_id = s.id
                WHERE ss.id = ?
            `, [this.lastID], (err, service) => {
                if (err) {
                    console.error('Error fetching created service:', err.message);
                    return res.status(201).json({
                        success: true,
                        message: 'Service created successfully',
                        data: { id: this.lastID, server_id, service_name }
                    });
                }
                
                res.status(201).json({
                    success: true,
                    message: 'Service created successfully',
                    data: service
                });
            });
        });
    });
};

/**
 * Delete a service entry
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteService = (req, res) => {
    const { id } = req.params;
    
    // Check if service exists
    db.get('SELECT id FROM server_services WHERE id = ?', [id], (err, service) => {
        if (err) {
            console.error('Error checking service:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to check service existence'
            });
        }
        
        if (!service) {
            return res.status(404).json({
                success: false,
                error: 'Service not found'
            });
        }
        
        // Delete the service
        db.run('DELETE FROM server_services WHERE id = ?', [id], function(err) {
            if (err) {
                console.error('Error deleting service:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to delete service'
                });
            }
            
            res.json({
                success: true,
                message: 'Service deleted successfully',
                data: { id: parseInt(id) }
            });
        });
    });
};

/**
 * Control service (start/stop/restart) on slave server
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const controlService = async (req, res) => {
    const { id } = req.params;
    const { action } = req.body;
    
    if (!action || !['start', 'stop', 'restart'].includes(action)) {
        return res.status(400).json({
            success: false,
            error: 'Valid action (start, stop, or restart) is required'
        });
    }
    
    // Get service details
    db.get(`
        SELECT ss.*, s.ip as server_ip
        FROM server_services ss
        JOIN servers s ON ss.server_id = s.id
        WHERE ss.id = ?
    `, [id], async (err, service) => {
        if (err) {
            console.error('Error fetching service:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to fetch service details'
            });
        }
        
        if (!service) {
            return res.status(404).json({
                success: false,
                error: 'Service not found'
            });
        }
        
        try {
            // Call the slave server to control the service
            const axios = require('axios');
            const response = await axios.post(`http://${service.server_ip}:${SLAVE_PORT}/service-control`, {
                service_name: service.service_name,
                action: action
            }, {
                timeout: 10000
            });
            
            res.json({
                success: true,
                message: `Service ${action} operation completed`,
                data: response.data
            });
        } catch (error) {
            console.error(`Error controlling service on ${service.server_ip}:`, error.message);
            res.status(500).json({
                success: false,
                error: `Failed to ${action} service: ${error.message}`,
                service: service
            });
        }
    });
};

/**
 * Get status of all services or services for a specific server
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllServicesStatus = async (req, res) => {
    const { server_id } = req.query;
    
    let query, params = [];
    
    if (server_id) {
        // Get services for a specific server
        query = `
            SELECT ss.*, s.ip as server_ip, s.server_name
            FROM server_services ss
            JOIN servers s ON ss.server_id = s.id
            WHERE ss.server_id = ?
            ORDER BY ss.service_name ASC
        `;
        params = [server_id];
    } else {
        // Get all services for all servers
        query = `
            SELECT ss.*, s.ip as server_ip, s.server_name
            FROM server_services ss
            JOIN servers s ON ss.server_id = s.id
            ORDER BY s.server_name ASC, ss.service_name ASC
        `;
    }

    db.all(query, params, async (err, services) => {
        if (err) {
            console.error('Error fetching services:', err.message);
            return res.status(500).json({ 
                success: false, 
                error: 'Failed to fetch services' 
            });
        }

        if (!services || services.length === 0) {
            return res.json({
                success: true,
                message: 'No services found',
                count: 0,
                data: []
            });
        }

        // Group services by server IP to minimize network requests
        const servicesByServer = {};
        services.forEach(service => {
            if (!servicesByServer[service.server_ip]) {
                servicesByServer[service.server_ip] = [];
            }
            servicesByServer[service.server_ip].push(service);
        });

        const axios = require('axios');
        const results = [];
        const errors = [];

        // Process each server in parallel
        await Promise.all(Object.entries(servicesByServer).map(async ([serverIp, serverServices]) => {
            // Process services for each server sequentially to avoid overwhelming the slave
            for (const service of serverServices) {
                try {
                    const response = await axios.get(`http://${serverIp}:${SLAVE_PORT}/service-status`, {
                        params: { service_name: service.service_name },
                        timeout: 5000
                    });
                    
                    results.push({
                        ...service,
                        status: response.data
                    });
                } catch (error) {
                    console.error(`Error getting status for service ${service.service_name} on ${serverIp}:`, error.message);
                    
                    // Add to results with error status
                    results.push({
                        ...service,
                        status: {
                            service_name: service.service_name,
                            status: 'unknown',
                            running: false,
                            stopped: false,
                            error: error.message
                        }
                    });
                    
                    errors.push({
                        service_id: service.id,
                        service_name: service.service_name,
                        server_ip: serverIp,
                        error: error.message
                    });
                }
            }
        }));

        // Calculate summary statistics
        const summary = {
            total: results.length,
            running: results.filter(s => s.status && s.status.running).length,
            stopped: results.filter(s => s.status && s.status.stopped).length,
            unknown: results.filter(s => !s.status || s.status.status === 'unknown').length,
            errors: errors.length
        };

        res.json({
            success: true,
            count: results.length,
            summary,
            data: results,
            errors: errors.length > 0 ? errors : undefined
        });
    });
};

/**
 * Update a service entry
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateService = (req, res) => {
    const { id } = req.params;
    const { service_name } = req.body;
    
    if (!service_name || service_name.trim() === '') {
        return res.status(400).json({
            success: false,
            error: 'Service name is required and cannot be empty'
        });
    }
    
    // First check if the service exists
    db.get('SELECT id, server_id, service_name FROM server_services WHERE id = ?', [id], (err, service) => {
        if (err) {
            console.error('Error checking service existence:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to check service existence'
            });
        }
        
        if (!service) {
            return res.status(404).json({
                success: false,
                error: 'Service not found'
            });
        }
        
        // If the service name hasn't changed, return early
        if (service.service_name === service_name) {
            return res.json({
                success: true,
                message: 'No changes were made to the service',
                data: service
            });
        }
        
        // Check if a service with the new name already exists for this server
        db.get(
            'SELECT id FROM server_services WHERE server_id = ? AND service_name = ? AND id != ?',
            [service.server_id, service_name, id],
            (err, existingService) => {
                if (err) {
                    console.error('Error checking for duplicate service:', err.message);
                    return res.status(500).json({
                        success: false,
                        error: 'Failed to check for duplicate service'
                    });
                }
                
                if (existingService) {
                    return res.status(409).json({
                        success: false,
                        error: 'A service with this name already exists for this server'
                    });
                }
                
                // Update the service name
                db.run(
                    'UPDATE server_services SET service_name = ? WHERE id = ?',
                    [service_name, id],
                    function(err) {
                        if (err) {
                            console.error('Error updating service:', err.message);
                            return res.status(500).json({
                                success: false,
                                error: 'Failed to update service'
                            });
                        }
                        
                        // Get the updated service with server details
                        db.get(`
                            SELECT ss.*, s.ip as server_ip, s.server_name
                            FROM server_services ss
                            JOIN servers s ON ss.server_id = s.id
                            WHERE ss.id = ?
                        `, [id], (err, updatedService) => {
                            if (err) {
                                console.error('Error fetching updated service:', err.message);
                                return res.json({
                                    success: true,
                                    message: 'Service updated successfully',
                                    data: { 
                                        id: parseInt(id), 
                                        server_id: service.server_id, 
                                        service_name 
                                    }
                                });
                            }
                            
                            res.json({
                                success: true,
                                message: 'Service updated successfully',
                                data: updatedService
                            });
                        });
                    }
                );
            }
        );
    });
};

module.exports = {
    getAllServices,
    createService,
    updateService,
    deleteService,
    controlService,
    getAllServicesStatus
};




