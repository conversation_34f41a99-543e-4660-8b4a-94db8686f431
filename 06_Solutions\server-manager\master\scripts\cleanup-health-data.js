const { cleanupOldHealthData } = require('../models/healthData');
const db = require('../db');

console.log('🔄 Starting health data cleanup process...');

const retentionDays = process.env.HEALTH_DATA_RETENTION_DAYS || 30;

cleanupOldHealthData(retentionDays)
    .then(results => {
        console.log('\n📊 Cleanup Summary:');
        console.log('==================');
        results.forEach(result => {
            console.log(`Server ${result.server_ip}: ${result.recordsRemoved} records removed`);
        });

        const totalRemoved = results.reduce((sum, result) => sum + result.recordsRemoved, 0);
        console.log('\n✅ Total records removed:', totalRemoved);

        db.close(() => {
            console.log('📡 Database connection closed');
            process.exit(0);
        });
    })
    .catch(error => {
        console.error('❌ Error during cleanup:', error);
        db.close(() => {
            console.log('📡 Database connection closed');
            process.exit(1);
        });
    });