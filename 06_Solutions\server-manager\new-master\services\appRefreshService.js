const db = require('../db/db');


async function updateApplicationsForServer(serverId, instancesData) {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            db.run('BEGIN TRANSACTION');

            // Get existing applications for this server
            db.all('SELECT id, path FROM applications WHERE server_id = ?', [serverId], (err, existingApps) => {
                if (err) {
                    db.run('ROLLBACK');
                    return reject(err);
                }
                const existingPathsMap = new Map();
                existingApps.forEach(app => {
                    existingPathsMap.set(app.path, app.id);
                });
                const newPathsSet = new Set();
                instancesData.forEach(instance => {
                    newPathsSet.add(instance.app_path);
                });
                const insertPromises = [];
                instancesData.forEach(instance => {
                    if (!existingPathsMap.has(instance.app_path)) {
                        insertPromises.push(
                            new Promise((resolveInsert) => {
                                db.run(
                                    'INSERT INTO applications (path, server_id) VALUES (?, ?)',
                                    [instance.app_path, serverId],
                                    function (err) {
                                        if (err) {
                                            console.error(`Error inserting application: ${err.message}`);
                                            return resolveInsert(); // Continue even on error
                                        }
                                        const appId = this.lastID;
                                        db.run(
                                            'INSERT INTO aras_versions (application_id, aras_version, build_number) VALUES (?, NULL, NULL)',
                                            [appId],
                                            (err) => {
                                                if (err) {
                                                    console.error(`Error inserting into aras_versions: ${err.message}`);
                                                }
                                                resolveInsert();
                                            }
                                        );
                                    }
                                );
                            })
                        );
                    }
                });

                const deletePromises = [];
                existingPathsMap.forEach((appId, path) => {
                    if (!newPathsSet.has(path)) {
                        deletePromises.push(
                            new Promise((resolveDelete) => {
                                db.run(
                                    'DELETE FROM applications WHERE id = ?',
                                    [appId],
                                    function (err) {
                                        if (err) {
                                            console.error(`Error deleting application: ${err.message}`);
                                            return resolveDelete(); // Continue even on error
                                        }
                                        db.run(
                                            'DELETE FROM aras_versions WHERE application_id = ?',
                                            [appId],
                                            (err) => {
                                                if (err) {
                                                    console.error(`Error deleting from aras_versions: ${err.message}`);
                                                }
                                                resolveDelete();
                                            }
                                        );
                                    }
                                );
                            })
                        );
                    }
                });

                Promise.all([...insertPromises, ...deletePromises])
                    .then(() => {
                        db.run('COMMIT', (err) => {
                            if (err) {
                                console.error(`Error committing transaction: ${err.message}`);
                                db.run('ROLLBACK');
                                return reject(err);
                            }
                            resolve();
                        });
                    })
                    .catch(error => {
                        console.error(`Error in update operations: ${error.message}`);
                        db.run('ROLLBACK');
                        reject(error);
                    });
            });
        });
    });
}

module.exports = {
    updateApplicationsForServer
};
