// routes/instance.js
const express = require('express');
const router = express.Router();
const { getPublicInstances, getAllInstances } = require('../controllers/instanceController');
const authenticateAdmin = require('../middlewares/authenticateAdmin');

// Public API: Returns only public instance data.
router.get('/public', getPublicInstances);

// Admin API: Returns all instance data (protected).
router.get('/admin', authenticateAdmin, getAllInstances);

module.exports = router;
