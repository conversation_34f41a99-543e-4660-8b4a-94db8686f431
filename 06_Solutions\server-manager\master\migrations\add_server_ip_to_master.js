const db = require('../db');

console.log('🔄 Starting migration to add server_ip column to master_server table...');

db.serialize(() => {
    // Step 1: Add new column
    db.run(`ALTER TABLE master_server ADD COLUMN server_ip TEXT`, (err) => {
        if (err) {
            console.error('❌ Error adding server_ip column:', err.message);
            process.exit(1);
        }
        console.log('✅ Added server_ip column to master_server table');

        // Step 2: Import the getLocalIPAddress utility
        const { getLocalIPAddress } = require('../utils/networkUtils');

        // Step 3: Update the existing record with the server IP
        getLocalIPAddress()
            .then(localIP => {
                db.run(
                    `UPDATE master_server SET server_ip = ? WHERE id = 0`,
                    [localIP],
                    function(err) {
                        if (err) {
                            console.error('❌ Error updating server_ip:', err.message);
                            process.exit(1);
                        }
                        console.log(`✅ Updated server_ip to: ${localIP}`);

                        // Step 4: Verify the update
                        db.get('SELECT * FROM master_server WHERE id = 0', (err, row) => {
                            if (err) {
                                console.error('❌ Error verifying update:', err.message);
                                process.exit(1);
                            }
                            console.log('📝 Current master_server data:', row);
                            console.log('✅ Migration completed successfully');
                            db.close(() => {
                                console.log('📡 Database connection closed');
                                process.exit(0);
                            });
                        });
                    }
                );
            })
            .catch(error => {
                console.error('❌ Error getting local IP:', error.message);
                db.close(() => {
                    process.exit(1);
                });
            });
    });
});