const fs = require("fs");
const xml2js = require("xml2js");
const configPath = "C:/Windows/System32/inetsrv/config/applicationHost.config";
async function readXmlFile(filePath) {
    try {
        const xmlData = fs.readFileSync(filePath, "utf-8");
        const parser = new xml2js.Parser({
            explicitArray: false,
            mergeAttrs: true,
            explicitRoot: false
        });
        return await parser.parseStringPromise(xmlData);
    } catch (error) {
        console.error("❌ Error reading XML file:", error);
        return null;
    }
}
async function getInstancesFromXML() {
    const xmlData = await readXmlFile(configPath);
    if (!xmlData) {
        throw new Error("Failed to parse XML configuration.");
    }
    const systemAppHost = xmlData["system.applicationHost"];
    if (!systemAppHost) {
        throw new Error("'system.applicationHost' section not found in XML.");
    }
    const sites = systemAppHost.sites.site;
    const site = Array.isArray(sites)
        ? sites.find(s => s.name === "Default Web Site")
        : sites;
    if (!site) {
        throw new Error("'Default Web Site' not found in IIS configuration.");
    }

    let instances = [];
    if (site.application) {
        const applications = Array.isArray(site.application) ? site.application : [site.application];

        // First, identify all client sub-instances and their parent paths
        const clientInstances = new Set();
        applications.forEach(app => {
            const appPath = app.path;
            if (appPath === "/") return;

            if (appPath.endsWith("/Client")) {
                // Extract parent path (remove "/Client" suffix)
                const parentPath = appPath.substring(0, appPath.length - 7);
                clientInstances.add(parentPath);
            }
        });

        // Then process all applications, skipping parent paths that have client instances
        applications.forEach(app => {
            const appPath = app.path;
            if (appPath === "/") return;

            const isFirstLevel = appPath.split("/").length === 2;
            const isClientSubInstance = appPath.endsWith("/Client");

            // Include if it's a client sub-instance OR a first-level that doesn't have a client
            if (isClientSubInstance || (isFirstLevel && !clientInstances.has(appPath))) {
                instances.push({
                    app_path: appPath
                });
            }
        });
    }

    return { instances };
}
async function handleGetRequest(req, res) {
    try {
        const instanceData = await getInstancesFromXML();
        res.json(instanceData);
    } catch (error) {
        console.error("❌ Error in handleGetRequest:", error.message);
        console.error("Error fetching instance data:", error);
        res.status(500).json({ error: error.message });
    }
}
module.exports = { getInstancesFromXML, handleGetRequest };
if (require.main === module) {
    getInstancesFromXML()
        .then(data => console.log(JSON.stringify(data, null, 2)))
        .catch(err => console.error(err));
}
