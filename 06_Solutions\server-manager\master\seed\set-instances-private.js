const db = require('../db');

console.log('🔄 Starting to set all instances to private (is_public = 0)...');

const updateQuery = `UPDATE INSTANCE_DATA SET is_public = 0`;

db.run(updateQuery, function(err) {
    if (err) {
        console.error('❌ Error updating instances:', err.message);
        process.exit(1);
    }
    
    console.log(`✅ Successfully updated ${this.changes} instances to private`);
    
    // Verify the update
    db.all('SELECT * FROM INSTANCE_DATA WHERE is_public != 0', (err, rows) => {
        if (err) {
            console.error('❌ Error verifying update:', err.message);
            process.exit(1);
        }
        
        if (rows.length === 0) {
            console.log('✅ Verification successful - All instances are now private');
            
            // Show current state
            db.all('SELECT server_ip, instance_name, is_public FROM INSTANCE_DATA', (err, allRows) => {
                if (err) {
                    console.error('❌ Error fetching final state:', err.message);
                } else {
                    console.log('\n📊 Current instances state:');
                    console.table(allRows);
                }
                
                // Close the database connection
                db.close(() => {
                    console.log('📡 Database connection closed.');
                    process.exit(0);
                });
            });
        } else {
            console.error('❌ Verification failed - Some instances are still public');
            console.log('Remaining public instances:', rows);
            db.close(() => {
                console.log('📡 Database connection closed.');
                process.exit(1);
            });
        }
    });
});