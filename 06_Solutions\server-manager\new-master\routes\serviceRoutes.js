const express = require('express');
const router = express.Router();
const {
    getAllServices,
    // getServiceById,
    createService,
    updateService,
    deleteService,
    // getServiceStatus,
    controlService,
    getAllServicesStatus
} = require('../controllers/serviceController');
// const authenticateAdmin = require('../middleware/authenticateAdmin');

/**
 * @route   GET /services
 * @desc    Get all services or services for a specific server
 * @access  Public
 */
router.get('/', getAllServices);

// /**
//  * @route   GET /services/:id
//  * @desc    Get service by ID
//  * @access  Public
//  */
// router.get('/:id', getServiceById);

/**
 * @route   POST /services
 * @desc    Create a new service entry
 * @access  Private (Admin only)
 */
router.post('/',
    // authenticateAdmin,
    createService);

/**
 * @route   PUT /services/:id
 * @desc    Update a service entry
 * @access  Private (Admin only)
 */
router.put('/:id',
    // authenticateAdmin,
    updateService);

/**
 * @route   DELETE /services/:id
 * @desc    Delete a service entry
 * @access  Private (Admin only)
 */
router.delete('/:id',
    // authenticateAdmin,
    deleteService);

// /**
//  * @route   GET /services/:id/status
//  * @desc    Get service status from slave server
//  * @access  Public
//  */
// router.get('/:id/status', getServiceStatus);

/**
 * @route   POST /services/:id/control
 * @desc    Control service (start/stop/restart) on slave server
 * @access  Private (Admin only)
 */
router.post('/control/:id',
    // authenticateAdmin,
    controlService);

/**
 * @route   GET /services/status/all
 * @desc    Get status of all services or services for a specific server
 * @access  Public
 */
router.get('/status/all', getAllServicesStatus);

module.exports = router;


