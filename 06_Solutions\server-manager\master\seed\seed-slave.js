const bcrypt = require('bcrypt');
const db = require('../db');

// Desired admin credentials
const server_ip = '*************';
const server_name = `<PERSON><PERSON>'s Server`;

// Hash the password with a salt rounds value of 10

// Insert the admin user into the users table
const insertQuery = `INSERT INTO slave_servers (server_ip, server_name) VALUES (?, ?)`;
db.run(insertQuery, [server_ip, server_name], function (err) {
    if (err) {
        console.error('❌ Error inserting admin user:', err.message);
    } else {
        console.log('✅ Slave server created successfully.');
    }
    // Close the database connection after seeding
    db.close();
});