const WebSocket = require('ws');
const { getHealthData } = require('./healthService');
const db = require('../db');

const PORT = process.env.PORT || 3000;
const WS_PORT = process.env.WS_PORT || 3001;  // WS_PORT is defined but not used correctly
const SLAVE_WS_PORT = process.env.SLAVE_WS_PORT || 3001;

class WebSocketService {
    constructor(server) {
        console.debug('🚀 Initializing WebSocket Service');
        this.wss = new WebSocket.Server({ server });
        this.clients = new Map();
        this.slaveConnections = new Map();
        this.activeSlaveConnectors = new Map();
        console.debug('✅ WebSocket Server initialized on port:', WS_PORT);
        this.initialize();
    }

    initialize() {
        this.wss.on('connection', (ws, req) => {
            const clientIp = req.socket.remoteAddress;
            console.debug('📡 New WebSocket connection from:', clientIp);

            this.handleClientSubscription(ws, clientIp);

            ws.on('message', (message) => {
                try {
                    const data = JSON.parse(message);
                    console.debug('📥 Received message from client:', {
                        ip: clientIp,
                        type: data.type,
                        timestamp: new Date().toISOString()
                    });
                    this.handleMessage(ws, data, clientIp);
                } catch (error) {
                    console.error('❌ WebSocket message handling error:', error);
                }
            });

            ws.on('close', () => {
                console.debug('👋 Client disconnected:', clientIp);
                this.handleDisconnection(ws, clientIp);
            });
        });
    }

    handleMessage(ws, data, clientIp) {
        console.debug('🔍 Processing message:', {
            type: data.type,
            from: clientIp,
            timestamp: new Date().toISOString()
        });

        switch (data.type) {
            case 'client_subscribe':
                if (data.token) {
                    console.debug('🔐 Client subscription with token:', clientIp);
                    this.handleClientSubscription(ws, clientIp);
                } else {
                    console.debug('⚠️ Client subscription without token:', clientIp);
                    ws.close(1008, 'Authentication required');
                }
                break;

            case 'slave_connect':
                this.handleSlaveConnection(ws, data.serverId, clientIp);
                break;

            case 'health_update':
                console.log(`💓 Health update from ${data.serverId}:`, {
                    cpu: data.healthData.cpu?.usage,
                    memory: data.healthData.memory?.used,
                    timestamp: new Date().toISOString()
                });
                this.handleHealthUpdate(data.serverId, data.healthData);
                break;

            default:
                console.warn('⚠️ Unknown message type:', data.type);
        }
    }

    async handleClientSubscription(ws, clientIp) {
        this.clients.set(clientIp, ws);
        console.log(`Client subscribed: ${clientIp}`);

        // Send initial connection acknowledgment
        ws.send(JSON.stringify({
            type: 'connection_ack',
            timestamp: new Date().toISOString()
        }));

        // Connect to all slave servers if this is the first client
        if (this.clients.size === 1) {
            await this.connectToAllSlaves();
        }

        // Send initial health data
        await this.sendInitialHealthData(ws);
    }

    async connectToAllSlaves() {
        return new Promise((resolve, reject) => {
            db.all('SELECT server_ip, server_ip FROM servers WHERE server_type = "slave"', async (err, slaves) => {
                if (err) {
                    console.error('Error fetching slave servers:', err);
                    return reject(err);
                }

                for (const slave of slaves) {
                    await this.connectToSlave(slave.server_ip, slave.server_ip);
                }
                resolve();
            });
        });
    }

    async connectToSlave(slaveIp, serverId) {
        // Don't create duplicate connections
        if (this.activeSlaveConnectors.has(serverId)) {
            return;
        }

        const wsUrl = `ws://${slaveIp}:${SLAVE_WS_PORT}`;
        console.log(`Attempting to connect to slave at ${wsUrl}`);

        const ws = new WebSocket(wsUrl);

        const slaveConnector = {
            ws,
            reconnectAttempts: 0,
            maxReconnectAttempts: 5,
            reconnectTimeout: null,
            ip: slaveIp
        };

        this.activeSlaveConnectors.set(serverId, slaveConnector);

        ws.on('open', () => {
            console.log(`Connected to slave: ${serverId} (${slaveIp})`);
            slaveConnector.reconnectAttempts = 0;
            this.slaveConnections.set(serverId, { ws, ip: slaveIp, lastUpdate: new Date() });
        });

        ws.on('message', (message) => {
            try {
                const data = JSON.parse(message);
                if (data.type === 'health_update') {
                    this.handleHealthUpdate(serverId, data.healthData);
                }
            } catch (error) {
                console.error(`Error handling message from slave ${serverId}:`, error);
            }
        });

        ws.on('close', () => {
            this.handleSlaveDisconnection(serverId, slaveConnector);
        });

        ws.on('error', (error) => {
            console.error(`Error in slave connection ${serverId}:`, error);
            ws.close();
        });
    }

    handleSlaveDisconnection(serverId, slaveConnector) {
        this.slaveConnections.delete(serverId);

        // Only attempt reconnection if we still have clients
        if (this.clients.size > 0 && slaveConnector.reconnectAttempts < slaveConnector.maxReconnectAttempts) {
            slaveConnector.reconnectAttempts++;
            const delay = 5000 * Math.pow(2, slaveConnector.reconnectAttempts - 1);

            console.log(`Attempting to reconnect to slave ${serverId} in ${delay}ms (attempt ${slaveConnector.reconnectAttempts})`);

            slaveConnector.reconnectTimeout = setTimeout(() => {
                this.connectToSlave(slaveConnector.ip, serverId);
            }, delay);
        } else {
            this.activeSlaveConnectors.delete(serverId);
        }
    }

    handleDisconnection(ws, clientIp) {
        // Remove client
        this.clients.delete(clientIp);
        console.log(`Client disconnected: ${clientIp}`);

        // If no more clients, disconnect from all slaves
        if (this.clients.size === 0) {
            this.disconnectAllSlaves();
        }
    }

    disconnectAllSlaves() {
        console.log('No more clients connected. Disconnecting from all slaves...');

        // Close all slave connections
        for (const [serverId, connector] of this.activeSlaveConnectors.entries()) {
            if (connector.reconnectTimeout) {
                clearTimeout(connector.reconnectTimeout);
            }
            if (connector.ws) {
                connector.ws.close();
            }
            this.activeSlaveConnectors.delete(serverId);
            this.slaveConnections.delete(serverId);
        }
    }

    handleHealthUpdate(serverId, healthData) {
        if (this.clients.size === 0) return; // Don't process if no clients

        const serverData = {
            timestamp: new Date().toISOString(),
            servers: { [serverId]: healthData }
        };

        // Broadcast to all connected clients
        this.clients.forEach((ws) => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(serverData));
            }
        });
    }

    async sendInitialHealthData(ws) {
        try {
            const healthData = await getHealthData();
            const serverData = {
                timestamp: new Date().toISOString(),
                servers: {}
            };

            // Get master IP from database
            db.get('SELECT server_ip FROM servers WHERE server_type = "master"', (err, row) => {
                if (err) {
                    console.error('Error getting master IP:', err);
                    return;
                }
                if (row && row.server_ip) {
                    serverData.servers[row.server_ip] = healthData;
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify(serverData));
                    }
                }
            });
        } catch (error) {
            console.error('Error sending initial health data:', error);
        }
    }

    startMasterHealthUpdates() {
        // Reduced interval for more frequent updates
        setInterval(async () => {
            try {
                const healthData = await getHealthData();

                // Get master IP from database
                db.get('SELECT server_ip FROM servers WHERE server_type = "master"', (err, row) => {
                    if (err) {
                        console.error('Error getting master IP:', err);
                        return;
                    }

                    if (row && row.server_ip) {
                        const serverData = {
                            timestamp: new Date().toISOString(),
                            servers: {
                                [row.server_ip]: healthData
                            }
                        };

                        // Broadcast to all connected clients
                        this.clients.forEach((ws) => {
                            if (ws.readyState === WebSocket.OPEN) {
                                ws.send(JSON.stringify(serverData));
                            }
                        });
                    }
                });
            } catch (error) {
                console.error('Error collecting master health data:', error);
            }
        }, 5000); // Update every second
    }
}

module.exports = WebSocketService;












