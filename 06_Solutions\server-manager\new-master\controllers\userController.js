const db = require('../db/db');
const CryptoJS = require('crypto-js');

/**
 * Get all users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllUsers = (req, res) => {
    // Select all users but exclude password field for security
    const query = `
        SELECT id, username, email, created_at
        FROM users
        ORDER BY username ASC
    `;

    db.all(query, [], (err, users) => {
        if (err) {
            console.error('Error fetching users:', err.message);
            return res.status(500).json({ 
                success: false, 
                error: 'Failed to fetch users' 
            });
        }

        res.json({
            success: true,
            count: users.length,
            data: users
        });
    });
};

/**
 * Get user by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getUserById = (req, res) => {
    const { id } = req.params;
    
    // Select user by ID but exclude password field
    db.get(
        'SELECT id, username, email, created_at FROM users WHERE id = ?', 
        [id], 
        (err, user) => {
            if (err) {
                console.error('Error fetching user:', err.message);
                return res.status(500).json({ 
                    success: false, 
                    error: 'Failed to fetch user' 
                });
            }
            
            if (!user) {
                return res.status(404).json({
                    success: false,
                    error: 'User not found'
                });
            }

            res.json({
                success: true,
                data: user
            });
        }
    );
};

/**
 * Hash the password using email as salt
 * @param {string} plainPassword - The plain password
 * @param {string} email - The email (used as the salt)
 * @returns {string} - The hashed password
 */
const hashPassword = (plainPassword, email) => {
    const salt = CryptoJS.SHA256(email).toString();
    let hashedPassword = plainPassword;
    for (let i = 0; i < 1000; i++) {
        hashedPassword = CryptoJS.SHA256(hashedPassword + salt).toString();
    }
    return hashedPassword;
};

/**
 * Create a new user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createUser = (req, res) => {
    const { username, email, password } = req.body;

    // Validate required fields
    if (!username || !email || !password) {
        return res.status(400).json({
            success: false,
            error: 'Username, email, and password are required'
        });
    }

    // Simple email validation
    if (!email.includes('@')) {
        return res.status(400).json({
            success: false,
            error: 'Invalid email format'
        });
    }

    // Hash the password before inserting
    const hashedPassword = hashPassword(password, email);

    const query = `INSERT INTO users (username, email, password) VALUES (?, ?, ?)`;

    db.run(query, [username, email, hashedPassword], function (err) {
        if (err) {
            // Check for unique constraint violations
            if (err.message.includes('UNIQUE constraint failed: users.email')) {
                return res.status(409).json({
                    success: false,
                    error: 'Email already in use'
                });
            } else if (err.message.includes('UNIQUE constraint failed: users.username')) {
                return res.status(409).json({
                    success: false,
                    error: 'Username already in use'
                });
            }

            console.error('Error creating user:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to create user'
            });
        }

        // Get the newly created user (excluding password)
        db.get(
            'SELECT id, username, email, created_at FROM users WHERE id = ?',
            [this.lastID],
            (err, user) => {
                if (err) {
                    console.error('Error fetching created user:', err.message);
                    return res.status(500).json({
                        success: false,
                        error: 'User created but failed to retrieve details'
                    });
                }

                res.status(201).json({
                    success: true,
                    message: 'User created successfully',
                    data: user
                });
            }
        );
    });
};

/**
 * Update an existing user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateUser = (req, res) => {
    const { id } = req.params;
    const { username, email, password } = req.body;

    // Check if at least one field to update is provided
    if (!username && !email && !password) {
        return res.status(400).json({
            success: false,
            error: 'At least one field (username, email, or password) must be provided'
        });
    }

    // First check if the user exists
    db.get('SELECT id FROM users WHERE id = ?', [id], (err, user) => {
        if (err) {
            console.error('Error checking user existence:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to update user'
            });
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                error: 'User not found'
            });
        }

        // Build dynamic update query based on provided fields
        let updateFields = [];
        let params = [];

        if (username) {
            updateFields.push('username = ?');
            params.push(username);
        }

        if (email) {
            // Simple email validation
            if (!email.includes('@steepgraph.com')) {
                return res.status(400).json({
                    success: false,
                    error: 'Invalid email format. Only steepgraph emails are allowed.' // only email with steepgraph peoples are allowed
                });
            }
            updateFields.push('email = ?');
            params.push(email);
        }

        if (password) {
            updateFields.push('password = ?');
            params.push(password);
        }

        // Add id as the last parameter
        params.push(id);

        const query = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;

        db.run(query, params, function (err) {
            if (err) {
                // Check for unique constraint violations
                if (err.message.includes('UNIQUE constraint failed: users.email')) {
                    return res.status(409).json({
                        success: false,
                        error: 'Email already in use'
                    });
                } else if (err.message.includes('UNIQUE constraint failed: users.username')) {
                    return res.status(409).json({
                        success: false,
                        error: 'Username already in use'
                    });
                }

                console.error('Error updating user:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to update user'
                });
            }

            // If no rows were affected but no error occurred, the user exists but no changes were made
            if (this.changes === 0) {
                return res.json({
                    success: true,
                    message: 'No changes were made to the user',
                    data: { id: parseInt(id) }
                });
            }

            // Get the updated user (excluding password)
            db.get(
                'SELECT id, username, email, created_at FROM users WHERE id = ?',
                [id],
                (err, updatedUser) => {
                    if (err) {
                        console.error('Error fetching updated user:', err.message);
                        return res.status(500).json({
                            success: false,
                            error: 'User updated but failed to retrieve details'
                        });
                    }

                    res.json({
                        success: true,
                        message: 'User updated successfully',
                        data: updatedUser
                    });
                }
            );
        });
    });
};

/**
 * Delete a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteUser = (req, res) => {
    const { id } = req.params;

    // First check if the user exists
    db.get('SELECT id FROM users WHERE id = ?', [id], (err, user) => {
        if (err) {
            console.error('Error checking user existence:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to delete user'
            });
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                error: 'User not found'
            });
        }

        // Delete the user
        const query = `DELETE FROM users WHERE id = ?`;

        db.run(query, [id], function (err) {
            if (err) {
                console.error('Error deleting user:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to delete user'
                });
            }

            res.json({
                success: true,
                message: 'User deleted successfully',
                id: parseInt(id)
            });
        });
    });
};

module.exports = {
    getAllUsers,
    getUserById,
    createUser,
    updateUser,
    deleteUser
};

