const db = require('../db');

console.log('🔄 Starting removal of health data...');

// Function to drop health_data table
const removeHealthTable = () => {
    return new Promise((resolve, reject) => {
        db.run('DROP TABLE IF EXISTS health_data', function (err) {
            if (err) {
                console.error('❌ Error removing health_data table:', err.message);
                reject(err);
            } else {
                console.log('✅ Successfully removed health_data table');
                resolve();
            }
        });
    });
};

// Function to verify table is removed
const verifyTableRemoved = () => {
    return new Promise((resolve, reject) => {
        db.all(`
            SELECT name 
            FROM sqlite_master 
            WHERE type='table' 
            AND name='health_data'
        `, (err, rows) => {
            if (err) {
                console.error('❌ Error verifying table removal:', err.message);
                reject(err);
            } else {
                if (rows.length === 0) {
                    console.log('✅ Verification successful - Health data table no longer exists');
                    resolve();
                } else {
                    console.error('❌ Verification failed - Table still exists');
                    reject(new Error('Table still exists'));
                }
            }
        });
    });
};

// Execute the removal process
removeHealthTable()
    .then(() => verifyTableRemoved())
    .then(() => {
        console.log('📡 Health data table removed successfully');
        db.close(() => {
            console.log('📡 Database connection closed');
            process.exit(0);
        });
    })
    .catch(error => {
        console.error('❌ Error during table removal process:', error.message);
        db.close(() => {
            console.log('📡 Database connection closed');
            process.exit(1);
        });
    });

