<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch, nextTick } from 'vue';
import { userState } from '../main.js';
import { useRouter } from 'vue-router';
import {
  Chart,
  LineController,
  LineElement,
  PointElement,
  LinearScale,
  TimeScale,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import 'chartjs-adapter-date-fns';
import HealthHistoryModal from './HealthHistoryModal.vue';

const VITE_MASTER_URL = import.meta.env.VITE_MASTER_URL;
const VITE_WS_MASTER_URL = import.meta.env.VITE_WS_MASTER_URL;
const router = useRouter();

// Add authentication check
const checkAuth = () => {
  if (!userState.isLoggedIn || !userState.user?.token) {
    router.push('/');
    return false;
  }
  return true;
};

// Register Chart.js components
Chart.register(
  LineController,
  LineElement,
  PointElement,
  LinearScale,
  TimeScale,
  Title,
  Toolt<PERSON>,
  Legend,
  Filler
);

// Add this function to create consistent chart configuration
const createChartConfig = (metric, data, options = {}) => {
  return {
    type: 'line',
    data: {
      datasets: [{
        label: metric,
        data: data,
        borderColor: options.color,
        backgroundColor: `${options.color}33`,
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointRadius: 0
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          enabled: true,
          mode: 'index',
          intersect: false,
          position: 'nearest',
          yAlign: 'bottom',
          caretPadding: 10,
          callbacks: {
            title: (context) => {
              const date = new Date(context[0].parsed.x);
              return date.toLocaleTimeString();
            },
            label: (context) => {
              const server = options.server;
              const metrics = server?.metrics;

              switch (metric) {
                case 'cpu':
                  return `CPU Usage: ${context.parsed.y.toFixed(1)}%`;
                case 'ram':
                  return [
                    `RAM Usage: ${context.parsed.y.toFixed(1)}%`,
                    `Used: ${metrics?.ram?.used?.toFixed(1) || 0}GB`,
                    `Total: ${metrics?.ram?.total?.toFixed(1) || 0}GB`
                  ];
                case 'disk':
                  return [
                    `Disk Usage: ${context.parsed.y.toFixed(1)}%`,
                    `Used: ${metrics?.disk?.used?.toFixed(1) || 0}GB`,
                    `Total: ${metrics?.disk?.total?.toFixed(1) || 0}GB`
                  ];
                case 'network':
                  return [
                    `Total Traffic: ${context.parsed.y.toFixed(1)}MB/s`,
                    `Upload: ${metrics?.network?.up?.toFixed(1) || 0}MB/s`,
                    `Download: ${metrics?.network?.down?.toFixed(1) || 0}MB/s`
                  ];
                case 'gpu':
                  return [
                    `GPU Usage: ${context.parsed.y.toFixed(1)}%`,
                    `Memory: ${metrics?.gpu?.memory?.toFixed(1) || 0}GB`,
                    `Temperature: ${metrics?.gpu?.temperature?.toFixed(1) || 0}°C`
                  ];
                default:
                  return `${metric}: ${context.parsed.y.toFixed(1)}%`;
              }
            }
          }
        }
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'second',
            displayFormats: {
              second: 'HH:mm:ss'
            }
          },
          ticks: {
            display: false  // This hides the x-axis timestamps
          },
          grid: {
            display: false  // This removes the vertical grid lines
          }
        },
        y: {
          beginAtZero: true,
          max: metric === 'network' ? undefined : 100,
          ticks: {
            stepSize: metric === 'network' ? undefined : 20
          }
        }
      },
      animation: false
    }
  };
};

// Add this helper function to safely destroy a chart
const destroyChart = (chartId) => {
  const existingChart = Chart.getChart(chartId);
  if (existingChart) {
    existingChart.destroy();
  }
};

const ws = ref(null);
const servers = ref([]);
const isLoading = ref(true);
const error = ref(null);
const selectedServer = ref('all');
const maximizedChart = ref(null);
const selectedHistoryServer = ref(null);
const showHistoryModal = ref(false);
const serverList = ref([]);
const serverHealthData = ref({});
const chartInstances = ref(new Map());
const maximizedChartInstance = ref(null);

// Add this constant at the top with other constants
const HEALTH_DATA_TIMEOUT_MS = 10000; // 10 seconds

// WebSocket message handler function
const handleWebSocketMessage = (event) => {
  const data = JSON.parse(event.data);

  // Add timestamp to each server's health data
  const timestampedData = {};
  Object.entries(data.servers || {}).forEach(([serverIp, healthData]) => {
    timestampedData[serverIp] = {
      ...healthData,
      timestamp: data.timestamp || new Date().toISOString()
    };
  });

  // Update the health data store
  serverHealthData.value = timestampedData;

  // Check each known server's health status
  serverList.value.forEach(server => {
    if (!serverHealthData.value[server.server_ip]) {
      // If server's health data is not in the WebSocket data, mark it as offline
      updateServerMetrics({
        servers: {
          [server.server_ip]: null // This will trigger offline status
        }
      });
    }
  });

  // Update metrics for servers that have data
  updateServerMetrics(data);
};

// Connect to WebSocket for real-time updates
const connectWebSocket = () => {
  console.debug('🔄 Attempting WebSocket connection...');
  console.debug('📍 WS URL:', import.meta.env.VITE_WS_MASTER_URL);

  if (ws.value) {
    console.debug('🔌 Closing existing WebSocket connection');
    ws.value.close();
  }

  // Create WebSocket connection
  ws.value = new WebSocket(import.meta.env.VITE_WS_MASTER_URL);

  ws.value.onopen = () => {
    console.debug('✅ WebSocket connected successfully');
    if (ws.value && ws.value.readyState === WebSocket.OPEN) {
      const subscribeMessage = {
        type: 'client_subscribe',
        token: userState.user.token
      };
      console.debug('📤 Sending subscription message:', subscribeMessage);
      ws.value.send(JSON.stringify(subscribeMessage));
    }
  };

  ws.value.onmessage = (event) => {
    console.debug('📥 Received WebSocket message:', event.data);
    handleWebSocketMessage(event);
  };

  ws.value.onclose = (event) => {
    console.debug('❌ WebSocket closed:', event.code, event.reason);
    ws.value = null;
    // setTimeout(connectWebSocket, 5000);
  };

  ws.value.onerror = (error) => {
    console.error('🚨 WebSocket error:', error);
  };
};

// Fetch initial server list
const fetchServers = async () => {
  try {
    isLoading.value = true;
    error.value = null;
    const response = await fetch(`${VITE_MASTER_URL}/serverlist`, {
      headers: {
        'Authorization': `Bearer ${userState.user.token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      if (response.status === 401 || response.status === 403) {
        router.push('/');
        throw new Error('Unauthorized access');
      }
      throw new Error('Failed to fetch servers');
    }

    const data = await response.json();
    servers.value = data.servers.map(server => initializeServerMetrics(server));
  } catch (err) {
    console.error('Error fetching servers:', err);
    error.value = 'Failed to load servers';
  } finally {
    isLoading.value = false;
  }
};

// Add new function to fetch health history
const fetchHealthHistory = async (serverIp) => {
  try {
    const response = await fetch(`${VITE_MASTER_URL}/health-history`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userState.user.token}`
      }
    });

    if (!response.ok) throw new Error('Failed to fetch health history');
    const data = await response.json();
    return data[serverIp] || [];
  } catch (error) {
    console.error('Error fetching health history:', error);
    return [];
  }
};

// Component lifecycle hooks
onMounted(async () => {
  if (!checkAuth()) return;

  await fetchServers();
  connectWebSocket();
});

onBeforeUnmount(() => {
  // Clean up all chart instances
  chartInstances.value.forEach((chart, chartId) => {
    destroyChart(chartId);
  });
  chartInstances.value.clear();

  if (maximizedChartInstance.value) {
    maximizedChartInstance.value.destroy();
    maximizedChartInstance.value = null;
  }

  // Close WebSocket connection
  if (ws.value) {
    ws.value.close();
    ws.value = null;
  }
});

// Add computed property for filtered servers
const filteredServers = computed(() => {
  if (selectedServer.value === 'all') {
    return servers.value;
  }
  return servers.value.filter(server => server.ip === selectedServer.value);
});

// Add these functions for chart maximization
const maximizeChart = (server, metric) => {
  if (maximizedChartInstance.value) {
    maximizedChartInstance.value.destroy();
    maximizedChartInstance.value = null;
  }

  maximizedChart.value = {
    server,
    metric,
    chartId: `maximized-${metric.name}-chart`
  };

  nextTick(() => {
    updateCharts(server);
  });
};

const minimizeChart = () => {
  if (maximizedChartInstance.value) {
    maximizedChartInstance.value.destroy();
    maximizedChartInstance.value = null;
  }
  maximizedChart.value = null;
};

// Add emit for modal state changes
const emit = defineEmits(['modal-state-changed']);

// Update the getServerHealthStatus function
const getServerHealthStatus = (metrics, serverIp) => {
  const currentTime = Date.now();
  const serverData = serverHealthData.value[serverIp];

  // Check if we have any health data
  if (!serverData) {
    return {
      status: 'offline',
      color: 'var(--grey)',
      tooltip: 'Server is not sending health data'
    };
  }

  // Check if the health data is stale (more than 10 seconds old)
  const lastUpdateTime = new Date(serverData.timestamp).getTime();
  const timeDifference = currentTime - lastUpdateTime;

  if (timeDifference > HEALTH_DATA_TIMEOUT_MS) {
    return {
      status: 'offline',
      color: 'var(--grey)',
      tooltip: `Server health data is stale (Last update: ${new Date(lastUpdateTime).toLocaleTimeString()})`
    };
  }

  if (!metrics) {
    return {
      status: 'offline',
      color: 'var(--grey)',
      tooltip: 'Server is offline'
    };
  }

  // Define thresholds
  const CRITICAL_CPU = 90;
  const HIGH_CPU = 80;
  const CRITICAL_RAM = 90;
  const HIGH_RAM = 80;
  const CRITICAL_DISK = 90;
  const HIGH_DISK = 80;

  // Get current values
  const cpuUsage = metrics.cpu.usage;
  const ramUsage = metrics.ram.usage;
  const diskUsage = metrics.disk.usage;

  // Check critical conditions
  if (cpuUsage >= CRITICAL_CPU || ramUsage >= CRITICAL_RAM || diskUsage >= CRITICAL_DISK) {
    return {
      status: 'critical',
      color: '#ff3b30',
      tooltip: `Critical: ${cpuUsage >= CRITICAL_CPU ? 'High CPU, ' : ''}${ramUsage >= CRITICAL_RAM ? 'High RAM, ' : ''}${diskUsage >= CRITICAL_DISK ? 'High Disk' : ''}`.replace(/, $/, '')
    };
  }

  // Check high usage conditions
  if (cpuUsage >= HIGH_CPU || ramUsage >= HIGH_RAM || diskUsage >= HIGH_DISK) {
    return {
      status: 'weak',
      color: '#ff9500',
      tooltip: `Weak: ${cpuUsage >= HIGH_CPU ? 'High CPU, ' : ''}${ramUsage >= HIGH_RAM ? 'High RAM, ' : ''}${diskUsage >= HIGH_DISK ? 'High Disk' : ''}`.replace(/, $/, '')
    };
  }

  // Check moderate usage
  if (cpuUsage >= 50 || ramUsage >= 50 || diskUsage >= 50) {
    return {
      status: 'moderate',
      color: 'var(--yellow)',
      tooltip: 'Moderate load'
    };
  }

  // All metrics are good
  return {
    status: 'healthy',
    color: 'var(--primary-light)',
    tooltip: 'All systems normal'
  };
};

// Update the initializeServerMetrics function
const initializeServerMetrics = (server) => {
  const now = Date.now();
  const initialHistory = Array.from({ length: 60 }, (_, i) => ({
    x: now - (59 - i) * 1000,
    y: 0
  }));

  return {
    ip: server.server_ip,
    name: server.server_name,
    health: { status: 'healthy', color: '#00ff00', tooltip: 'All systems normal' },  // Add initial health status
    metrics: {
      cpu: {
        usage: 0,
        history: [...initialHistory]
      },
      ram: {
        usage: 0,
        used: 0,
        total: 0,
        history: [...initialHistory]
      },
      disk: {
        usage: 0,
        used: 0,
        total: 0,
        history: [...initialHistory]
      },
      network: {
        usage: 0,
        up: 0,
        down: 0,
        history: [...initialHistory]
      },
      gpu: {
        usage: 0,
        memory: 0,
        temperature: 0,
        history: [...initialHistory]
      }
    }
  };
};

// Update server metrics with real-time data
const updateServerMetrics = (data) => {
  if (!data.servers) return;

  const now = Date.now();

  servers.value.forEach(server => {
    const serverData = data.servers[server.ip];

    // If no data for this server, mark metrics as offline
    if (!serverData) {
      server.status = 'offline';
      return;
    }

    const updateHistory = (currentHistory, newValue) => {
      const newHistory = [...currentHistory.slice(-59), newValue];
      return newHistory;
    };

    // Update metrics with timestamps
    server.metrics.cpu.usage = serverData.cpu.usage;
    server.metrics.cpu.history = updateHistory(server.metrics.cpu.history, {
      x: now,
      y: serverData.cpu.usage
    });

    server.metrics.ram.used = serverData.memory.used;
    server.metrics.ram.total = serverData.memory.total;
    server.metrics.ram.usage = serverData.memory.usage;
    server.metrics.ram.history = updateHistory(server.metrics.ram.history, {
      x: now,
      y: serverData.memory.usage
    });

    server.metrics.disk.used = serverData.disk.used;
    server.metrics.disk.total = serverData.disk.total;
    server.metrics.disk.usage = serverData.disk.usage;
    server.metrics.disk.history = updateHistory(server.metrics.disk.history, {
      x: now,
      y: serverData.disk.usage
    });

    const networkTotal = serverData.network.download_speed + serverData.network.upload_speed;
    server.metrics.network.up = serverData.network.upload_speed;
    server.metrics.network.down = serverData.network.download_speed;
    server.metrics.network.history = updateHistory(server.metrics.network.history, {
      x: now,
      y: networkTotal
    });

    server.metrics.gpu.usage = serverData.gpu.usage;
    server.metrics.gpu.memory = serverData.gpu.memoryUsed;
    server.metrics.gpu.temperature = serverData.gpu.temperature;
    server.metrics.gpu.history = updateHistory(server.metrics.gpu.history, {
      x: now,
      y: serverData.gpu.usage
    });

    // Update server health status
    server.health = getServerHealthStatus(server.metrics);

    updateCharts(server);
  });
};

// Update chart data
const updateCharts = (server) => {
  if (!server || !server.metrics) return;

  const metrics = ['cpu', 'ram', 'disk', 'network', 'gpu'];

  metrics.forEach(metric => {
    const chartId = `${metric}-chart-${server.ip}`;
    const chartCanvas = document.getElementById(chartId);

    if (chartCanvas) {
      destroyChart(chartId);

      const ctx = chartCanvas.getContext('2d');
      const chartData = server.metrics[metric].history;
      const chartOptions = {
        color: getMetricColor(metric),
        server: server // Pass server data to the chart config
      };

      if (metric === 'network') {
        const maxValue = Math.max(...chartData.map(h => h.y)) * 1.2;
        chartOptions.yMax = maxValue < 1 ? 1 : maxValue;
      }

      const newChart = new Chart(ctx, createChartConfig(metric, chartData, chartOptions));
      chartInstances.value.set(chartId, newChart);
    }

    // Update maximized chart tooltip similarly
    if (maximizedChart.value?.server?.ip === server.ip &&
      maximizedChart.value?.metric?.name === metric) {
      const maximizedCanvas = document.getElementById(maximizedChart.value.chartId);

      if (maximizedCanvas && server.metrics[metric]) {
        if (maximizedChartInstance.value) {
          maximizedChartInstance.value.destroy();
          maximizedChartInstance.value = null;
        }

        const ctx = maximizedCanvas.getContext('2d');
        maximizedChartInstance.value = new Chart(ctx, {
          type: 'line',
          data: {
            datasets: [{
              label: maximizedChart.value.metric.label,
              data: server.metrics[metric].history,
              borderColor: maximizedChart.value.metric.color,
              backgroundColor: `${maximizedChart.value.metric.color}33`,
              borderWidth: 2,
              fill: true,
              tension: 0.4,
              pointRadius: 0
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: { display: false },
              tooltip: {
                enabled: true,
                mode: 'index',
                intersect: false,
                position: 'nearest',
                yAlign: 'bottom',
                caretPadding: 10,
                callbacks: {
                  title: (context) => {
                    const date = new Date(context[0].parsed.x);
                    return date.toLocaleTimeString();
                  },
                  label: (context) => {
                    const metrics = server.metrics;
                    switch (metric) {
                      case 'cpu':
                        return `CPU Usage: ${context.parsed.y.toFixed(1)}%`;
                      case 'ram':
                        return [
                          `RAM Usage: ${context.parsed.y.toFixed(1)}%`,
                          `Used: ${metrics.ram.used.toFixed(1)}GB`,
                          `Total: ${metrics.ram.total.toFixed(1)}GB`
                        ];
                      case 'disk':
                        return [
                          `Disk Usage: ${context.parsed.y.toFixed(1)}%`,
                          `Used: ${metrics.disk.used.toFixed(1)}GB`,
                          `Total: ${metrics.disk.total.toFixed(1)}GB`
                        ];
                      case 'network':
                        return [
                          `Total Traffic: ${context.parsed.y.toFixed(1)}MB/s`,
                          `Upload: ${metrics.network.up.toFixed(1)}MB/s`,
                          `Download: ${metrics.network.down.toFixed(1)}MB/s`
                        ];
                      case 'gpu':
                        return [
                          `GPU Usage: ${context.parsed.y.toFixed(1)}%`,
                          `Memory: ${metrics.gpu.memory.toFixed(1)}GB`,
                          `Temperature: ${metrics.gpu.temperature.toFixed(1)}°C`
                        ];
                      default:
                        return `${metric}: ${context.parsed.y.toFixed(1)}%`;
                    }
                  }
                }
              }
            },
            scales: {
              x: {
                type: 'time',
                time: {
                  unit: 'second',
                  stepSize: 1,
                  displayFormats: {
                    second: 'HH:mm:ss'
                  }
                },
                ticks: {
                  maxRotation: 0,
                  autoSkip: true,
                  maxTicksLimit: 10,
                  display: true  // Keep timestamps visible in maximized view
                },
                grid: {
                  display: true  // Keep grid lines in maximized view
                }
              },
              y: {
                beginAtZero: true,
                max: metric === 'network' ? undefined : 100,
                ticks: {
                  stepSize: metric === 'network' ? undefined : 20
                }
              }
            },
            animation: false
          }
        });
      }
    }
  });
};

const initializeCharts = (server) => {
  const metrics = ['cpu', 'ram', 'disk', 'network', 'gpu'];

  metrics.forEach(metric => {
    const chartId = `${metric}-chart-${server.ip}`;
    const chartCanvas = document.getElementById(chartId);
    if (!chartCanvas) return;

    // Destroy any existing chart
    destroyChart(chartId);

    const ctx = chartCanvas.getContext('2d');
    const chartOptions = {
      color: getMetricColor(metric)
    };

    if (metric === 'network') {
      chartOptions.yMax = 1;
    }

    const newChart = new Chart(ctx, createChartConfig(metric, server.metrics[metric].history, chartOptions));
    chartInstances.value.set(chartId, newChart);
  });
};

const showHistory = async (server) => {
  selectedHistoryServer.value = server;
  showHistoryModal.value = true;
  emit('modal-state-changed', true);

  // Fetch and pass history data to modal
  const historyData = await fetchHealthHistory(server.ip);
  selectedHistoryServer.value = {
    ...server,
    historyData
  };
};

const closeHistory = () => {
  showHistoryModal.value = false;
  emit('modal-state-changed', false);
};

const getMetricTooltip = (server, type) => {
  if (!server?.metrics?.[type]) return '';

  switch (type) {
    case 'cpu':
      return `CPU Usage: ${server.metrics.cpu.usage?.toFixed(1) || 0}%`;
    case 'ram':
      return `Memory Usage: ${server.metrics.ram.usage?.toFixed(1) || 0}%\nUsed: ${server.metrics.ram.used?.toFixed(1) || 0}GB\nTotal: ${server.metrics.ram.total?.toFixed(1) || 0}GB`;
    case 'disk':
      return `Disk Usage: ${server.metrics.disk.usage?.toFixed(1) || 0}%\nUsed: ${server.metrics.disk.used?.toFixed(1) || 0}GB\nTotal: ${server.metrics.disk.total?.toFixed(1) || 0}GB`;
    case 'network':
      return `Network Traffic\nUpload: ${server.metrics.network.up?.toFixed(1) || 0}MB/s\nDownload: ${server.metrics.network.down?.toFixed(1) || 0}MB/s`;
    case 'gpu':
      return `GPU Usage: ${server.metrics.gpu.usage?.toFixed(1) || 0}%\nMemory: ${server.metrics.gpu.memory?.toFixed(1) || 0}GB\nTemperature: ${server.metrics.gpu.temperature?.toFixed(1) || 0}°C`;
    default:
      return '';
  }
};

// Helper function to get metric colors
const getMetricColor = (metric) => {
  const colors = {
    cpu: '#FF6384',    // Red
    ram: '#36A2EB',    // Blue
    disk: '#4BC0C0',   // Teal
    network: '#9966FF', // Purple
    gpu: '#FF9F40'     // Orange
  };
  return colors[metric] || '#36A2EB';
};
</script>

<template>
  <div class="monitoring-container">
    <div class="monitoring-header-wrapper">
      <div class="monitoring-header">
        <div class="header-left">
          <h1>System Health Monitor</h1>
        </div>
        <div class="header-actions">
          <div class="server-filter">
            <span class="filter-label">Filter Server:</span>
            <select v-model="selectedServer" class="server-select">
              <option value="all">All Servers</option>
              <option v-for="server in servers" :key="server.ip" :value="server.ip">
                {{ server.name }} ({{ server.ip }})
              </option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <div class="monitoring-content">
      <!-- Loading state -->
      <div v-if="isLoading" class="loading-state">
        Loading servers...
      </div>

      <!-- Error state -->
      <div v-else-if="error" class="error-state">
        {{ error }}
      </div>

      <!-- No servers state -->
      <div v-else-if="servers.length === 0" class="empty-state">
        No servers found
      </div>

      <!-- Servers grid -->
      <div v-else class="servers-grid">
        <div v-for="server in filteredServers" :key="server.ip" class="server-card">
          <div class="server-header">
            <div class="server-info">
              <h2>{{ server.name }}</h2>
              <span class="server-ip">{{ server.ip }}</span>
            </div>
            <div class="server-actions">
              <div class="health-status-container" :title="getServerHealthStatus(server.metrics, server.ip).tooltip">
                <div class="health-status-indicator" :class="getServerHealthStatus(server.metrics, server.ip).status">
                  <span class="status-dot"></span>
                  <span class="status-text">{{ getServerHealthStatus(server.metrics, server.ip).status }}</span>
                </div>
              </div>
              <button class="history-button" @click="showHistory(server)">
                <i class="pi pi-history"></i>
                History
              </button>
            </div>
          </div>

          <div class="metrics-grid">
            <!-- CPU Metric -->
            <div class="metric-card">
              <div class="metric-header">
                <div class="metric-title">
                  <i class="pi pi-microchip"></i>
                  <span>CPU Usage</span>
                </div>
                <button class="maximize-button"
                  @click="maximizeChart(server, { name: 'cpu', label: 'CPU Usage', color: '#FF6384' })">
                  <i class="pi pi-window-maximize"></i>
                </button>
              </div>
              <div class="metric-chart">
                <canvas :id="'cpu-chart-' + server.ip"></canvas>
              </div>
              <div class="metric-value" :title="getMetricTooltip(server, 'cpu')">
                {{ server.metrics?.cpu?.usage?.toFixed(1) || '0' }}%
              </div>
            </div>

            <!-- RAM Metric -->
            <div class="metric-card">
              <div class="metric-header">
                <div class="metric-title">
                  <i class="pi pi-server"></i>
                  <span>Memory</span>
                </div>
                <button class="maximize-button"
                  @click="maximizeChart(server, { name: 'ram', label: 'RAM Usage', color: '#36A2EB' })">
                  <i class="pi pi-window-maximize"></i>
                </button>
              </div>
              <div class="metric-chart">
                <canvas :id="'ram-chart-' + server.ip"></canvas>
              </div>
              <div class="metric-value" :title="getMetricTooltip(server, 'ram')">
                {{ server.metrics?.ram?.used?.toFixed(1) || '0' }}GB / {{ server.metrics?.ram?.total?.toFixed(1) || '0'
                }}GB
              </div>
            </div>

            <!-- Disk Metric -->
            <div class="metric-card">
              <div class="metric-header">
                <div class="metric-title">
                  <i class="pi pi-database"></i>
                  <span>Disk Usage</span>
                </div>
                <button class="maximize-button"
                  @click="maximizeChart(server, { name: 'disk', label: 'Disk Usage', color: '#4BC0C0' })">
                  <i class="pi pi-window-maximize"></i>
                </button>
              </div>
              <div class="metric-chart">
                <canvas :id="'disk-chart-' + server.ip"></canvas>
              </div>
              <div class="metric-value" :title="getMetricTooltip(server, 'disk')">
                {{ server.metrics?.disk?.used?.toFixed(1) || '0' }}GB / {{ server.metrics?.disk?.total?.toFixed(1) ||
  '0' }}GB
              </div>
            </div>

            <!-- Network Metric -->
            <div class="metric-card">
              <div class="metric-header">
                <div class="metric-title">
                  <i class="pi pi-wifi"></i>
                  <span>Network</span>
                </div>
                <button class="maximize-button"
                  @click="maximizeChart(server, { name: 'network', label: 'Network', color: '#9966FF' })">
                  <i class="pi pi-window-maximize"></i>
                </button>
              </div>
              <div class="metric-chart">
                <canvas :id="'network-chart-' + server.ip"></canvas>
              </div>
              <div class="metric-value" :title="getMetricTooltip(server, 'network')">
                ↑{{ server.metrics?.network?.up?.toFixed(1) || '0' }}MB/s ↓{{ server.metrics?.network?.down?.toFixed(1)
                  || '0' }}MB/s
              </div>
            </div>

            <!-- GPU Metric -->
            <div class="metric-card">
              <div class="metric-header">
                <div class="metric-title">
                  <i class="pi pi-calculator"></i>
                  <span>GPU</span>
                </div>
                <button class="maximize-button"
                  @click="maximizeChart(server, { name: 'gpu', label: 'GPU Usage', color: '#FF9F40' })">
                  <i class="pi pi-window-maximize"></i>
                </button>
              </div>
              <div class="metric-chart">
                <canvas :id="'gpu-chart-' + server.ip"></canvas>
              </div>
              <div class="metric-value" :title="getMetricTooltip(server, 'gpu')">
                {{ server.metrics?.gpu?.usage?.toFixed(1) || '0' }}% ({{ server.metrics?.gpu?.memory?.toFixed(1) || '0'
                }}GB)
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <HealthHistoryModal v-if="selectedHistoryServer" :server="selectedHistoryServer" :isOpen="showHistoryModal"
      @close="closeHistory" />
  </div>
  <Teleport to="body">
    <div v-if="maximizedChart" class="maximized-chart-overlay">
      <div class="maximized-chart-container">
        <div class="maximized-chart-header">
          <h3>{{ maximizedChart.metric.label }} - {{ maximizedChart.server.name }}</h3>
          <button class="minimize-button" @click="minimizeChart">
            <i class="pi pi-window-minimize"></i>
          </button>
        </div>
        <div class="maximized-chart-content">
          <canvas :id="maximizedChart.chartId"></canvas>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<style scoped>
.monitoring-container {
  padding: 0;
  /* Remove padding from container */
  background: var(--bg-primary);
  min-height: calc(100vh - 60px);
}

.monitoring-header-wrapper {
  position: sticky;
  top: 48px;
  /* Match your navbar height */
  z-index: 100;
  background: var(--bg-primary);
  padding: 0.5rem 1.5rem;
  /* Reduced from 1rem 2rem */
  margin-bottom: 0.5rem;
  /* Reduced from 1rem */
}

.monitoring-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.375rem 0.75rem;
  /* Reduced from 0.5rem 1rem */
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(33, 23, 97, 0.08);
}

.monitoring-content {
  padding: 0 2rem 2rem;
}

.header-left h1 {
  color: var(--primary-dark);
  font-size: 1.25rem;
  /* Reduced from 1.75rem */
  font-weight: 600;
  line-height: 1.2;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  /* Reduced from 1rem */
}

.server-filter {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  background: #f8f9fe;
  /* Light background */
  padding: 0.25rem 0.5rem;
  /* Added padding around entire filter */
  border-radius: 10px;
  /* Added radius to container */
}

.filter-label {
  color: var(--grey);
  font-size: 0.8125rem;
  font-weight: 500;
  padding-left: 0.25rem;
  /* Added small padding */
}

.server-select {
  padding: 0.3rem 1.5rem 0.3rem 0.5rem;
  border: 2px solid var(--light-grey);
  border-radius: 8px;
  /* Added radius to select */
  background: white;
  font-size: 0.8125rem;
  min-width: 180px;
  cursor: pointer;
  color: var(--primary-dark);
  font-weight: 500;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23211761' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  transition: all 0.2s ease;
}

.server-select:hover {
  border-color: var(--primary-light);
  box-shadow: 0 2px 8px rgba(82, 55, 204, 0.1);
}

.server-select:focus {
  outline: none;
  border-color: var(--primary-dark);
  box-shadow: 0 0 0 3px rgba(82, 55, 204, 0.15);
}

.server-select option {
  padding: 0.375rem;
  font-weight: 500;
}

/* Update responsive styles */
@media (max-width: 768px) {
  .monitoring-header-wrapper {
    padding: 0.5rem;
    /* Reduced from 1rem */
  }

  .monitoring-content {
    padding: 0 1rem 1rem;
  }

  .monitoring-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding: 1rem;
  }

  .header-left h1 {
    font-size: 1.125rem;
    /* Reduced from 1.5rem for mobile */
  }

  .server-filter {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .server-select {
    width: 100%;
    min-width: unset;
  }
}

.servers-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: 1fr;
}

.server-card {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  /* Reduced from 1.5rem to 1rem */
  box-shadow: 0 4px 16px rgba(82, 55, 204, 0.08);
}

.server-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  /* Reduced from 1rem */
  padding: 0.25rem 0;
  /* Added small vertical padding */
}

.server-info h2 {
  color: var(--primary-dark);
  font-size: 1.125rem;
  /* Reduced from 1.25rem */
  margin-bottom: 0.125rem;
  /* Reduced from 0.25rem */
  line-height: 1.2;
  /* Added to reduce line height */
}

.server-ip {
  color: var(--text-secondary);
  font-size: 0.8125rem;
  /* Reduced from 0.875rem */
  line-height: 1.2;
  /* Added to reduce line height */
}

.server-status {
  font-weight: bold;
  text-transform: capitalize;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  background: rgba(0, 0, 0, 0.1);
}

.server-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  /* Reduced from 0.75rem */
}

.history-button {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  /* Reduced from 0.375rem */
  padding: 0.25rem 0.5rem;
  /* Reduced from 0.375rem 0.75rem */
  background: #f8f9fe;
  border: none;
  border-radius: 16px;
  color: var(--primary-dark);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.75rem;
  /* Reduced font size */
}

.history-button i {
  font-size: 0.75rem;
  /* Match font size */
}

.history-button:hover {
  background: var(--primary-light);
  color: white;
}

.metrics-grid {
  display: grid;
  gap: 1rem;
  /* Reduced from 1.5rem to 1rem to maintain consistency */
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.metric-card {
  background: #f8f9fe;
  border-radius: 8px;
  padding: 0.75rem;
  /* Reduced from 1rem to 0.75rem for better proportion */
  transition: transform 0.2s, box-shadow 0.2s;
}

/* .metric-card:hover {
                                                                                        transform: translateY(-2px);
                                                                                          box-shadow: 0 6px 20px rgba(82, 55, 204, 0.12);
                                                                                        }
                                                                                        
                                                          */
.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.metric-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-dark);
}

.maximize-button {
  background: none;
  border: none;
  color: var(--primary-dark);
  cursor: pointer;
  padding: 0.25rem;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.maximize-button:hover {
  opacity: 1;
}

.metric-chart {
  height: 150px;
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 8px;
}

.line-chart {
  width: 100%;
  height: 100%;
}

.metric-value {
  font-size: 0.875rem;
  color: var(--primary-dark);
  font-weight: 500;
  text-align: center;
  padding: 0.5rem 0;
}

.history-button {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: #f8f9fe;
  border: none;
  border-radius: 16px;
  color: var(--primary-dark);
  cursor: pointer;
}

/* Add these new styles at the root level */
.maximized-chart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  pointer-events: auto;
}

.maximized-chart-container {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 1200px;
  height: 80vh;
  padding: 1.5rem;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10000;
}

.maximized-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.maximized-chart-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--primary-dark);
}

.maximized-chart-content {
  flex: 1;
  position: relative;
  min-height: 0;
}

.maximized-chart-content canvas {
  width: 100% !important;
  height: 100% !important;
}

.minimize-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: var(--primary-dark);
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.minimize-button:hover {
  opacity: 0.8;
}

.health-status-container {
  display: inline-flex;
  align-items: center;
}

.health-status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  /* Slightly increased horizontal padding to match history button */
  border-radius: 16px;
  /* Matched with history button radius */
  font-size: 0.75rem;
  /* Matched with history button font size */
  font-weight: 500;
  text-transform: capitalize;
  transition: all 0.2s ease;
}

.status-dot {
  width: 6px;
  /* Slightly reduced to match the smaller height */
  height: 6px;
  border-radius: 50%;
}

/* Healthy Status */
.healthy {
  background: #f8f9fe;
  /* Matched with history button background */
  color: var(--primary-dark);
}

.healthy .status-dot {
  background: var(--primary-light);
  box-shadow: 0 0 0 2px rgba(82, 55, 204, 0.2);
}

/* Moderate Status */
.moderate {
  background: #f8f9fe;
  color: #8f7912;
}

.moderate .status-dot {
  background: var(--yellow);
  box-shadow: 0 0 0 2px rgba(224, 192, 47, 0.2);
}

/* Weak Status */
.weak {
  background: #f8f9fe;
  color: #c65d00;
}

.weak .status-dot {
  background: #ff9500;
  box-shadow: 0 0 0 2px rgba(255, 149, 0, 0.2);
}

/* Critical Status */
.critical {
  background: #f8f9fe;
  color: #c41e3a;
}

.critical .status-dot {
  background: #ff3b30;
  box-shadow: 0 0 0 2px rgba(255, 59, 48, 0.2);
}

/* Offline Status */
.offline {
  background: #f8f9fe;
  color: var(--text-secondary);
}

.offline .status-dot {
  background: var(--grey);
  box-shadow: 0 0 0 2px rgba(76, 71, 94, 0.2);
}

/* Add hover effect similar to history button */
.health-status-indicator:hover {
  background: var(--primary-light);
  color: white;
}

.health-status-indicator:hover .status-dot {
  background: white;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}
</style>


