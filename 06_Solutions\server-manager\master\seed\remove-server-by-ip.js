const db = require('../db');

// Get server IP from command line arguments
const serverIp = process.argv[2];

if (!serverIp) {
    console.error('❌ Please provide a server IP address');
    console.log('Usage: node remove-server-by-ip.js <server_ip>');
    process.exit(1);
}

console.log(`🔄 Starting removal of server with IP: ${serverIp}...`);

// Prevent removal of master server (id = 0)
const deleteQuery = `DELETE FROM servers WHERE server_ip = ? AND id != 0`;

db.run(deleteQuery, [serverIp], function(err) {
    if (err) {
        console.error('❌ Error removing server:', err.message);
        process.exit(1);
    }
    
    console.log(`✅ Successfully removed ${this.changes} server(s) with IP: ${serverIp}`);
    
    // Verify the removal
    db.all('SELECT * FROM servers WHERE server_ip = ?', [serverIp], (err, rows) => {
        if (err) {
            console.error('❌ Error verifying removal:', err.message);
            process.exit(1);
        }
        
        if (rows.length === 0) {
            console.log('✅ Verification successful - Server successfully removed');
        } else if (rows[0].id === 0) {
            console.log('ℹ️ Note: Master server cannot be removed');
        } else {
            console.error('❌ Verification failed - Server still exists');
            console.log('Remaining server:', rows);
        }
        
        // Show remaining servers
        db.all('SELECT server_ip, server_name, server_type FROM servers', (err, allRows) => {
            if (err) {
                console.error('❌ Error fetching remaining servers:', err.message);
            } else {
                console.log('\n📊 Remaining servers:');
                console.table(allRows);
            }
            
            // Close the database connection
            db.close(() => {
                console.log('📡 Database connection closed.');
                process.exit(0);
            });
        });
    });
});