const jwt = require('jsonwebtoken');
const db = require('../db/db');

/**
 * Authentication middleware
 * Since all users are admins, this middleware:
 * 1. Verifies the JWT token is valid
 * 2. Checks if the user still exists in the database
 * 3. Attaches fresh user data to the request
 */
function authenticateAdmin(req, res, next) {
    // Check if Authorization header exists
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Authentication required' });
    }

    const token = authHeader.split(' ')[1];

    try {
        // Verify token is valid
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        if (!decoded.id) {
            return res.status(401).json({ error: 'Invalid token format' });
        }
        
        // Verify user still exists in database
        db.get(
            'SELECT id, username, email FROM users WHERE id = ?',
            [decoded.id],
            (err, user) => {
                if (err) {
                    console.error('Database error during authentication:', err.message);
                    return res.status(500).json({ error: 'Internal server error' });
                }
                
                if (!user) {
                    console.log(`User not found in database: ${decoded.id}`);
                    return res.status(401).json({ error: 'User no longer exists' });
                }
                
                // Attach fresh user data to request
                req.user = user;
                
                // Check token expiration time
                const currentTime = Math.floor(Date.now() / 1000);
                if (decoded.exp && decoded.exp < currentTime) {
                    return res.status(401).json({ error: 'Token has expired' });
                }
                
                // User is authenticated, proceed to route handler
                next();
            }
        );
    } catch (error) {
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({ error: 'Token has expired' });
        } else if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({ error: 'Invalid token' });
        }
        
        console.log(`Authentication error: ${error.message}`);
        return res.status(401).json({ error: 'Authentication failed' });
    }
}

module.exports = authenticateAdmin;
