const CryptoJS = require('crypto-js');
const db = require('../db');

// Desired admin credentials
const username = 'admin';
const email = '<EMAIL>';
const plainTextPassword = 'admin';
const role = 'admin';

// Hash the password using email as salt
const hashPassword = (plainPassword, email) => {
    const salt = CryptoJS.SHA256(email).toString();
    let hashedPassword = plainPassword;
    for (let i = 0; i < 1000; i++) {
        hashedPassword = CryptoJS.SHA256(hashedPassword + salt).toString();
    }
    return hashedPassword;
};

const hashedPassword = hashPassword(plainTextPassword, email);

// Insert the admin user into the users table
const insertQuery = `INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)`;
db.run(insertQuery, [username, email, hashedPassword, role], function (err) {
    if (err) {
        console.error('❌ Error inserting admin user:', err.message);
    } else {
        console.log('✅ Admin user created successfully');
    }
    db.close();
});
