const db = require('../db');

console.log('🔄 Starting removal of server_ip from master_server table...');

const updateQuery = `UPDATE master_server SET server_ip = NULL WHERE id = 0`;

db.run(updateQuery, function(err) {
    if (err) {
        console.error('❌ Error removing server_ip:', err.message);
        process.exit(1);
    }
    
    console.log(`✅ Successfully removed server_ip from master_server`);
    
    // Verify the update
    db.get('SELECT * FROM master_server WHERE id = 0', (err, row) => {
        if (err) {
            console.error('❌ Error verifying update:', err.message);
            process.exit(1);
        }
        
        if (!row.server_ip) {
            console.log('✅ Verification successful - server_ip has been removed');
            console.log('📝 Current master_server data:', row);
        } else {
            console.error('❌ Verification failed - server_ip still exists');
        }
        
        // Close the database connection
        db.close(() => {
            console.log('📡 Database connection closed.');
            process.exit(0);
        });
    });
});