const db = require('../db');
const CryptoJS = require('crypto-js');

console.log('🔄 Starting migration to update password hashing...');

// Function to hash password with email
const hashPassword = (plainPassword, email) => {
    const salt = CryptoJS.SHA256(email).toString();
    let hashedPassword = plainPassword;
    for (let i = 0; i < 1000; i++) {
        hashedPassword = CryptoJS.SHA256(hashedPassword + salt).toString();
    }
    return hashedPassword;
};

db.all('SELECT username, email, password FROM users', [], (err, users) => {
    if (err) {
        console.error('❌ Error fetching users:', err.message);
        process.exit(1);
    }

    const updates = users.map(user => {
        if (!user.email) {
            console.warn(`⚠️ User ${user.username} has no email, skipping...`);
            return Promise.resolve();
        }
        
        // For demo purposes, assuming original password is 'admin'
        // In production, you'd need a different approach to handle existing passwords
        const newHash = hashPassword('admin', user.email);
        
        return new Promise((resolve, reject) => {
            db.run(
                'UPDATE users SET password = ? WHERE username = ?',
                [newHash, user.username],
                (err) => {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });
    });

    Promise.all(updates)
        .then(() => {
            console.log('✅ Successfully updated password hashes');
            db.close();
        })
        .catch(err => {
            console.error('❌ Error updating passwords:', err);
            db.close();
        });
});