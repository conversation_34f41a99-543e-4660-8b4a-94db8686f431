<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import Chart from 'chart.js/auto';
import 'chartjs-adapter-date-fns';
import { format, parseISO } from 'date-fns';

const metrics = {
  cpu: {
    label: 'CPU Usage',
    color: '#FF6384',
    unit: '%'
  },
  ram: {
    label: 'RAM Usage',
    color: '#36A2EB',
    unit: '%'
  },
  disk: {
    label: 'Disk Usage',
    color: '#4BC0C0',
    unit: '%'
  },
  network: {
    label: 'Network',
    color: '#9966FF',
    unit: 'Mbps'
  },
  gpu: {
    label: 'GPU Usage',
    color: '#FF9F40',
    unit: '%'
  }
};

const props = defineProps({
  server: {
    type: Object,
    required: true
  },
  isOpen: {
    type: Boolean,
    required: true
  }
});

const emit = defineEmits(['close']);

const selectedMetric = ref('cpu');
const chart = ref(null);
const timeUnit = ref('DAY'); // Default to day view
const isInitializing = ref(false);

const hasHistoryData = computed(() => {
  return props.server?.historyData?.length > 0;
});

const processHistoryData = (data, metric) => {
  return data.map(record => {
    const timestamp = new Date(`${record.date}T${record.time}`).getTime();
    let value = 0;

    switch (metric) {
      case 'cpu':
        value = record.cpu_usage;
        break;
      case 'ram':
        value = record.memory_usage;
        break;
      case 'disk':
        value = record.disk_usage;
        break;
      case 'network':
        value = record.network_download + record.network_upload;
        break;
      case 'gpu':
        value = record.gpu_data?.usage || 0;
        break;
    }

    return {
      timestamp,
      value
    };
  }).sort((a, b) => a.timestamp - b.timestamp);
};

const initializeChart = () => {
  if (!hasHistoryData.value) return;

  const ctx = document.getElementById('historyChart');
  if (!ctx) return;

  if (chart.value) {
    chart.value.destroy();
    chart.value = null;
  }

  try {
    const historyData = props.server.historyData || [];
    const processedData = processHistoryData(historyData, selectedMetric.value);

    // Set width based on number of data points
    const dataPointCount = processedData.length;
    const minWidth = Math.max(dataPointCount * 25, 100); // At least 25px per point
    ctx.parentElement.style.minWidth = `${minWidth}px`;

    const newChart = new Chart(ctx, {
      type: 'line',
      data: {
        datasets: [{
          label: metrics[selectedMetric.value].label,
          data: processedData.map(d => ({
            x: d.timestamp,
            y: d.value
          })),
          borderColor: metrics[selectedMetric.value].color,
          backgroundColor: `${metrics[selectedMetric.value].color}33`,
          fill: true,
          tension: 0.4,
          pointRadius: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        animation: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              title: (context) => {
                const date = new Date(context[0].parsed.x);
                return format(date, 'MMM dd, yyyy HH:mm');
              },
              label: (context) => {
                return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}${metrics[selectedMetric.value].unit}`;
              }
            }
          }
        },
        scales: {
          x: {
            type: 'time',
            time: {
              displayFormats: {
                datetime: 'MMM dd, HH:mm'
              }
            },
            grid: {
              color: '#f0f0f0'
            },
            ticks: {
              maxRotation: 45,
              minRotation: 45,
              color: '#666',
              font: { size: 12 },
              autoSkip: false,
              source: 'data'
            }
          },
          y: {
            beginAtZero: true,
            max: selectedMetric.value === 'network' ? undefined : 100,
            grid: {
              color: '#f0f0f0'
            },
            ticks: {
              stepSize: 10,
              callback: value => `${value}${metrics[selectedMetric.value].unit}`,
              color: '#666',
              font: { size: 12 }
            }
          }
        }
      }
    });

    chart.value = newChart;
  } catch (error) {
    console.error('Failed to initialize chart:', error);
    isInitializing.value = false;
  }
};

const exportChart = () => {
  const canvas = document.getElementById('historyChart');
  const url = canvas.toDataURL('image/png');
  const link = document.createElement('a');
  link.download = `${props.server.name}-${selectedMetric.value}-history.png`;
  link.href = url;
  link.click();
};

const changeMetric = (metric) => {
  selectedMetric.value = metric;
  initializeChart();
};

const changeTimeUnit = (unit) => {
  timeUnit.value = unit;
  initializeChart();
};

onMounted(() => {
  if (props.isOpen) {
    initializeChart();
  }
});

watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    nextTick(() => {
      initializeChart();
    });
  }
});

onUnmounted(() => {
  if (chart.value) {
    try {
      chart.value.destroy();
    } catch (error) {
      console.warn('Failed to destroy chart on unmount:', error);
    }
    chart.value = null;
  }
});
</script>

<template>
  <div v-if="isOpen" class="modal-overlay">
    <div class="modal-container">
      <div class="modal-header">
        <div class="server-info">
          <h2>{{ server.name }}</h2>
          <span class="server-ip">{{ server.ip }}</span>
        </div>
        <button class="close-button" @click="$emit('close')">
          <i class="pi pi-times"></i>
        </button>
      </div>

      <div class="modal-content">
        <div class="controls-container">
          <div class="metrics-menu">
            <button v-for="(metric, key) in metrics" :key="key"
              :class="['metric-button', { active: selectedMetric === key }]" @click="changeMetric(key)">
              {{ metric.label }}
            </button>
          </div>
        </div>

        <div v-if="!hasHistoryData" class="no-data-container">
          <i class="pi pi-chart-line no-data-icon"></i>
          <p class="no-data-text">No health history data available for this server.</p>
          <p class="no-data-subtext">Historical data will appear here once the server starts reporting metrics.</p>
        </div>

        <template v-else>
          <div class="chart-scroll-container">
            <div class="chart-container">
              <div v-if="isInitializing" class="chart-loading">
                <span>Updating chart...</span>
              </div>
              <canvas id="historyChart"></canvas>
            </div>
          </div>

          <div class="actions">
            <button class="export-button" @click="exportChart" title="Export as PNG">
              <i class="pi pi-download"></i>
            </button>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 12px;
  width: 95%;
  max-width: 1400px;
  height: 95vh;
  padding: 1rem 1.5rem;
    /* Reduced top/bottom padding from 1.5rem to 1rem */
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    /* Prevent content overflow */
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 0.5rem;
    /* Reduced from 0.75rem */
    margin-bottom: 0;
    flex-shrink: 0;
  }
  
  .server-info h2 {
    color: var(--primary-dark);
    font-size: 1.25rem;
    margin: 0;
    /* Remove margin */
    line-height: 1.2;
    /* Reduced line height */
  }
  
  .server-ip {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.2;
    /* Reduced line height */
    margin-top: 0.125rem;
    /* Small gap between name and IP */
  }
  
  .close-button {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    transition: color 0.2s;
  }
  
  .close-button:hover {
    color: var(--primary-dark);
  }
  
  .metrics-menu {
    display: flex;
    gap: 0.5rem;
    padding: 0.25rem;
    /* Reduced from 0.5rem */
    background: #f8f9fe;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    /* Reduced from 0.75rem */
    flex-shrink: 0;
  }
  
  .metric-button {
    padding: 0.25rem 0.75rem;
    /* Reduced vertical padding from 0.5rem */
    border: none;
    border-radius: 6px;
    background: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    /* Slightly smaller font size */
  }
  
  .metric-button.active {
    background: white;
    color: var(--primary-dark);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .metric-button:hover:not(.active) {
    background: rgba(255, 255, 255, 0.5);
  }
  
  .controls-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .time-unit-toggle {
    display: flex;
    gap: 0.5rem;
    padding: 0.25rem;
    background: #f8f9fe;
    border-radius: 8px;
    flex-shrink: 0;
  }
  
  .time-unit-button {
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 6px;
    background: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    font-weight: 500;
  }
  
  .time-unit-button.active {
    background: white;
    color: var(--primary-dark);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .time-unit-button:hover:not(.active) {
    background: rgba(255, 255, 255, 0.5);
  }
  
  .modal-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    /* Important to contain the chart */
    min-height: 0;
    /* Required for Firefox */
  }
  
  .chart-scroll-container {
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
    position: relative;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    /* Add padding for scrollbar */
  }
  
  .chart-container {
    height: calc(70vh - 100px);
    /* Adjust height as needed */
  position: relative;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 4px;
  z-index: 10;
}

canvas {
  width: 100% !important;
  height: 100% !important;
}

.actions {
  position: absolute;
  right: 2rem;
  bottom: 0.5rem;
  z-index: 10;
}

.export-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--primary-dark);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.export-button:hover {
  background: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.export-button i {
  font-size: 1.25rem;
}

/* Hide the "Export as PNG" text */
.export-button span {
  display: none;
}

/* Add to your existing styles */
.zoom-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  z-index: 1;
}

@media (max-width: 768px) {
  .modal-container {
    width: 100%;
    height: 100vh;
    border-radius: 0;
    padding: 1rem;
  }

  .metrics-menu {
    flex-wrap: wrap;
  }

  .metric-button {
    flex: 1 1 calc(50% - 0.5rem);
    text-align: center;
  }

  .chart-container {
    padding-bottom: 0.8rem;
  }

  .actions {
    right: 1rem;
    bottom: 0.25rem;
  }

  .export-button {
    width: 40px;
    height: 40px;
  }
}

/* Add these new styles */
.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.no-data-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-data-text {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.no-data-subtext {
  font-size: 0.875rem;
  max-width: 300px;
  line-height: 1.4;
}
</style>

