// routes/admin.js
const express = require('express');
const { body, validationResult } = require('express-validator');
const authenticateAdmin = require('../middlewares/authenticateAdmin');
const { updateInstance, updateServer } = require('../controllers/adminController');

const router = express.Router();

// Update instance endpoint
router.put(
    '/admin/instances/:id',
    authenticateAdmin,
    [
        body('instance_title').optional().isString(),
        body('instance_description').optional().isString(),
        body('instance_owner').optional().isString(),
        body('is_public').optional().isBoolean()
    ],
    (req, res, next) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        next();
    },
    updateInstance
);

// Update server endpoint
router.put(
    '/admin/servers/:id',
    authenticateAdmin,
    [
        body('server_name').notEmpty().withMessage("server_name is required and must be a string")
    ],
    (req, res, next) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        next();
    },
    updateServer
);

module.exports = router;
