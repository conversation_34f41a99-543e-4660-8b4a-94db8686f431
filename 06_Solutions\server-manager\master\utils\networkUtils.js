const os = require('os');
const db = require('../db');

async function updateMasterServerIP(ip) {
    console.log("Attempting to update master server IP...");
    return new Promise((resolve, reject) => {
        db.get('SELECT server_ip FROM servers WHERE id = 0', (err, row) => {
            if (err) {
                console.error('❌ Error checking master server IP:', err.message);
                return resolve(); // Continue even if check fails
            }

            // Only update if IP is not set
            if (!row || !row.server_ip) {
                db.run('INSERT OR REPLACE INTO servers (id, server_ip, server_name, server_type) VALUES (0, ?, "Master Server", "master")', [ip], function (err) {
                    if (err) {
                        console.error('❌ Error updating master server IP:', err.message);
                    } else {
                        console.log('✅ Master server IP updated automatically:', ip);
                    }
                    resolve();
                });
            } else {
                resolve();
            }
        });
    });
}

async function getLocalIPAddress() {
    try {
        console.log("Attempting to determine default gateway...");
        // Get all network interfaces
        const nets = os.networkInterfaces();
        const allIPs = [];
        
        // Collect all IPv4 addresses
        for (const iface of Object.values(nets)) {
            for (const addr of iface) {
                if (addr.family === 'IPv4' && !addr.internal) {
                    allIPs.push(addr.address);
                }
            }
        }

        console.log("Available IPs:", allIPs);

        // Priority order for IP addresses
        const priorities = [
            ip => ip.startsWith('192.168.0.'),
            ip => ip.startsWith('192.168.56.'),
            ip => ip.startsWith('192.168.'),
            ip => true  // fallback to any IP
        ];

        for (const priority of priorities) {
            const found = allIPs.find(priority);
            if (found) {
                console.log("Selected IP:", found);
                // Automatically update the master server IP when found
                await updateMasterServerIP(found);
                return found;
            }
        }

        // Final fallback
        const defaultIP = process.env.DEFAULT_MASTER_IP || '*************';
        console.log("Using fallback IP:", defaultIP);
        // Update with fallback IP as well
        await updateMasterServerIP(defaultIP);
        return defaultIP;

    } catch (error) {
        console.error("Error in getLocalIPAddress:", error);
        const fallbackIP = process.env.DEFAULT_MASTER_IP || '*************';
        await updateMasterServerIP(fallbackIP);
        return fallbackIP;
    }
}

module.exports = { getLocalIPAddress };
