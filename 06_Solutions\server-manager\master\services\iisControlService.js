const axios = require('axios');
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);
const db = require('../db');

class IISControlService {
    static async getLocalStatus() {
        try {
            const { stdout } = await execAsync('powershell -Command "Get-Service W3SVC | Select-Object -ExpandProperty Status"');
            const status = stdout.trim().toLowerCase();
            return {
                status,
                running: status === 'running',
                stopped: status === 'stopped'
            };
        } catch (error) {
            throw new Error(`Failed to get IIS service status: ${error.message}`);
        }
    }

    static async getRemoteStatus(serverIp) {
        try {
            const response = await axios.get(`http://${serverIp}:3000/iis-status`, {
                timeout: 3000
            });
            return response.data;
        } catch (error) {
            throw new Error(`Failed to get status from slave ${serverIp}: ${error.message}`);
        }
    }

    static async getIISStatus(serverIp) {
        const server = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM servers WHERE server_ip = ?', [serverIp], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (!server) {
            throw new Error('Server not found');
        }

        return server.server_type === 'master' 
            ? await this.getLocalStatus()
            : await this.getRemoteStatus(serverIp);
    }

    static async executeLocalCommand(action) {
        const validActions = ['start', 'stop', 'restart'];
        if (!validActions.includes(action)) {
            throw new Error('Invalid IIS action');
        }

        let command;
        switch (action) {
            case 'start':
                command = 'Start-Service W3SVC';
                break;
            case 'stop':
                command = 'Stop-Service W3SVC';
                break;
            case 'restart':
                command = 'Restart-Service W3SVC';
                break;
        }

        try {
            await execAsync(`powershell -Command "${command}"`);
            return { success: true, message: `IIS service ${action} completed successfully` };
        } catch (error) {
            throw new Error(`Failed to ${action} IIS service: ${error.message}`);
        }
    }

    static async executeRemoteCommand(serverIp, action) {
        try {
            const response = await axios.post(`http://${serverIp}:3000/iis-control`, 
                { action },
                { 
                    timeout: 30000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );
            return response.data;
        } catch (error) {
            throw new Error(`Failed to execute ${action} on slave ${serverIp}: ${error.message}`);
        }
    }

    static async controlIIS(serverIp, action) {
        // Get server info from database
        const server = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM servers WHERE server_ip = ?', [serverIp], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (!server) {
            throw new Error('Server not found');
        }

        // Execute command based on server type
        if (server.server_type === 'master') {
            return await this.executeLocalCommand(action);
        } else {
            return await this.executeRemoteCommand(serverIp, action);
        }
    }
}

module.exports = IISControlService;
