const db = require('../db');

console.log('🔄 Starting removal of users...');

// Function to remove all users
const removeAllUsers = () => {
    const deleteQuery = `DELETE FROM users`;
    
    db.run(deleteQuery, function(err) {
        if (err) {
            console.error('❌ Error removing users:', err.message);
            process.exit(1);
        }
        
        console.log(`✅ Successfully removed ${this.changes} users`);
        
        // Verify the removal
        db.all('SELECT * FROM users', (err, rows) => {
            if (err) {
                console.error('❌ Error verifying removal:', err.message);
                process.exit(1);
            }
            
            if (rows.length === 0) {
                console.log('✅ Verification successful - No users remaining in database');
            } else {
                console.error('❌ Verification failed - Some users still exist');
                console.log('Remaining users:', rows);
            }
            
            // Close the database connection
            db.close(() => {
                console.log('📡 Database connection closed.');
                process.exit(0);
            });
        });
    });
};

// Function to remove specific user by username
const removeSpecificUser = (username) => {
    const deleteQuery = `DELETE FROM users WHERE username = ?`;
    
    db.run(deleteQuery, [username], function(err) {
        if (err) {
            console.error('❌ Error removing user:', err.message);
            process.exit(1);
        }
        
        console.log(`✅ Successfully removed ${this.changes} user(s) with username: ${username}`);
        
        // Verify the removal
        db.all('SELECT * FROM users WHERE username = ?', [username], (err, rows) => {
            if (err) {
                console.error('❌ Error verifying removal:', err.message);
                process.exit(1);
            }
            
            if (rows.length === 0) {
                console.log('✅ Verification successful - User successfully removed');
            } else {
                console.error('❌ Verification failed - User still exists');
                console.log('Remaining user:', rows);
            }
            
            // Close the database connection
            db.close(() => {
                console.log('📡 Database connection closed.');
                process.exit(0);
            });
        });
    });
};

// Check if a specific username was provided as command line argument
const username = process.argv[2];
if (username) {
    console.log(`🎯 Removing specific user: ${username}`);
    removeSpecificUser(username);
} else {
    console.log('🎯 Removing all users');
    removeAllUsers();
}