<script setup>
import {
  ref,
  watch,
  inject,
  getCurrentInstance,
  onMounted,
  onUnmounted
} from "vue";
import ExportDialog from "./ExportDialog.vue";
import { userState } from "../main";

const VITE_MASTER_URL = import.meta.env.VITE_MASTER_URL;
const app = getCurrentInstance();
const alert = inject("alert") || app?.appContext.config.globalProperties.$alert;
const exportDialog = ref(null);
const filteredInstances = ref([]);

const updateFilteredInstances = (instances) => {
  filteredInstances.value = instances;
};

const props = defineProps({
  instanceTableRef: Object,
  modelValue: String,
  isAdmin: Boolean
});

const emit = defineEmits(["update:modelValue", "refreshComplete"]);
const searchText = ref(props.modelValue);

watch(searchText, (newVal) => {
  emit("update:modelValue", newVal);
});

const handleRefresh = async () => {
  if (!userState.isLoggedIn || !userState.user?.token) {
    alert?.show("Authentication required. Please log in.", "error", 3000);
    return;
  }

  try {
    if (!props.instanceTableRef) {
      console.error("Instance table reference is undefined.");
      return;
    }

    const allServers =
      props.instanceTableRef.instances?.map((instance) => instance.server_ip) ||
      [];
    const processedServers = new Set();

    allServers.forEach((serverIp) => {
      props.instanceTableRef.updateRefreshStatus(serverIp, "refreshing");
    });

    const response = await fetch(`${VITE_MASTER_URL}/refresh`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${userState.user.token}`,
        "Content-Type": "application/json"
      }
    });

    if (!response.ok) {
      throw new Error("Refresh request failed. Please try again.");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = "";
    let isCompleted = false;

    const handleCompletionMessage = () => {
      console.log("Handling completion message");

      allServers.forEach((serverIp) => {
        if (!processedServers.has(serverIp)) {
          const currentStatus =
            props.instanceTableRef.refreshStatus?.get(serverIp);
          console.log("Server status:", serverIp, currentStatus);

          // Only update if still refreshing
          if (currentStatus?.status === "refreshing") {
            console.log("Updating server to success:", serverIp);
            props.instanceTableRef.updateRefreshStatus(serverIp, "success");
          }
        }
      });
      isCompleted = true;
      alert?.show("Refresh process completed", "info", 5000);
      emit("refreshComplete");
      reader.cancel();
    };

    while (!isCompleted) {
      const { done, value } = await reader.read();
      if (done) break;
      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";
      for (const line of lines) {
        if (line.trim()) {
          try {
            const data = JSON.parse(line.replace("data: ", ""));
            if (data.message && data.message.includes("completed")) {
              handleCompletionMessage();
              return;
            } else if (data.server_ip && data.status) {
              console.log("Updating status:", {
                serverIp: data.server_ip,
                status: data.status,
                error:
                  data.status === "error" || data.status === "failed"
                    ? data.error
                    : null
              });

              if (
                data.status === "failed" ||
                data.status === "error" ||
                data.status === "success"
              ) {
                processedServers.add(data.server_ip);
              }

              props.instanceTableRef.updateRefreshStatus(
                data.server_ip,
                data.status,
                data.status === "error" || data.status === "failed"
                  ? data.error || "Refresh failed"
                  : null
              );
            }
          } catch (e) {
            console.error("Error parsing server response:", e);
          }
        }
      }
    }

    if (buffer.trim() && !isCompleted) {
      try {
        const data = JSON.parse(buffer.replace("data: ", ""));
        if (data.message && data.message.includes("completed")) {
          handleCompletionMessage();
        } else if (data.server_ip && data.status) {
          if (
            data.status === "failed" ||
            data.status === "error" ||
            data.status === "success"
          ) {
            processedServers.add(data.server_ip);
          }
          props.instanceTableRef.updateRefreshStatus(
            data.server_ip,
            data.status,
            data.status === "error" || data.status === "failed"
              ? data.error || "Refresh failed"
              : null
          );
        }
      } catch (e) {
        console.error("Error parsing final buffer:", e);
      }
    }
  } catch (error) {
    console.error("Refresh error:", error);
    alert?.show("Failed to refresh servers. Please try again.", "error", 3000);
    allServers.forEach((serverIp) => {
      props.instanceTableRef.updateRefreshStatus(
        serverIp,
        "error",
        "Refresh failed"
      );
    });
  }
};

const handleExport = () => {
  exportDialog.value.toggleDialog();
};

const handleKeyboardShortcut = (event) => {
  if (props.isAdmin && event.altKey && event.shiftKey && event.ctrlKey) {
    event.preventDefault();
    handleRefresh();
  }
};

onMounted(() => {
  window.addEventListener("keydown", handleKeyboardShortcut);
});

onUnmounted(() => {
  window.removeEventListener("keydown", handleKeyboardShortcut);
});
</script>

<template>
  <div class="search-container">
    <div class="search-wrapper">
      <div class="search-controls">
        <div class="input-container">
          <i class="pi pi-search search-icon"></i>
          <input type="text" placeholder="Search..." class="search-input" v-model="searchText" />
        </div>
        <button v-if="isAdmin" class="action-btn refresh-btn" @click="handleRefresh"
          title="Refresh server list (Alt + Shift + Ctrl)">
          <i class="pi pi-refresh"></i>
        </button>
        <button class="action-btn export-btn" @click="handleExport" title="Export data">
          <i class="pi pi-file-export"></i>
        </button>
      </div>
    </div>
    <ExportDialog ref="exportDialog" :selected-servers="props.instanceTableRef?.selectedServers || new Set()"
      :instances="props.instanceTableRef?.instances || []"
      :all-instances="props.instanceTableRef?.allInstances || []" />
  </div>
</template>

<style scoped>
.search-container {
  position: sticky;
  top: 3rem;
  left: 0;
  right: 0;
  z-index: 998;
  padding: 0.75rem 2rem;
  background-color: var(--bg-primary);
}

.search-wrapper {
  max-width: 600px;
  margin: 0 auto;
}

.search-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.input-container {
  position: relative;
  flex-grow: 1;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #211761;
  font-size: 0.875rem;
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2rem;
  border: 2px solid #211761;
  border-radius: 3rem;
  font-size: 0.875rem;
  background-color: var(--bg-primary);
  color: #211761;
  outline: none;
  transition: border-color 0.2s;
}

.search-input::placeholder {
  color: #4c475e;
}

.search-input:focus {
  border-color: #5237cc;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  /* Reduced from 0.75rem */
  border: 2px solid #211761;
  border-radius: 3rem;
  background-color: transparent;
  color: #211761;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.action-btn::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: #211761;
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.2s ease;
}

.action-btn:hover {
  background-color: #211761;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(33, 23, 97, 0.15);
}

.action-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(33, 23, 97, 0.1);
}

.action-btn:active::after {
  transform: translate(-50%, -50%) scale(1.5);
  opacity: 0.3;
}

.action-btn i {
  font-size: 0.875rem;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 1;
}

.refresh-btn:hover i {
  transform: rotate(45deg);
}

.refresh-btn:active i {
  transform: rotate(90deg);
}

.export-btn:hover i {
  transform: translateY(2px);
}

.export-btn:active i {
  transform: translateY(4px);
}
</style>
