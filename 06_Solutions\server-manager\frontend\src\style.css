@import "tailwindcss";
:root {
  font-family: 'Montserrat', sans-serif;  line-height: 1.5;
  font-weight: 400;
  overflow-y: scroll;
    /* Base colors */
  --primary-dark: #211761;
  --primary-light: #5237cc;
  --grey: #4c475e;
  --light-grey: #dddae4;
  --yellow: #e0c02f;

    /* Light mode */
  --bg-primary: #ffffff;
  --bg-secondary: #211761;
  --text-primary: #211761;
  --text-secondary: #4c475e;
  --border-color: #dddae4;
  --hover-bg-button: #5237cc;
  --shadow-color: rgba(33, 23, 97, 0.15);
}
* {
  margin: 0;  padding: 0;
  box-sizing: border-box;  font-family: 'Montserrat', sans-serif;
}
body {
  font-family: 'Montserrat', sans-serif;
  background-color: #ffffff;
}

#app {
  min-height: 100vh;
}

/* .left-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding-left: 2rem;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-img {
  height: 2rem;
  width: auto;
}

.nav-menu {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-item {
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem;
  transition: color 0.2s;
}

.nav-item:hover {
  color: #dddae4;
}

.nav-right {
  padding-right: 2rem;
}

.login-btn {
  padding: 0.5rem 1.5rem;
  background-color:var(--bg-secondary);
  color: white;
  border: none;
  border-radius:3rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.login-btn:hover {
  background-color: var(--hover-bg-button);
}

.search-container {
  width: 100%;
  padding: 1rem 2rem;
  background-color: transparent;
}

.search-wrapper {
  max-width: 600px;
  margin: 0 auto;
}

.input-container {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #211761;
  font-size: 1rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid #211761;
  border-radius: 3rem;
  font-family: 'Montserrat', sans-serif;
  font-size: 1rem;
  background-color: transparent;
  color: #211761;
  outline: none;
  transition: border-color 0.2s;
}

.search-input::placeholder {
  color: #4c475e;
}

.search-input:focus {
  border-color: #5237cc;
}

.table-container {
  width: 100%;
  padding: 2rem;
  background-color: transparent;
}

.server-group {
  margin-bottom: 3rem;
}

.server-group:last-child {
  margin-bottom: 0;
}

.server-ip-title {
  color: #211761;
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 1rem;
  padding-left: 0.5rem;
}

.instance-table {
  width: 100%;
  max-width: 9000px;
  min-width: 1425px;
  margin: 0 auto;
  border-collapse: collapse;
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(76, 71, 94, 0.1);
}

.instance-table th {
  background-color: #211761;
  color: white;
  font-weight: 500;
  text-align: left;
  padding: 1rem;
}

.instance-table td {
  padding: 1rem;
  border-bottom: 1px solid #dddae4;
  color: #4c475e;
}

.instance-table tbody tr:hover {
  background-color: rgba(82, 55, 204, 0.05);
}

.instance-table tbody tr:last-child td {
  border-bottom: none;
} */ */

/* Base Cards */
.base-card {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(33, 23, 97, 0.1);
}

/* Base Buttons */
.base-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.3rem 0.6rem;
  border-radius: 2rem;
  border: 1px solid var(--border-color);
  background: white;
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.base-btn:hover {
  background: var(--primary-light);
  color: white;
  border-color: var(--primary-light);
}

.base-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Loading Animation */
.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Flex Layouts */
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Grid Layouts */
.grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
}

/* Status Badges */
.status-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  text-transform: capitalize;
}
