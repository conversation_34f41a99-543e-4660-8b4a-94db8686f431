<script setup>
import { ref } from 'vue';

const message = {
  emoji: '📚✨',
  title: 'Hey there!',
  subtitle: 'Something amazing is brewing...',
  content: `Our documentation wizards are hard at work creating something special just for you. 
            Soon, you'll find all the guides, tips, and tricks you need right here!`,
  footnote: `Check back in a bit – we promise it'll be worth the wait!`
};
</script>

<template>
  <div class="documentation-container">
    <div class="coming-soon">
      <div class="emoji">{{ message.emoji }}</div>
      <h1>{{ message.title }}</h1>
      <h2>{{ message.subtitle }}</h2>
      <p>{{ message.content }}</p>
      <div class="footnote">{{ message.footnote }}</div>
    </div>
  </div>
</template>

<style scoped>
.documentation-container {
  min-height: calc(100vh - 60px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: var(--bg-primary);
}

.coming-soon {
  text-align: center;
  max-width: 600px;
  padding: 3.5rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(82, 55, 204, 0.12);
  transform: translateY(-20px);
}

.emoji {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  animation: float 3s ease-in-out infinite;
}

h1 {
  color: var(--primary-light);
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  opacity: 0.9;
}

p {
  color: var(--text-secondary);
  line-height: 1.8;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.footnote {
  color: var(--primary-light);
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.8;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .coming-soon {
    padding: 2.5rem;
    margin: 1rem;
  }

  h1 { font-size: 2rem; }
  h2 { font-size: 1.25rem; }
  p { font-size: 1rem; }
  .emoji { font-size: 3rem; }
}
</style>

