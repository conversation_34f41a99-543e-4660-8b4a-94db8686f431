{"name": "master", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "nodemon index.js", "test-local": "node test-local.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.2", "bcrypt": "^5.1.1", "cors": "^2.8.5", "crypto-js": "^4.2.0", "default-gateway": "^7.2.2", "dotenv": "^16.4.7", "express": "^4.21.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-cron": "^3.0.3", "nodemon": "^3.1.9", "os-utils": "^0.0.14", "sqlite3": "^5.1.7", "systeminformation": "^5.25.11", "ws": "^8.18.1", "xml2js": "^0.6.2"}}