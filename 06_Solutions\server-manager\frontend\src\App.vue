<script setup>
import { ref } from 'vue';
import Navbar from './components/Navbar.vue'
import ScrollToTop from './components/ScrollToTop.vue'
import AlertModal from './components/AlertModal.vue'
import LoadingScreen from './components/LoadingScreen.vue'
import { isLoading, isInitialLoad } from './router'

// Add this to track modal state globally
const isModalOpen = ref(false);
</script>

<template>
  <div class="app-container" :class="{ 'loading': isInitialLoad }">
    <Navbar v-show="!isInitialLoad" />
    <main class="main-content">
      <LoadingScreen v-if="isLoading" />
      <router-view @modal-state-changed="isModalOpen = $event"></router-view>
    </main>
    <AlertModal />
    <ScrollToTop :disabled="isModalOpen" />
  </div>
</template>

<style scoped>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container.loading {
  overflow: hidden;
}

.main-content {
  flex: 1;
  margin-top: 3rem;
  padding: 0;
  position: relative;
}
</style>

