<script setup>
import { ref } from 'vue';

const props = defineProps({
  message: {
    type: String,
    default: 'Are you sure you want to proceed with this action?'
  },
  title: {
    type: String,
    default: 'Confirm Action'
  }
});

const emits = defineEmits(['confirmed', 'modalStateChanged']);

const isVisible = ref(false);

const toggleDialog = () => {
  isVisible.value = !isVisible.value;
  emits('modalStateChanged', isVisible.value);
};

const confirm = () => {
  emits('confirmed');
  toggleDialog();
};

defineExpose({
  toggleDialog
});
</script>

<template>
  <div v-if="isVisible" class="confirmation-dialog-overlay">
    <div class="confirmation-dialog" ref="dialogRef">
      <h3>{{ title }}</h3>
      <p>{{ message }}</p>
      <div class="dialog-actions">
        <button class="cancel-btn" @click="toggleDialog">No</button>
        <button class="confirm-btn" @click="confirm">Yes</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.confirmation-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirmation-dialog {
  background: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 16px rgba(33, 23, 97, 0.12);
}

.confirmation-dialog h3 {
  margin-top: 0;
  color: #211761;
  font-size: 1rem;
  font-weight: 600;
}

.confirmation-dialog p {
  color: #4c475e;
  margin: 1rem 0;
  font-size: 0.875rem;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.cancel-btn, .confirm-btn {
  padding: 0.5rem 1rem;
  border-radius: 3rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.cancel-btn {
  background: none;
  border: 2px solid #dddae4;
  color: #4c475e;
}

.cancel-btn:hover {
  border-color: #211761;
  color: #211761;
}

.confirm-btn {
  background: #211761;
  color: white;
  border: none;
}

.confirm-btn:hover {
  background: #5237cc;
}

.confirm-btn:active, .cancel-btn:active {
  transform: scale(0.98);
}
</style>












