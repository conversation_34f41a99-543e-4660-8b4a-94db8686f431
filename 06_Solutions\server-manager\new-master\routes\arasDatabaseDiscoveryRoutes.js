const express = require('express');
const router = express.Router();
const { discoverDatabases } = require('../controllers/arasDatabaseDiscoveryController');
// const authenticateAdmin = require('../middleware/authenticateAdmin');

/**
 * @route   GET /database
 * @desc    Discover databases for all servers and update the database
 * @access  Public (can be restricted to Admin only)
 */
router.get('/', 
    // authenticateAdmin, 
    discoverDatabases);

module.exports = router;