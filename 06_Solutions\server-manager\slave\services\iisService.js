const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);
async function getIISStatus() {
    try {
        const { stdout } = await execAsync('powershell -Command "Get-Service W3SVC | Select-Object -ExpandProperty Status"');
        const status = stdout.trim().toLowerCase();
        return {
            status,
            running: status === 'running',
            stopped: status === 'stopped'
        };
    } catch (error) {
        throw new Error(`Failed to get IIS service status: ${error.message}`);
    }
}
async function executeIISCommand(action) {
    const validActions = ['start', 'stop', 'restart'];
    if (!validActions.includes(action)) {
        throw new Error('Invalid IIS action');
    }
    let command;
    switch (action) {
        case 'start':
            command = 'Start-Service W3SVC';
            break;
        case 'stop':
            command = 'Stop-Service W3SVC';
            break;
        case 'restart':
            command = 'Restart-Service W3SVC';
            break;
    }
    try {
        await execAsync(`powershell -Command "${command}"`);
        const status = await getIISStatus();
        return { 
            success: true, 
            message: `IIS service ${action} completed successfully`,
            status: status.status,
            running: status.running
        };
    } catch (error) {
        throw new Error(`Failed to ${action} IIS service: ${error.message}`);
    }
}
module.exports = { executeIISCommand, getIISStatus };