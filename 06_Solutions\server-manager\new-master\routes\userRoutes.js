const express = require('express');
const router = express.Router();
const {
    getAllUsers,
    getUserById,
    createUser,
    updateUser,
    deleteUser
} = require('../controllers/userController');
// const authenticateAdmin = require('../middleware/authenticateAdmin');

/**
 * @route   GET /users
 * @desc    Get all users
 * @access  Private (Admin only)
 */
router.get('/',
    // authenticateAdmin,
    getAllUsers);

/**
 * @route   GET /users/:id
 * @desc    Get user by ID
 * @access  Private (Admin only)
 */
router.get('/:id',
    // authenticateAdmin,
    getUserById);

/**
 * @route   POST /users
 * @desc    Create a new user
 * @access  Private (Admin only)
 */
router.post('/',
    // authenticateAdmin,
    createUser);

/**
 * @route   PUT /users/:id
 * @desc    Update an existing user
 * @access  Private (Admin only)
 */
router.put('/:id',
    // authenticateAdmin,
    updateUser);

/**
 * @route   DELETE /users/:id
 * @desc    Delete a user
 * @access  Private (Admin only)
 */
router.delete('/:id',
    // authenticateAdmin,
    deleteUser);

module.exports = router;
