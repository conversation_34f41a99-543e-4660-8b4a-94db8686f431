require('dotenv').config();
const express = require('express');
const morgan = require('morgan');
const cors = require('cors');
const cron = require('node-cron');
const axios = require('axios');
const http = require('http');
const db = require('./db/db');
const WebSocketService = require('./services/websocketService');

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT;
const WS_PORT = process.env.WS_PORT;

// Initialize WebSocket service with the HTTP server
const wsService = new WebSocketService(server);

const applicationRoutes = require('./routes/applicationRoutes');
const serverRoutes = require('./routes/serverRoutes');
const userRoutes = require('./routes/userRoutes');
const tagRoutes = require('./routes/tagRoutes');
const arasCredentialRoutes = require('./routes/arasCredentialRoutes');
const applicationTagRoutes = require('./routes/applicationTagRoutes');
const authRoutes = require('./routes/authRoutes.js');
const refreshRoutes = require('./routes/refreshRoutes');
const iisControlRoutes = require('./routes/iisControlRoutes');
const arasVersionRoutes = require('./routes/arasVersionRoutes');
const healthHistoryRoutes = require('./routes/healthHistoryRoutes');
const realtimeHealthRoutes = require('./routes/realtimeHealthRoutes');
const databaseDiscoveryRoutes = require('./routes/arasDatabaseDiscoveryRoutes.js');
const serviceRoutes = require('./routes/serviceRoutes');

app.use(express.json());
app.use(morgan('dev'));
app.use(cors());

// Routes
app.get('/', (req, res) => {
    res.send("Welcome! This is the ServerTap backend.");
});

app.use('/applications', applicationRoutes);
app.use('/servers', serverRoutes);
app.use('/users', userRoutes);
app.use('/tags', tagRoutes);
app.use('/aras-credentials', arasCredentialRoutes);
app.use('/application-tag', applicationTagRoutes);
app.use('/auth', authRoutes);
app.use('/refresh', refreshRoutes);
app.use('/iis', iisControlRoutes);
app.use('/aras-versions', arasVersionRoutes);
app.use('/health-history', healthHistoryRoutes);
app.use('/realtime-health', realtimeHealthRoutes);
app.use('/aras-databases', databaseDiscoveryRoutes);
app.use('/services', serviceRoutes);

// Cron job for auto refresh (runs every hour)
cron.schedule('0 * * * *', async () => {
    console.log("🔄 Running hourly auto refresh job...");

    try {
        // Step 1: Trigger refresh process
        const instancesResponse = await axios.get(`http://localhost:${PORT}/refresh/auto`, {
            timeout: 20000
        });
        console.log("✅ Refresh process completed:", instancesResponse.data);

        // Step 2: Trigger aras-versions only if refresh succeeds
        try {
            const arasVersionResponse = await axios.get(`http://localhost:${PORT}/aras-versions`, {
                timeout: 30000
            });
            console.log("✅ Aras version fetch completed:", arasVersionResponse.data);
        } catch (arasError) {
            console.error("❌ Aras version fetch error:", arasError.message);
        }

        // Step 3: Collect health data from all servers
        try {
            const healthResponse = await axios.get(`http://localhost:${PORT}/health-history/collect`, {
                timeout: 30000
            });
            console.log("✅ Health data collection completed:", {
                servers: healthResponse.data.results.total,
                successful: healthResponse.data.results.successful,
                failed: healthResponse.data.results.failed
            });
        } catch (healthError) {
            console.error("❌ Health data collection error:", healthError.message);
        }

        // Step 4: Discover and update databases from all servers
        try {
            const databaseResponse = await axios.get(`http://localhost:${PORT}/aras-databases`, {
                timeout: 10000
            });
            console.log("✅ Database discovery completed:", {
                servers: databaseResponse.data.results.total,
                successful: databaseResponse.data.results.successful,
                failed: databaseResponse.data.results.failed
            });
        } catch (databaseError) {
            console.error("❌ Database discovery error:", databaseError.message);
        }

    } catch (refreshError) {
        console.error("❌ Refresh process error:", refreshError.message);
    }
});

// Initialize database before starting the server
const initializeServer = async () => {
    try {
        // Wait for database initialization (db.js will handle the table creation)
        await new Promise((resolve) => {
            db.on('open', () => {
                console.log('📡 Database connection established');
                resolve();
            });
        });

        // Start the server after database is initialized
        server.listen(PORT, () => {
            console.log(`🚀 Server is running on port ${PORT}`);
            console.log(`📡 WebSocket server is running on the same port`);
        });

    } catch (error) {
        console.error('❌ Failed to initialize server:', error);
        process.exit(1);
    }
};

// Start the server with database initialization
initializeServer();

// Handle process termination
process.on('SIGINT', () => {
    db.close((err) => {
        if (err) {
            console.error('❌ Error closing database:', err.message);
        } else {
            console.log('📡 Database connection closed');
        }
        process.exit(err ? 1 : 0);
    });
});
