const db = require('../db');
const { getLocalIPAddress } = require('../utils/networkUtils');

async function migrateTables() {
    console.log('🔄 Starting migration to merge master and slave server tables...');

    try {
        // Check if both old tables exist
        const [slaveTableExists, masterTableExists] = await Promise.all([
            new Promise((resolve, reject) => {
                db.get(`SELECT name FROM sqlite_master WHERE type='table' AND name='slave_servers'`, 
                    (err, row) => err ? reject(err) : resolve(row ? true : false));
            }),
            new Promise((resolve, reject) => {
                db.get(`SELECT name FROM sqlite_master WHERE type='table' AND name='master_server'`, 
                    (err, row) => err ? reject(err) : resolve(row ? true : false));
            })
        ]);

        console.log('📝 Existing tables check:', {
            slave_servers: slaveTableExists ? 'exists' : 'not found',
            master_server: masterTableExists ? 'exists' : 'not found'
        });

        // Create new servers table
        await new Promise((resolve, reject) => {
            db.run(`
                CREATE TABLE IF NOT EXISTS servers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    server_ip TEXT NOT NULL UNIQUE,
                    server_name TEXT NOT NULL,
                    server_type TEXT NOT NULL DEFAULT 'slave'
                )
            `, err => err ? reject(err) : resolve());
        });
        console.log('✅ Created new servers table');

        // Get master server IP
        const masterIP = await getLocalIPAddress();

        // Insert master server with id 0
        await new Promise((resolve, reject) => {
            db.run(`
                INSERT OR IGNORE INTO servers (id, server_ip, server_name, server_type)
                VALUES (0, ?, 'Master Server', 'master')
            `, [masterIP], err => err ? reject(err) : resolve());
        });
        console.log('✅ Inserted master server record');

        // Migrate slave servers if table exists
        if (slaveTableExists) {
            // Get all slave servers
            const slaveServers = await new Promise((resolve, reject) => {
                db.all('SELECT * FROM slave_servers', (err, rows) => {
                    if (err) reject(err);
                    resolve(rows || []);
                });
            });
            console.log(`📝 Found ${slaveServers.length} slave servers to migrate`);

            // Insert all slave servers
            for (const slave of slaveServers) {
                await new Promise((resolve, reject) => {
                    db.run(`
                        INSERT OR IGNORE INTO servers (server_ip, server_name, server_type)
                        VALUES (?, ?, 'slave')
                    `, [slave.server_ip, slave.server_name], 
                    function(err) {
                        if (err) reject(err);
                        resolve(this.changes);
                    });
                });
                console.log(`✅ Migrated slave server: ${slave.server_name} (${slave.server_ip})`);
            }
        }

        // Verify migration before dropping old tables
        const migratedServers = await new Promise((resolve, reject) => {
            db.all('SELECT * FROM servers ORDER BY server_type DESC', (err, rows) => {
                if (err) reject(err);
                resolve(rows);
            });
        });

        console.log('\n📊 Migration Results:');
        console.log('==================');
        console.log('Total servers:', migratedServers.length);
        console.log('Master server:', migratedServers.find(s => s.server_type === 'master'));
        console.log('Slave servers:', migratedServers.filter(s => s.server_type === 'slave').length);

        // Drop old tables only if migration was successful
        if (migratedServers.length > 0) {
            if (slaveTableExists) {
                await new Promise((resolve, reject) => {
                    db.run('DROP TABLE slave_servers', err => err ? reject(err) : resolve());
                });
                console.log('✅ Dropped slave_servers table');
            }

            if (masterTableExists) {
                await new Promise((resolve, reject) => {
                    db.run('DROP TABLE master_server', err => err ? reject(err) : resolve());
                });
                console.log('✅ Dropped master_server table');
            }
        }

        console.log('\n✅ Migration completed successfully');
    } catch (error) {
        console.error('❌ Error during migration:', error);
        throw error;
    }
}

migrateTables()
    .then(() => {
        db.close(() => {
            console.log('📡 Database connection closed');
            process.exit(0);
        });
    })
    .catch((error) => {
        console.error('❌ Migration failed:', error);
        db.close(() => {
            process.exit(1);
        });
    });

