const db = require('../db/db');
const axios = require('axios');
const SLAVE_PORT = process.env.SLAVE_PORT;

/**
 * @desc    Get health history for all servers
 * @route   GET /health-history
 * @access  Private (Admin only)
 */
async function getHealthHistory(req, res) {
    try {
        // Get query parameters for filtering
        const { limit, serverId } = req.query;
        
        // Build the SQL query based on parameters
        let query = 'SELECT * FROM servers_health';
        const params = [];
        
        // Add server filter if provided
        if (serverId) {
            query += ' WHERE server_id = ?';
            params.push(serverId);
        }
        
        // Add ordering
        query += ' ORDER BY date DESC, time DESC';
        
        // Add limit if provided
        if (limit && !isNaN(parseInt(limit))) {
            query += ' LIMIT ?';
            params.push(parseInt(limit));
        }
        
        // Execute the query
        db.all(query, params, (err, rows) => {
            if (err) {
                console.error('Database error:', err);
                return res.status(500).json({ 
                    error: 'Failed to fetch health history',
                    details: err.message 
                });
            }
            
            // Group data by server_id
            const groupedData = rows.reduce((acc, row) => {
                if (!acc[row.server_id]) {
                    acc[row.server_id] = [];
                }
                acc[row.server_id].push(row);
                return acc;
            }, {});
            
            res.status(200).json({
                success: true,
                data: groupedData,
                count: rows.length,
                servers: Object.keys(groupedData).length
            });
        });
    } catch (error) {
        console.error('Error fetching health history:', error);
        res.status(500).json({ 
            error: 'Failed to fetch health history',
            details: error.message 
        });
    }
}

/**
 * @desc    Collect and store health data from all servers
 * @route   POST /health-history/collect
 * @access  Private (Admin only)
 */
async function collectHealthData(req, res) {
    try {
        // Get all servers from database
        db.all('SELECT id, ip, server_name FROM servers', async (err, servers) => {
            if (err) {
                console.error('Error fetching servers:', err);
                return res.status(500).json({ 
                    error: 'Failed to fetch servers',
                    details: err.message 
                });
            }
            
            if (!servers || servers.length === 0) {
                return res.status(404).json({ 
                    error: 'No servers found in database' 
                });
            }
            
            const results = {
                success: [],
                failed: []
            };
            
            // Process each server
            const serverPromises = servers.map(async (server) => {
                try {
                    // Get health data from server
                    const response = await axios.get(`http://${server.ip}:${SLAVE_PORT}/health`, {
                        timeout: 5000 // 5 second timeout
                    });
                    
                    if (!response.data) {
                        throw new Error('Invalid response from server');
                    }
                    
                    const healthData = response.data;
                    
                    // Insert health data into database
                    await new Promise((resolve, reject) => {
                        const query = `
                            INSERT INTO servers_health (
                                server_id, date, time, 
                                cpu_usage, 
                                memory_used, memory_total, 
                                disk_used, disk_total, read_speed, write_speed,
                                network_download, network_upload
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        `;
                        
                        const params = [
                            server.id,
                            healthData.date,
                            healthData.time,
                            healthData.cpu.usage,
                            healthData.memory.used,
                            healthData.memory.total,
                            healthData.disk.used,
                            healthData.disk.total,
                            healthData.disk.read_speed,
                            healthData.disk.write_speed,
                            healthData.network.download_speed,
                            healthData.network.upload_speed
                        ];
                        
                        db.run(query, params, function(err) {
                            if (err) {
                                console.error(`Error storing health data for server ${server.ip}:`, err);
                                reject(err);
                            } else {
                                resolve(this.lastID);
                            }
                        });
                    });
                    
                    // Add to success list
                    results.success.push({
                        id: server.id,
                        ip: server.ip,
                        name: server.server_name || 'Unknown'
                    });
                    
                } catch (error) {
                    console.error(`Error collecting health data from ${server.ip}:`, error.message);
                    
                    // Add to failed list
                    results.failed.push({
                        id: server.id,
                        ip: server.ip,
                        name: server.server_name || 'Unknown',
                        error: error.message
                    });
                }
            });
            
            // Wait for all servers to be processed
            await Promise.all(serverPromises);
            
            // Return results
            res.status(200).json({
                success: true,
                message: 'Health data collection completed',
                results: {
                    total: servers.length,
                    successful: results.success.length,
                    failed: results.failed.length,
                    successList: results.success,
                    failedList: results.failed
                }
            });
        });
    } catch (error) {
        console.error('Error collecting health data:', error);
        res.status(500).json({ 
            error: 'Failed to collect health data',
            details: error.message 
        });
    }
}

module.exports = {
    getHealthHistory,
    collectHealthData
};
