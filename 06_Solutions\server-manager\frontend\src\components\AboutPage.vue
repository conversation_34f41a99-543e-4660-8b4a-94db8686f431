<script setup>
// import s
const content = {
  hero: {
    title: "Empowering IT Infrastructure Management",
    subtitle: "Your All-in-One IIS Monitoring Solution",
    description: "ServerTap is a powerful monitoring and management solution by Steepgraph Systems, designed to revolutionize how you handle IIS servers."
  },
  sections: [
    {
      title: "Why ServerTap?",
      content: `ServerTap brings enterprise-grade IIS server management within reach. Our solution 
                combines real-time monitoring, intelligent analytics, and seamless control capabilities 
                to ensure your web applications run at peak performance.`
    },
    {
      title: "Key Features",
      features: [
        {
          icon: "pi pi-chart-line",
          title: "Real-Time Monitoring",
          description: "Track CPU, memory, disk usage, and network performance metrics in real-time"
        },
        {
          icon: "pi pi-server",
          title: "IIS Control",
          description: "Start, stop, and restart application pools and websites with a single click"
        },
        {
          icon: "pi pi-database",
          title: "Instance Management",
          description: "Centralized control over all your database instances across servers"
        },
        {
          icon: "pi pi-chart-bar",
          title: "Performance Analytics",
          description: "Detailed insights and trends analysis for informed decision-making"
        }
      ]
    },
    {
      title: "About Steepgraph Systems",
      content: `SteepGraph Systems Pvt. Ltd. is a trusted PLM and digital transformation partner, delivering 
            specialized engineering IT services and solutions for global manufacturing enterprises. 
            With deep expertise in PLM platforms, CAD integrations, data migration, and enterprise application 
            development, SteepGraph empowers businesses to streamline engineering processes, accelerate 
            innovation, and drive digital excellence.`
    },
    {
      title: "Our Mission",
      content: `To enable engineering enterprises with tailored digital solutions that simplify complex 
            processes, enhance collaboration, and ensure sustained innovation in the Product Lifecycle 
            Management (PLM) domain.`
    }

  ],
  cta: {
    title: "Ready to Transform Your IIS Management?",
    description: "Join leading organizations that trust ServerTap for their IIS monitoring needs.",
    button: "Contact Sales"
  }
};
</script>

<template>
  <div class="about-container">
    <!-- Add video background -->
    <div class="video-background">
      <video autoplay muted loop playsinline>
        <source src="/servertap-about.mp4" type="video/mp4">
      </video>
      <div class="video-overlay"></div>
    </div>

    <!-- Existing content -->
    <div class="hero-section">
      <div class="company-brand">
        <img src="/steepgraph-logo-dark.svg" alt="Steepgraph Systems" class="steepgraph-logo" />
      </div>
      <h1>{{ content.hero.title }}</h1>
      <h2>{{ content.hero.subtitle }}</h2>
      <p class="hero-description">{{ content.hero.description }}</p>
    </div>

    <div class="content-sections">
      <div v-for="(section, index) in content.sections" :key="index" class="section">
        <h3>{{ section.title }}</h3>

        <div v-if="section.features" class="features-grid">
          <div v-for="feature in section.features" :key="feature.title" class="feature-card">
            <i :class="feature.icon"></i>
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>

        <p v-else class="section-content">{{ section.content }}</p>
      </div>
    </div>

    <div class="cta-section">
      <h3>{{ content.cta.title }}</h3>
      <p>{{ content.cta.description }}</p>
      <button class="cta-button">{{ content.cta.button }}</button>
    </div>
  </div>
</template>

<style scoped>
/* Add video background styles */
.video-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: -1;
  overflow: hidden;
}

.video-background video {
  position: absolute;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: hsla(248, 62%, 24%, 0.250);
  /* Semi-transparent white overlay */
}

/* Update existing container styles */
.about-container {
  position: relative;
    padding: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    background: hsla(0, 0%, 100%, 0);
    /* Semi-transparent white overlay */
  }
  
  .hero-section {
    text-align: center;
    margin-bottom: 2.5rem;
      padding: 2rem 1rem;
    }
    
    .company-brand {
      margin-bottom: 1.5rem;
    }
    
    .steepgraph-logo {
      height: 40px;
      width: auto;
    }
    
    h1 {
      color: #ffffff;
      font-size: 2.5rem;
      margin-bottom: 0.75rem;
      font-weight: 700;
    }
    
    h2 {
      color: #ffffff;
      font-size: 1.8rem;
      margin-bottom: 1rem;
      font-weight: 600;
    }
    
    .hero-description {
      font-size: 1.2rem;
      color: white;
      max-width: 800px;
      margin: 0 auto;
      line-height: 1.6;
      text-align: justify;
    }
    
    .content-sections {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      margin-bottom: 2rem;
    }
    
    .section {
      padding: 1.5rem;
      background: hsl(0, 0%, 100%);
      /* Semi-transparent white overlay */
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(82, 55, 204, 0.08);
    }
    
    .section h3 {
      color: #211761;
      font-size: 1.6rem;
      margin-bottom: 1rem;
      font-weight: 600;
    }
    
    .section-content {
      color: var(--text-secondary);
      line-height: 1.8;
      font-size: 1.1rem;
      text-align: justify;
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 1.5rem;
      margin-top: 1.5rem;
    }
    
    .feature-card {
      padding: 1.25rem;
      text-align: center;
      /* Ensure entire card content is centered */
      background: #f8f9fa;
      border-radius: 10px;
      transition: transform 0.2s ease;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
    }
    
    .feature-card i {
      font-size: 1.75rem;
      color: #5237cc;
      margin-bottom: 0.75rem;
    }
    
    .feature-card h4 {
      color: #211761;
      font-size: 1.1rem;
      margin-bottom: 0.6rem;
      font-weight: 600;
      text-align: center;
      /* Ensure heading is centered */
    }
    
    .feature-card p {
      color: var(--text-secondary);
      font-size: 0.95rem;
      line-height: 1.6;
      text-align: center;
      /* Changed from justify to center */
    }
    
    .cta-section {
      text-align: center;
      padding: 2rem 1.5rem;
      /* Reduced from 3rem 2rem */
      background: linear-gradient(135deg, #211761 0%, #5237cc 100%);
      border-radius: 16px;
      color: white;
      margin-bottom: 1.5rem;
    }
    
    .cta-section h3 {
      font-size: 1.8rem;
      margin-bottom: 0.5rem;
      /* Reduced from 0.75rem */
      font-weight: 600;
    }
    
    .cta-section p {
      font-size: 1.1rem;
      margin-bottom: 1rem;
      /* Reduced from 1.5rem */
      opacity: 0.9;
      text-align: center;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .cta-button {
      padding: 0.6rem 1.5rem;
      /* Reduced from 0.875rem 2.25rem */
      font-size: 1.1rem;
      font-weight: 600;
      background: white;
      color: #211761;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
    
    @media (max-width: 768px) {
      .hero-section {
        padding: 1.5rem 1rem;
      }
    
      h1 {
        font-size: 2rem;
      }
    
      h2 {
        font-size: 1.5rem;
      }
    
      .section h3 {
        font-size: 1.5rem;
      }
    
      .features-grid {
        grid-template-columns: 1fr;
      }
    
      .cta-section {
        padding: 1.5rem 1rem;
        /* Further reduced padding for mobile */
      }
    
      .cta-section h3 {
        font-size: 1.5rem;
      }
    
      .cta-section p {
        font-size: 1rem;
      }
    
      .cta-button {
        padding: 0.5rem 1.25rem;
        /* Even smaller padding on mobile */
        font-size: 1rem;
      }
    
      @media (max-width: 768px) {
        .video-background video {
          /* Adjust video positioning for mobile if needed */
          width: 100%;
          height: 100%;
        }
      }
    }
</style>
