const si = require('systeminformation');
const axios = require('axios');
const db = require('../db');
const { getLocalIPAddress } = require('../utils/networkUtils');
// const { cleanupOldData } = require('../models/healthData');

async function getHealthData() {
    try {
        const [cpu, mem, disk, networkStats, graphics] = await Promise.all([
            si.currentLoad(),
            si.mem(),
            si.fsSize(),
            si.networkStats(),
            si.graphics().catch(() => ({ controllers: [] }))
        ]);

        const now = new Date();
        const date = now.toISOString().split('T')[0];
        const time = now.toTimeString().split(' ')[0];

        // Ensure we have at least one network interface
        const primaryNetwork = networkStats[0] || { rx_sec: 0, tx_sec: 0 };

        // Standardize GPU data format
        const gpuController = graphics.controllers[0];
        const gpu = gpuController ? {
            usage: Math.round(gpuController.utilizationGpu * 100) / 100 || 0,
            memoryUsed: Math.round(gpuController.memoryUsed * 100) / 100 || 0,
            memoryTotal: Math.round(gpuController.memoryTotal * 100) / 100 || 0,
            temperature: Math.round(gpuController.temperatureGpu * 100) / 100 || 0
        } : {
            usage: 0,
            memoryUsed: 0,
            memoryTotal: 0,
            temperature: 0
        };

        return {
            date,
            time,
            cpu: {
                usage: Math.round(cpu.currentLoad * 10) / 10 || 0
            },
            memory: {
                used: Math.round((mem.used || 0) / (1024 * 1024 * 1024) * 100) / 100,
                total: Math.round((mem.total || 0) / (1024 * 1024 * 1024) * 100) / 100,
                usage: Math.round(((mem.used || 0) / (mem.total || 1) * 100) * 10) / 10
            },
            disk: {
                used: Math.round((disk[0]?.used || 0) / (1024 * 1024 * 1024) * 100) / 100,
                total: Math.round((disk[0]?.size || 0) / (1024 * 1024 * 1024) * 100) / 100,
                usage: Math.round((disk[0]?.use || 0) * 10) / 10
            },
            network: {
                download_speed: Math.round(primaryNetwork.rx_sec * 100) / 100 || 0,
                upload_speed: Math.round(primaryNetwork.tx_sec * 100) / 100 || 0
            },
            gpu
        };
    } catch (error) {
        console.error('Error collecting health data:', error);
        const now = new Date();
        return {
            date: now.toISOString().split('T')[0],
            time: now.toTimeString().split(' ')[0],
            cpu: { usage: 0 },
            memory: { used: 0, total: 0, usage: 0 },
            disk: { used: 0, total: 0, usage: 0 },
            network: { download_speed: 0, upload_speed: 0 },
            gpu: {
                usage: 0,
                memoryUsed: 0,
                memoryTotal: 0,
                temperature: 0
            }
        };
    }
}

async function getMasterHealthData() {
    return await getHealthData();
}

async function saveHealthData(serverIp, data, isMaster = false) {
    if (!data || typeof data !== 'object') {
        throw new Error('Invalid health data provided');
    }

    try {
        validateHealthData(data);

        // Get master IP if this is master server data
        const actualServerIp = isMaster ? await getLocalIPAddress() : serverIp;

        const sql = `INSERT INTO health_data (
            server_ip, date, time, cpu_usage, memory_used, memory_total, memory_usage,
            disk_used, disk_total, disk_usage, network_download, network_upload,
            gpu_data, server_type
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

        const values = [
            actualServerIp, // Now using actual server IP for both master and slave
            data.date,
            data.time,
            data.cpu.usage,
            data.memory.used,
            data.memory.total,
            data.memory.usage,
            data.disk.used,
            data.disk.total,
            data.disk.usage,
            data.network.download_speed,
            data.network.upload_speed,
            JSON.stringify(data.gpu),
            isMaster ? 'master' : 'slave'
        ];

        return new Promise((resolve, reject) => {
            db.run(sql, values, function (err) {
                if (err) reject(err);
                else resolve(this.lastID);
            });
        });
    } catch (error) {
        console.error('Error saving health data:', error);
        throw error;
    }
}

async function collectAllHealthData() {
    try {
        const masterHealth = await getMasterHealthData();
        await saveHealthData(null, masterHealth, true);

        const results = {
            master: masterHealth,
            slaves: {},
            errors: []
        };

        return new Promise((resolve, reject) => {
            // Updated query to use the new servers table structure
            db.all('SELECT server_ip, server_name FROM servers WHERE server_type = "slave"', async (err, slaves) => {
                if (err) return reject(err);

                if (!slaves || slaves.length === 0) {
                    return resolve(results);
                }

                const slavePromises = slaves.map(slave =>
                    axios.get(`http://${slave.server_ip}:3000/health`, {
                        timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT_MS, 10) || 10000
                    })
                        .then(async response => {
                            if (response.data && typeof response.data === 'object') {
                                await saveHealthData(slave.server_ip, response.data);
                                results.slaves[slave.server_ip] = response.data;
                                console.log(`Successfully collected health data from ${slave.server_ip} (${slave.server_name})`);
                            } else {
                                throw new Error('Invalid health data received from slave');
                            }
                        })
                        .catch(error => {
                            const errorMessage = error.code === 'ECONNREFUSED'
                                ? 'Connection refused - server might be down'
                                : error.code === 'ETIMEDOUT'
                                    ? 'Connection timed out - server might be overloaded'
                                    : error.message;

                            console.error(`Health check failed for ${slave.server_ip} (${slave.server_name}):`, {
                                error: errorMessage,
                                code: error.code
                            });

                            results.errors.push({
                                server_ip: slave.server_ip,
                                server_name: slave.server_name,
                                error: errorMessage,
                                code: error.code,
                                timestamp: new Date().toISOString()
                            });
                        })
                );

                await Promise.all(slavePromises);
                resolve(results);
            });
        });
    } catch (error) {
        console.error('Error in collectAllHealthData:', error);
        throw error;
    }
}

function validateHealthData(data) {
    if (!data || typeof data !== 'object') {
        throw new Error('Health data must be an object');
    }

    const required = ['date', 'time', 'cpu', 'memory', 'disk', 'network'];
    for (const field of required) {
        if (!data[field]) {
            throw new Error(`Missing required field: ${field}`);
        }
    }

    // Validate nested objects with null checks
    if (!data.cpu || typeof data.cpu.usage !== 'number') throw new Error('Missing CPU usage data');

    if (!data.memory ||
        typeof data.memory.used !== 'number' ||
        typeof data.memory.total !== 'number' ||
        typeof data.memory.usage !== 'number') {
        throw new Error('Missing memory data');
    }

    if (!data.disk ||
        typeof data.disk.used !== 'number' ||
        typeof data.disk.total !== 'number' ||
        typeof data.disk.usage !== 'number') {
        throw new Error('Missing disk data');
    }

    if (!data.network ||
        typeof data.network.download_speed !== 'number' ||
        typeof data.network.upload_speed !== 'number') {
        throw new Error('Missing network data');
    }

    // GPU data is optional, no validation needed
}

module.exports = {
    getHealthData,
    getMasterHealthData,
    collectAllHealthData,
    saveHealthData
};















