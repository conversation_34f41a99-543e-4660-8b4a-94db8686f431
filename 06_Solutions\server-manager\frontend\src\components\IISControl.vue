<script setup>
import { ref, onMounted, computed } from 'vue';
import { userState } from '../main';
import ConfirmationDialog from './ConfirmationDialog.vue';

const VITE_MASTER_URL = import.meta.env.VITE_MASTER_URL;
const servers = ref([]);
const isLoading = ref(false);
const error = ref(null);
const confirmDialog = ref(null);
const pendingAction = ref(null);
const pendingServer = ref(null);
const statusError = ref({});

// Add this computed property
const getButtonStates = (server) => {
  if (!server.iisStatus || server.isActionLoading) {
    return {
      start: true,
      stop: true,
      restart: true
    };
  }

  return {
    start: !server.iisStatus.running,    // Enable start only when service is stopped
    stop: server.iisStatus.running,      // Enable stop only when service is running
    restart: server.iisStatus.running    // Enable restart only when service is running
  };
};

// Fetch IIS status for a server
const fetchIISStatus = async (server) => {
  try {
    const response = await fetch(`${VITE_MASTER_URL}/iis-status?server_ip=${server.server_ip}`, {
      headers: {
        'Authorization': `Bearer ${userState.user.token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(response.status === 500 ? 'Internal Server Error' : 'Failed to fetch IIS status');
    }

    const status = await response.json();
    server.iisStatus = {
      ...status,
      running: status.status === 'running',
      stopped: status.status === 'stopped'
    };
    statusError.value[server.server_ip] = null; // Clear any previous error
    return server.iisStatus;
  } catch (err) {
    console.error('Error fetching IIS status:', err);
    statusError.value[server.server_ip] = true;
    server.iisStatus = null;
  }
};

// Modified fetchServers to include IIS status
const fetchServers = async () => {
  try {
    isLoading.value = true;
    error.value = null;
    const response = await fetch(`${VITE_MASTER_URL}/serverlist`, {
      headers: {
        'Authorization': `Bearer ${userState.user.token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      if (response.status === 401 || response.status === 403) {
        router.push('/');
        throw new Error('Unauthorized access');
      }
      throw new Error('Failed to fetch servers');
    }

    const data = await response.json();
    servers.value = data.servers;

    // Fetch IIS status for each server
    await Promise.all(servers.value.map(server => fetchIISStatus(server)));
  } catch (err) {
    console.error('Error fetching servers:', err);
    error.value = 'Failed to load servers';
  } finally {
    isLoading.value = false;
  }
};

const handleServiceAction = async (server, action) => {
  pendingServer.value = server;
  pendingAction.value = action;

  const actionMessages = {
    start: 'Start',
    stop: 'Stop',
    restart: 'Restart'
  };

  confirmDialog.value.toggleDialog();
};

const executeServiceAction = async () => {
  const server = pendingServer.value;
  const action = pendingAction.value;

  try {
    server.isActionLoading = true;
    const response = await fetch(`${VITE_MASTER_URL}/iis-control`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userState.user.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        server_ip: server.server_ip,
        action: action
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to ${action} IIS service`);
    }

    const result = await response.json();

    // Refresh the IIS status after action
    await fetchIISStatus(server);

    // Reset pending values
    pendingServer.value = null;
    pendingAction.value = null;
  } catch (err) {
    console.error(`Error executing ${action}:`, err);
    error.value = err.message;
  } finally {
    server.isActionLoading = false;
  }
};

onMounted(fetchServers);
</script>

<template>
  <div class="iis-control">
    <!-- Header Section -->
    <div class="header-wrapper">
      <div class="header">
        <div class="header-left">
          <h1>IIS Service Control</h1>
        </div>
        <div class="header-actions">
          <button class="refresh-btn" @click="fetchServers" :disabled="isLoading">
            <i class="pi pi-refresh" :class="{ 'rotating': isLoading }"></i>
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state">
      <i class="pi pi-spinner rotating"></i>
      <span>Loading servers...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-state">
      <i class="pi pi-exclamation-triangle"></i>
      <span>{{ error }}</span>
      <button @click="fetchServers" class="retry-btn">
        <i class="pi pi-refresh"></i>
        Retry
      </button>
    </div>

    <!-- Servers Grid -->
    <div v-else class="servers-grid">
      <div v-for="server in servers" :key="server.server_ip" class="server-card">
        <div class="server-header">
          <i :class="server.server_type === 'master' ? 'pi pi-database' : 'pi pi-server'"></i>
          <span class="server-type-badge" :class="server.server_type">
            {{ server.server_type }}
          </span>
        </div>
        <div class="server-info">
          <h2>{{ server.server_name }}</h2>
          <span class="server-ip">{{ server.server_ip }}</span>
        </div>

        <div class="controls-grid">
          <div class="control-card">
            <!-- Add error message display -->
            <div v-if="statusError[server.server_ip]" class="status-error">
              <i class="pi pi-exclamation-triangle"></i>
              <div class="error-message">
                <p>Unable to determine IIS service status</p>
                <p class="error-hint">Please verify the service status by logging into the server directly. There might
                  be connectivity issues or the service might be in an unexpected state.</p>
              </div>
            </div>

            <!-- Show controls only if no error -->
            <div v-else class="control-actions">
              <button class="action-btn start" :disabled="server.isActionLoading || !getButtonStates(server).start"
                @click="handleServiceAction(server, 'start')">
                <i class="pi" :class="server.isActionLoading ? 'pi-spinner rotating' : 'pi-play'"></i>
                <span>Start</span>
              </button>
              <button class="action-btn stop" :disabled="server.isActionLoading || !getButtonStates(server).stop"
                @click="handleServiceAction(server, 'stop')">
                <i class="pi pi-stop"></i>
                <span>Stop</span>
              </button>
              <button class="action-btn restart" :disabled="server.isActionLoading || !getButtonStates(server).restart"
                @click="handleServiceAction(server, 'restart')">
                <i class="pi pi-refresh"></i>
                <span>Restart</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add ConfirmationDialog component -->
    <ConfirmationDialog ref="confirmDialog" :title="'Confirm Service Action'"
      :message="`Are you sure you want to ${pendingAction} the IIS service on ${pendingServer?.server_name || ''}?`"
      @confirmed="executeServiceAction" />
  </div>
</template>

<style scoped>
.iis-control {
  padding: 0;
  /* Remove padding from container */
  background: var(--bg-primary);
  min-height: calc(100vh - 60px);
}

.header-wrapper {
  position: sticky;
  top: 48px;
  z-index: 100;
  background: var(--bg-primary);
  padding: 0.5rem 1.5rem;
  margin-bottom: 0.5rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.375rem 0.75rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(33, 23, 97, 0.08);
}

.header-left h1 {
  color: var(--primary-dark);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.2;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  /* Reduced from 1rem */
}

.refresh-btn {
  padding: 0.3rem 0.6rem;
  /* Reduced from 0.425rem 0.8rem */
  border-radius: 2rem;
  border: 1px solid var(--border-color);
  background: white;
  color: var(--text-primary);
  font-size: 0.75rem;
  /* Reduced from 0.875rem */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  /* Reduced from 0.5rem */
  width: fit-content;
  /* Changed from 100% to fit-content */
  justify-content: center;
}

.refresh-btn i {
  font-size: 0.75rem;
  /* Added to make icon smaller */
}

.refresh-btn:hover {
  background: var(--primary-light);
  color: white;
  border-color: var(--primary-light);
}

.refresh-btn.active {
  background: var(--primary-dark);
  color: white;
  border-color: var(--primary-dark);
}

.refresh-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Media queries to match MonitoringPage */
@media (max-width: 768px) {
  .header-wrapper {
    padding: 0.5rem;
    /* Reduced from 1rem */
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding: 1rem;
  }

  .header-left h1 {
    font-size: 1.125rem;
    /* Reduced from 1.5rem for mobile */
  }
}

.servers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
  padding: 0 2rem 2rem;
  /* Add padding here instead */
}

.server-card {
  background: white;
  /* Changed from #f8f9fa to white */
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(33, 23, 97, 0.1);
  /* Updated to match content-wrapper shadow */
}

.server-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-bottom: 0.75rem; */
}

.server-header i {
  font-size: 1.25rem;
  color: var(--primary-dark);
}

.server-type-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  text-transform: capitalize;
}

.server-type-badge.master {
  background: #e1f5fe;
  color: #0288d1;
}

.server-type-badge.slave {
  background: #f3e5f5;
  color: #7b1fa2;
}

.server-info {
  margin-bottom: 1rem;
}

.server-info h2 {
  color: var(--primary-dark);
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.server-ip {
  color: var(--text-secondary);
  font-size: 0.875rem;
  display: block;
  /* margin-top: 0.1rem; */
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.unknown {
  background: #f5f5f5;
  color: #666;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.controls-grid {
  display: grid;
  gap: 0.5rem;
  /* Reduced from 1rem */
}

.control-card {
  background: white;
  border-radius: 6px;
  /* padding: 0.625rem;  */
}

/* .control-header {
  margin-bottom: 0.5rem; 
}

.control-title {
  display: flex;
  align-items: center;
  gap: 0.375rem; 
  color: var(--primary-dark);
  font-weight: 500;
} */

.control-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  /* Reduced from 0.5rem */
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.3rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(0.5);
}

.action-btn:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(33, 23, 97, 0.15);
}

.action-btn.start {
  background: #e6f4ea;
  color: #1e7e34;
}

.action-btn.start:not(:disabled):hover {
  background: #d4eadc;
  color: #156926;
}

.action-btn.start:disabled {
  background: #f0f0f0;
  color: #888;
}

.action-btn.stop {
  background: #fbe9e7;
  color: #d32f2f;
}

.action-btn.stop:not(:disabled):hover {
  background: #f8dbd7;
  color: #b71c1c;
}

.action-btn.restart {
  background: #e3f2fd;
  color: #1976d2;
}

.action-btn.restart:not(:disabled):hover {
  background: #d0e8fb;
  color: #0d47a1;
}

.action-btn i {
  font-size: 1rem;
  transition: transform 0.2s ease;
}

.action-btn:not(:disabled):hover i {
  transform: scale(1.1);
}

.action-btn span {
  font-size: 0.7rem;
  font-weight: 600;
}

/* Add ripple effect */
.action-btn:not(:disabled):active {
  transform: translateY(0);
}

.action-btn:not(:disabled)::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.action-btn:not(:disabled):active::after {
  animation: ripple 0.5s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }

  100% {
    transform: scale(40, 40);
    opacity: 0;
  }
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(33, 23, 97, 0.1);
}

.loading-state i,
.error-state i {
  font-size: 2rem;
  color: var(--primary);
}

.error-state {
  color: #d32f2f;
}

.retry-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .iis-control {
    padding: 0 0.75rem 0.75rem;
    /* Adjusted padding for mobile */
  }

  .servers-grid {
    grid-template-columns: 1fr;
  }

  .header {
    padding: 0.75rem;
  }

  .control-actions {
    grid-template-columns: repeat(3, 1fr);
    /* Keep 3 columns even on mobile */
    gap: 0.25rem;
    /* Further reduce gap on mobile */
  }

  .action-btn {
    padding: 0.375rem;
    /* Even smaller padding on mobile */
  }
}

.status-error {
  padding: 1rem;
  background-color: #fff3f3;
  border-radius: 6px;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.status-error .pi-exclamation-triangle {
  color: #dc3545;
  font-size: 1.25rem;
  margin-top: 0.125rem;
}

.error-message {
  flex: 1;
}

.error-message p {
  margin: 0;
  color: #dc3545;
  font-weight: 500;
}

.error-message .error-hint {
  margin-top: 0.5rem;
  color: #6c757d;
  font-size: 0.875rem;
  font-weight: normal;
  line-height: 1.4;
}
</style>
