const { collectAllHealthData } = require('../services/healthService');
const db = require('../db');

async function getRealTimeHealth(req, res) {
    try {
        const healthData = await collectAllHealthData();
        
        // Get master server IP from database
        const masterServer = await new Promise((resolve, reject) => {
            db.get('SELECT server_ip FROM servers WHERE server_type = "master"', (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        const masterIP = masterServer?.server_ip;
        
        // Combine and transform the response
        const servers = {};
        
        // Add master server data
        if (masterIP && healthData.master) {
            servers[masterIP] = healthData.master;
        }
        
        // Add slave servers data
        Object.entries(healthData.slaves || {}).forEach(([slaveIP, slaveData]) => {
            servers[slaveIP] = slaveData;
        });

        const response = {
            timestamp: new Date().toISOString(),
            servers,
            status: {
                total: Object.keys(servers).length,
                online: Object.keys(servers).length - healthData.errors.length,
                errors: healthData.errors
            }
        };

        res.json(response);
    } catch (error) {
        console.error('Error fetching realtime health data:', error);
        res.status(500).json({ 
            error: 'Failed to fetch realtime health data',
            details: error.message 
        });
    }
}

module.exports = { getRealTimeHealth };
