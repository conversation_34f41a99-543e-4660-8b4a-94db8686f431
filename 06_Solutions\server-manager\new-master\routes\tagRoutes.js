const express = require('express');
const router = express.Router();
const {
    getAllTags,
    getTagById,
    createTag,
    updateTag,
    deleteTag
} = require('../controllers/tagController');
// const authenticateAdmin = require('../middleware/authenticateAdmin');

/**
 * @route   GET /tags
 * @desc    Get all tags
 * @access  Public
 */
router.get('/', getAllTags);

/**
 * @route   GET /tags/:id
 * @desc    Get tag by ID
 * @access  Public
 */
router.get('/:id', getTagById);

/**
 * @route   POST /tags
 * @desc    Create a new tag
 * @access  Private (Admin only)
 */
router.post('/',
    // authenticateAdmin,
    createTag);

/**
 * @route   PUT /tags/:id
 * @desc    Update an existing tag
 * @access  Private (Admin only)
 */
router.put('/:id',
    // authenticateAdmin,
    updateTag);

/**
 * @route   DELETE /tags/:id
 * @desc    Delete a tag
 * @access  Private (Admin only)
 */
router.delete('/:id',
    // authenticateAdmin,
    deleteTag);

module.exports = router;
