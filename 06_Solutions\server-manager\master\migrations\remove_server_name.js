const db = require('../db');

// Create a temporary table with the new schema
db.serialize(() => {
    db.run(`
        CREATE TABLE INSTANCE_DATA_NEW (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            server_ip TEXT NOT NULL,
            instance_name TEXT NOT NULL,
            instance_title TEXT,
            instance_url TEXT NOT NULL,
            description TEXT,
            instance_owner TEXT,
            is_public INTEGER DEFAULT 0
        )
    `);

    // Copy data from old table to new table
    db.run(`
        INSERT INTO INSTANCE_DATA_NEW (
            ID, server_ip, instance_name, instance_title, 
            instance_url, description, instance_owner, is_public
        )
        SELECT 
            ID, server_ip, instance_name, instance_title,
            instance_url, description, instance_owner, is_public
        FROM INSTANCE_DATA
    `);

    // Drop old table
    db.run(`DROP TABLE INSTANCE_DATA`);

    // Rename new table to original name
    db.run(`ALTER TABLE INSTANCE_DATA_NEW RENAME TO INSTANCE_DATA`);

    console.log('✅ Migration completed successfully');
    db.close();
});