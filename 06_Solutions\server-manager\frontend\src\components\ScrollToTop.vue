<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
});

const showScrollButton = ref(false);

const handleScroll = () => {
  if (!props.disabled) {
    showScrollButton.value = window.scrollY > 300;
  } else {
    showScrollButton.value = false;
  }
};

const scrollToTop = () => {
  if (!props.disabled) {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }
};

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>

<template>
  <button 
    v-show="showScrollButton && !disabled" 
    @click="scrollToTop" 
    class="scroll-to-top-btn"
    title="Scroll to top"
  >
    <i class="pi pi-arrow-up"></i>
  </button>
</template>

<style scoped>
.scroll-to-top-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #211761;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  z-index: 1000;
  /* opacity: 1; */
}

.scroll-to-top-btn:hover {
  background-color: #5237cc;
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

.scroll-to-top-btn i {
  font-size: 1.5rem;
}

html {
  scroll-behavior: smooth;
}
</style>

