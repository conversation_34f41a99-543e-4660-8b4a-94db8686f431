// controllers/adminController.js
const db = require('../db');

/**
 * Updates allowed fields for an instance.
 * Allowed fields: title, description, owner, is_public.
 */
function updateInstance(req, res) {
    const id = req.params.id;
    const { title, description, owner, is_public } = req.body;
    const fields = [];
    const values = [];
    if (title !== undefined) {
        fields.push('title = ?');
        values.push(title);
    }
    if (description !== undefined) {
        fields.push('description = ?');
        values.push(description);
    }
    if (owner !== undefined) {
        fields.push('owner = ?');
        values.push(owner);
    }
    if (is_public !== undefined) {
        fields.push('is_public = ?');
        values.push(is_public ? 1 : 0);
    }

    if (fields.length === 0) {
        return res.status(400).json({ error: 'No valid fields to update' });
    }

    const query = `UPDATE INSTANCE_DATA SET ${fields.join(', ')} WHERE ID = ?`;
    values.push(id);

    db.run(query, values, function (err) {
        if (err) {
            return res.status(500).json({ error: err.message });
        }
        res.json({
            message: 'Instance updated successfully',
            changes: this.changes
        });
    });
}

/**
 * Updates the server_name for a server.
 */
function updateServer(req, res) {
    const id = req.params.id;
    const { server_name } = req.body;

    if (!server_name) {
        return res.status(400).json({ error: "server_name is required" });
    }

    const sql = `UPDATE servers SET server_name = ? WHERE id = ?`;
    db.run(sql, [server_name, id], function(err) {
        if (err) {
            console.error("Error updating server:", err.message);
            return res.status(500).json({ error: err.message });
        }
        res.json({ 
            message: "Server updated successfully", 
            changes: this.changes,
            server: {
                id: parseInt(id),
                server_name: server_name,
                type: id === '0' ? 'master' : 'slave'
            }
        });
    });
}

module.exports = {
    updateInstance,
    updateServer
};
