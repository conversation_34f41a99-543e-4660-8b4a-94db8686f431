const db = require('../db');

// Helper function to run queries in promises
const runQuery = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        db.run(sql, params, function(err) {
            if (err) reject(err);
            resolve(this);
        });
    });
};

// Helper function to get all rows
const getAllRows = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        db.all(sql, params, (err, rows) => {
            if (err) reject(err);
            resolve(rows);
        });
    });
};

const databaseController = {
    // Remove all instances
    async removeAllInstances(req, res) {
        try {
            const result = await runQuery('DELETE FROM INSTANCE_DATA');
            res.json({ message: 'All instances removed successfully', rowsAffected: result.changes });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    // Remove instances by server IP
    async removeInstancesByIP(req, res) {
        const { server_ip } = req.params;
        try {
            const result = await runQuery('DELETE FROM INSTANCE_DATA WHERE server_ip = ?', [server_ip]);
            res.json({ message: `Instances for IP ${server_ip} removed`, rowsAffected: result.changes });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    // Remove all health data
    async removeAllHealthData(req, res) {
        try {
            const result = await runQuery('DELETE FROM health_data');
            res.json({ message: 'All health data removed', rowsAffected: result.changes });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    // Remove health data by server IP
    async removeHealthDataByIP(req, res) {
        const { server_ip } = req.params;
        try {
            const result = await runQuery('DELETE FROM health_data WHERE server_ip = ?', [server_ip]);
            res.json({ message: `Health data for IP ${server_ip} removed`, rowsAffected: result.changes });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    // Set all instances to private
    async setAllInstancesPrivate(req, res) {
        try {
            const result = await runQuery('UPDATE INSTANCE_DATA SET is_public = 0');
            res.json({ message: 'All instances set to private', rowsAffected: result.changes });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    // Remove all non-admin users
    async removeNonAdminUsers(req, res) {
        try {
            const result = await runQuery('DELETE FROM users WHERE role != "admin"');
            res.json({ message: 'Non-admin users removed', rowsAffected: result.changes });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    // Create new user
    async createUser(req, res) {
        const { username, email, password, role } = req.body;
        try {
            const hashedPassword = await bcrypt.hash(password, 10);
            const result = await runQuery(
                'INSERT INTO users (username, email, password, role, created_at) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)',
                [username, email, hashedPassword, role]
            );
            res.json({ message: 'User created successfully', id: result.lastID });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    // Remove server and related data
    async removeServer(req, res) {
        const { server_ip } = req.params;
        try {
            await db.run('BEGIN TRANSACTION');

            // Remove server from servers table
            await runQuery('DELETE FROM servers WHERE server_ip = ?', [server_ip]);
            
            // Remove related instances
            await runQuery('DELETE FROM INSTANCE_DATA WHERE server_ip = ?', [server_ip]);
            
            // Remove related health data
            await runQuery('DELETE FROM health_data WHERE server_ip = ?', [server_ip]);

            await db.run('COMMIT');
            
            res.json({ message: `Server ${server_ip} and related data removed successfully` });
        } catch (error) {
            await db.run('ROLLBACK');
            res.status(500).json({ error: error.message });
        }
    },

    // Add new server
    async addServer(req, res) {
        const { server_ip, server_name } = req.body;
        try {
            const result = await runQuery(
                'INSERT INTO servers (server_ip, server_name) VALUES (?, ?)',
                [server_ip, server_name]
            );
            res.json({ message: 'Server added successfully', id: result.lastID });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    // Delete specific user
    async deleteUser(req, res) {
        const { username } = req.params;
        
        // Prevent admin from deleting themselves
        if (username === req.user.username) {
            return res.status(403).json({ error: 'Cannot delete your own admin account' });
        }

        try {
            // Check if trying to delete an admin
            const user = await getAllRows('SELECT role FROM users WHERE username = ?', [username]);
            if (user.length === 0) {
                return res.status(404).json({ error: 'User not found' });
            }
            if (user[0].role === 'admin') {
                return res.status(403).json({ error: 'Cannot delete admin users' });
            }

            const result = await runQuery('DELETE FROM users WHERE username = ?', [username]);
            if (result.changes === 0) {
                return res.status(404).json({ error: 'User not found' });
            }
            res.json({ message: 'User deleted successfully', rowsAffected: result.changes });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    // Get all users
    async getAllUsers(req, res) {
        try {
            const users = await getAllRows(
                'SELECT id, username, email, role, datetime(created_at) as created_at FROM users ORDER BY role DESC, username ASC'
            );
            
            const enhancedUsers = users.map(user => ({
                ...user,
                is_current_user: user.username === req.user.username,
                can_be_deleted: user.role !== 'admin'
            }));

            res.json({ 
                users: enhancedUsers,
                total_count: enhancedUsers.length,
                admin_count: enhancedUsers.filter(u => u.role === 'admin').length
            });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    },

    // Get all servers
    async getAllServers(req, res) {
        try {
            const servers = await getAllRows(`
                SELECT 
                    s.*,
                    (SELECT COUNT(*) FROM INSTANCE_DATA WHERE server_ip = s.server_ip) as instance_count,
                    (SELECT COUNT(*) FROM health_data WHERE server_ip = s.server_ip) as health_records_count
                FROM servers s
                ORDER BY s.server_name ASC
            `);

            const formattedServers = servers.map(server => ({
                id: server.id,
                server_ip: server.server_ip,
                server_name: server.server_name,
                stats: {
                    instances: parseInt(server.instance_count) || 0,
                    health_records: parseInt(server.health_records_count) || 0
                }
            }));

            res.json({ 
                servers: formattedServers,
                metadata: {
                    total_servers: formattedServers.length
                }
            });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    }
};

module.exports = databaseController;





