const { updateArasVersions } = require('../services/arasVersionService');
const db = require('../db/db');

const updateArasVersionsAuto = async (req, res) => {
    try {
        const result = await updateArasVersions();

        return res.status(200).json({
            success: true,
            ...result
        });
    } catch (err) {
        return res.status(500).json({
            success: false,
            message: err.message
        });
    }
};

// Update Aras Version Entry by application_id
const updateArasVersionById = async (req, res) => {
    try {
        const { application_id } = req.params;
        const { aras_version, build_number } = req.body;

        if (!aras_version && !build_number) {
            return res.status(400).json({
                success: false,
                message: 'At least one of aras version or build number must be provided'
            });
        }

        const fields = [];
        const values = [];

        if (aras_version) {
            fields.push('aras_version = ?');
            values.push(aras_version);
        }

        if (build_number) {
            fields.push('build_number = ?');
            values.push(build_number);
        }

        const query = `
            UPDATE aras_versions
            SET ${fields.join(', ')}
            WHERE application_id = ?`;

        values.push(application_id);

        db.run(query, values, function (err) {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: err.message
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'No entry found for the given application_id'
                });
            }

            return res.status(200).json({
                success: true,
                message: 'Aras version entry updated successfully'
            });
        });

    } catch (err) {
        return res.status(500).json({
            success: false,
            message: err.message
        });
    }
};

module.exports = {
    updateArasVersionsAuto,
    updateArasVersionById
};