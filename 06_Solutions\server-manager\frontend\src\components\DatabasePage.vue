<script setup>
import { ref, getCurrentInstance, computed } from 'vue';
import { userState } from '../main';
import { useRouter } from 'vue-router';
import ConfirmationDialog from './ConfirmationDialog.vue';

const router = useRouter();
const alert = getCurrentInstance()?.appContext.config.globalProperties.$alert;
const MASTER_URL = import.meta.env.VITE_MASTER_URL;
// Add new refs for Remove by Server functionality
const showRemoveByServerConfirmation = ref(false);
const isRemoving = ref(false);
const serverIpToRemove = ref('');

// Add handler for Remove by Server action
const handleRemoveByServerAction = async () => {
  if (!serverIpToRemove.value) {
    alert.show('Please enter a server IP address', 'error', 3000);
    return;
  }

  if (!isValidIPv4(serverIpToRemove.value)) {
    alert.show('Invalid IP address format. Must be between 0.0.0.0 and ***************', 'error', 3000);
    return;
  }

  pendingAction.value = async () => {
    try {
      if (!validateToken()) return;

      isRemoving.value = true;
      const response = await fetch(`${MASTER_URL}/database/instances/ip/${serverIpToRemove.value}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userState.user.token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        handleUnauthorized(response);
        throw new Error(data.error || 'Failed to remove instances');
      }

      alert.show(`Successfully removed ${data.rowsAffected} instances from server ${serverIpToRemove.value}`, 'success', 3000);
      showRemoveByServerConfirmation.value = false;
      selectedAction.value = null;
      serverIpToRemove.value = '';
    } catch (error) {
      console.error('Error in handleRemoveByServerAction:', error);
      alert.show(error.message || 'Failed to remove instances', 'error', 5000);
    } finally {
      isRemoving.value = false;
    }
  };
  confirmationDialog.value.toggleDialog();
};

// Add this function to handle token validation
const validateToken = () => {
  if (!userState.isLoggedIn || !userState.user?.token) {
    alert.show('Please log in to continue', 'error', 5000);
    router.push('/');
    return false;
  }
  return true;
};

const activeMenu = ref('instances');
const showRemoveConfirmation = ref(false);
const isPurging = ref(false);
const showPrivacyConfirmation = ref(false);
const isSettingPrivacy = ref(false);
const selectedAction = ref(null);

// Add new refs for server list
const servers = ref([]);
const isLoadingServers = ref(false);
const showServerList = ref(false);

// Add handler for View Server List action
const handleViewServerList = async () => {
  try {
    if (!validateToken()) {
      return;
    }

    isLoadingServers.value = true;
    const response = await fetch(`${MASTER_URL}/database/servers`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userState.user.token}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();

    if (!response.ok) {
      if (response.status === 401 || response.status === 403) {
        userState.isLoggedIn = false;
        userState.user = null;
        router.push('/');
        alert.show('Session expired. Please log in again.', 'error', 5000);
        return;
      }
      throw new Error(data.error || 'Failed to fetch server list');
    }

    servers.value = data.servers;
    showServerList.value = true;

  } catch (error) {
    console.error('Error in handleViewServerList:', error);
    alert.show(error.message || 'Failed to fetch server list', 'error', 5000);
  } finally {
    isLoadingServers.value = false;
  }
};

const menuItems = [
  {
    id: 'instances',
    label: 'Instance Management',
    icon: 'pi pi-server',
  },
  {
    id: 'servers',
    label: 'Server Management',
    icon: 'pi pi-database',
  },
  {
    id: 'health',
    label: 'Performance & Health',
    icon: 'pi pi-chart-line',
  }
  // ,
  // { 
  //   id: 'users', 
  //   label: 'User Administration', 
  //   icon: 'pi pi-users',
  // }
];

const getContent = (menuId) => {
  switch (menuId) {
    case 'instances':
      return {
        title: 'Instance Management',
        // description: 'Control database instance visibility and perform bulk cleanup operations across servers.',
        actions: [
          'Remove All Instances',
          'Set Global Privacy',
          'Remove By Server'
        ],
        metrics: [
          { label: 'Total Instances', value: '48' },
          { label: 'Public Instances', value: '15' },
          { label: 'Private Instances', value: '33' },
          { label: 'Active Owners', value: '12' }
        ]
      };
    case 'servers':
      return {
        title: 'Server Management',
        // description: 'Configure master-slave relationships and manage server deployments across your infrastructure.',
        actions: [
          'View Server List',
          'Register New Server',
          'Remove Server'
        ],
        metrics: [
          { label: 'Total Servers', value: '8' },
          { label: 'Master Servers', value: '1' },
          { label: 'Slave Servers', value: '7' },
          { label: 'Active Servers', value: '8' }
        ]
      };
    case 'health':
      return {
        title: 'Performance & Health',
        // description: 'Monitor server performance metrics and manage historical health data records.',
        actions: [
          'Remove All Health Data',
          'Remove Health By Server',
          'View Table Schema'
        ],
        metrics: [
          { label: 'Total Health Records', value: '2,456' },
          { label: 'Monitored Servers', value: '8' },
          { label: "Today's Entries", value: '144' },
          { label: 'Storage Used', value: '256 MB' }
        ]
      };
    // case 'users':
    //   return {
    //     title: 'User Administration',
    //     // description: 'Manage user roles, permissions, and monitor active sessions across the platform.',
    //     actions: [
    //       'View All Users',
    //       'Add New User'
    //     ],
    //     metrics: [
    //       { label: 'Total Users', value: '156' },
    //       { label: 'Admin Users', value: '5' },
    //       { label: 'Regular Users', value: '151' },
    //       { label: 'Active Sessions', value: '23' }
    //     ]
    //   };
    // default:
    //   return {
    //     title: 'Select a Menu',
    //     description: 'Please select a menu item from the left sidebar.',
    //     actions: [],
    //     metrics: []
    //   };
  }
};

const handleRemoveAction = async () => {
  pendingAction.value = async () => {
    try {
      if (!validateToken()) return;

      isPurging.value = true;
      const response = await fetch(`${MASTER_URL}/database/instances/all`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userState.user.token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        handleUnauthorized(response);
        throw new Error(data.error || 'Failed to Remove instances');
      }

      showRemoveConfirmation.value = false;
      selectedAction.value = null;
      alert.show(`Successfully removed ${data.rowsAffected} instances`, 'success', 3000);
    } catch (error) {
      console.error('Error in handleRemoveAction:', error);
      alert.show(error.message || 'Failed to Remove instances', 'error', 5000);
    } finally {
      isPurging.value = false;
    }
  };
  confirmationDialog.value.toggleDialog();
};

const handleSetPrivacyAction = async () => {
  pendingAction.value = async () => {
    try {
      if (!validateToken()) return;

      isSettingPrivacy.value = true;
      const response = await fetch(`${MASTER_URL}/database/instances/private`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${userState.user.token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        handleUnauthorized(response);
        throw new Error(data.error || 'Failed to set instances to private');
      }

      alert.show('Successfully set all instances to private', 'success', 3000);
      showPrivacyConfirmation.value = false;
      selectedAction.value = null;
    } catch (error) {
      console.error('Error in handleSetPrivacyAction:', error);
      alert.show(error.message || 'Failed to set instances to private', 'error', 5000);
    } finally {
      isSettingPrivacy.value = false;
    }
  };
  confirmationDialog.value.toggleDialog();
};

const handleActionClick = (action) => {
  // Reset all confirmation dialogs first
  showRemoveConfirmation.value = false;
  showPrivacyConfirmation.value = false;
  showRemoveByServerConfirmation.value = false;
  showServerList.value = false;

  // Set the selected action
  selectedAction.value = action;

  // Show the appropriate dialog based on the action
  if (action === 'Remove All Instances') {
    showRemoveConfirmation.value = true;
  } else if (action === 'Set Global Privacy') {
    showPrivacyConfirmation.value = true;
  } else if (action === 'Remove By Server') {
    showRemoveByServerConfirmation.value = true;
  } else if (action === 'View Server List') {
    handleViewServerList();
  }
};

// Add these refs
const isRemovingServer = ref(false);
const serverToRemove = ref('');

// Add handler for Remove Server action
const handleRemoveServerAction = async () => {
  if (!serverToRemove.value) {
    alert.show('Please enter a server IP address', 'error', 3000);
    return;
  }
  pendingAction.value = async () => {
    try {
      if (!validateToken()) return;

      isRemovingServer.value = true;
      const response = await fetch(`${MASTER_URL}/database/servers/slave/${serverToRemove.value}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userState.user.token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        handleUnauthorized(response);
        throw new Error(data.error || 'Failed to remove server');
      }

      alert.show(data.message, 'success', 3000);
      selectedAction.value = null;
      serverToRemove.value = '';
    } catch (error) {
      console.error('Error in handleRemoveServerAction:', error);
      alert.show(error.message || 'Failed to remove server', 'error', 5000);
    } finally {
      isRemovingServer.value = false;
    }
  };
  confirmationDialog.value.toggleDialog();
};

// Add new refs
const isRegisteringServer = ref(false);
const newServer = ref({
  server_ip: '',
  server_name: ''
});

// Add handler for Register New Server action
const handleRegisterServerAction = async () => {
  if (!newServer.value.server_ip || !newServer.value.server_name) {
    alert.show('Please fill in all fields', 'error', 3000);
    return;
  }
  pendingAction.value = async () => {
    try {
      if (!validateToken()) return;

      isRegisteringServer.value = true;
      const response = await fetch(`${MASTER_URL}/database/servers/slave`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userState.user.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          server_ip: newServer.value.server_ip,
          server_name: newServer.value.server_name
        })
      });

      const data = await response.json();

      if (!response.ok) {
        handleUnauthorized(response);
        if (response.status === 400 && data.errors) {
          const errorMessages = data.errors.map(err => `${err.param}: ${err.msg}`).join(', ');
          throw new Error(errorMessages);
        }
        throw new Error(data.error || 'Failed to register server');
      }

      alert.show(`${data.message} (ID: ${data.id})`, 'success', 3000);
      selectedAction.value = null;
      newServer.value = { server_ip: '', server_name: '' };
    } catch (error) {
      console.error('Error in handleRegisterServerAction:', error);
      alert.show(error.message || 'Failed to register server', 'error', 5000);
    } finally {
      isRegisteringServer.value = false;
    }
  };
  confirmationDialog.value.toggleDialog();
};

// Add this function to map actions to icons
const getActionIcon = (action) => {
  switch (action) {
    case 'Remove All Instances':
      return 'pi pi-trash';
    case 'Set Global Privacy':
      return 'pi pi-lock';
    case 'Remove By Server':
      return 'pi pi-server';
    case 'Remove Server':
      return 'pi pi-times-circle';
    case 'Register New Server':
      return 'pi pi-plus-circle';
    default:
      return 'pi pi-cog';
  }
};

// Add new refs for Remove All Health Data functionality
const isRemovingHealthData = ref(false);

// Add handler for Remove All Health Data action
const handleRemoveAllHealthData = async () => {
  pendingAction.value = async () => {
    try {
      if (!validateToken()) return;

      isRemovingHealthData.value = true;
      const response = await fetch(`${MASTER_URL}/database/health/all`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userState.user.token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        handleUnauthorized(response);
        throw new Error(data.error || 'Failed to remove health data');
      }

      alert.show(`${data.message} (${data.rowsAffected} records)`, 'success', 3000);
      selectedAction.value = null;
    } catch (error) {
      console.error('Error in handleRemoveAllHealthData:', error);
      alert.show(error.message || 'Failed to remove health data', 'error', 5000);
    } finally {
      isRemovingHealthData.value = false;
    }
  };
  confirmationDialog.value.toggleDialog();
};

// Add new refs for Remove Health By Server functionality
const isRemovingServerHealth = ref(false);
const serverIpForHealthRemoval = ref('');

// Add handler for Remove Health By Server action
const handleRemoveHealthByServer = async () => {
  if (!serverIpForHealthRemoval.value) {
    alert.show('Please enter a server IP address', 'error', 3000);
    return;
  }
  pendingAction.value = async () => {
    try {
      if (!validateToken()) return;

      isRemovingServerHealth.value = true;
      const response = await fetch(`${MASTER_URL}/database/health/ip/${serverIpForHealthRemoval.value}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userState.user.token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        handleUnauthorized(response);
        throw new Error(data.error || 'Failed to remove health data');
      }

      alert.show(`${data.message} (${data.rowsAffected} records)`, 'success', 3000);
      selectedAction.value = null;
      serverIpForHealthRemoval.value = '';
    } catch (error) {
      console.error('Error in handleRemoveHealthByServer:', error);
      alert.show(error.message || 'Failed to remove health data', 'error', 5000);
    } finally {
      isRemovingServerHealth.value = false;
    }
  };
  confirmationDialog.value.toggleDialog();
};

// Add these refs for editing functionality
const editingUser = ref(null);
const editedData = ref({});

const roles = [
  'Admin',
  'Database Admin',
  'User',
  'Owner',
  'Marketing'
];

// Mock users data (later you can replace this with actual data from your API)
const users = ref([
  { id: 1, username: 'admin', email: '<EMAIL>', createdAt: '2025-04-01 12:00:00', role: 'Admin', isAdmin: true },
  { id: 2, username: 'john.doe', email: '<EMAIL>', createdAt: '2025-04-01 14:30:00', role: 'User', isAdmin: false },
  { id: 3, username: 'sarah.smith', email: '<EMAIL>', createdAt: '2025-04-02 09:15:00', role: 'Marketing', isAdmin: false },
  { id: 4, username: 'mike.wilson', email: '<EMAIL>', createdAt: '2025-04-02 11:45:00', role: 'Database Admin', isAdmin: false }
]);

const startEditing = (user) => {
  console.log('Starting edit for user:', user); // Debug log
  editingUser.value = user;
  editedData.value = {
    username: user.username,
    email: user.email,
    role: user.role
  };
};

const cancelEditing = () => {
  console.log('Canceling edit'); // Debug log
  editingUser.value = null;
  editedData.value = {};
};

const saveUserChanges = async (user) => {
  try {
    console.log('Saving changes for user:', editedData.value); // Debug log
    // Here you would typically make an API call to update the user
    const userIndex = users.value.findIndex(u => u.id === user.id);
    if (userIndex !== -1) {
      users.value[userIndex] = {
        ...users.value[userIndex],
        username: editedData.value.username,
        email: editedData.value.email,
        role: editedData.value.role
      };
    }
    alert?.show('User updated successfully!', 'success', 3000);
    editingUser.value = null;
    editedData.value = {};
  } catch (error) {
    console.error('Error saving user:', error); // Debug log
    alert?.show('Failed to update user', 'error', 3000);
  }
};

// Add these to your existing refs
const showPassword = ref(false);
const newUser = ref({
  username: '',
  email: '',
  password: '',
  role: '',
  sendEmail: true
});

// Add this computed property
const isFormValid = computed(() => {
  return newUser.value.username &&
    newUser.value.email &&
    newUser.value.password &&
    newUser.value.role;
});

// Add this method
const handleAddUser = () => {
  // Here you would typically make an API call to create the user
  console.log('Adding new user:', newUser.value);
  alert?.show('User added successfully!', 'success', 3000);
  selectedAction.value = null;
  // Reset form
  newUser.value = {
    username: '',
    email: '',
    password: '',
    role: '',
    sendEmail: true
  };
};

// Add refs for confirmation dialog
const confirmationDialog = ref(null);
const pendingAction = ref(null);

// Add handler for confirmation
const handleConfirmation = async () => {
  if (pendingAction.value) {
    await pendingAction.value();
    pendingAction.value = null;
  }
};

// Add this validation function
const isValidIPv4 = (ip) => {
  const parts = ip.split('.');
  if (parts.length !== 4) return false;

  return parts.every(part => {
    const num = parseInt(part, 10);
    return !isNaN(num) && num >= 0 && num <= 255 && part === num.toString();
  });
};
</script>

<template>
  <div class="database-container">
    <!-- Left Sidebar -->
    <div class="sidebar">
      <div class="sidebar-header">
        <h2>Database Management</h2>
      </div>
      <div class="menu-items">
        <button v-for="item in menuItems" :key="item.id" :class="['menu-item', { active: activeMenu === item.id }]"
          @click="activeMenu = item.id">
          <i :class="item.icon"></i>
          <span>{{ item.label }}</span>
        </button>
      </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
      <div class="content-header">
        <h1>{{ getContent(activeMenu).title }}</h1>
        <p>{{ getContent(activeMenu).description }}</p>
      </div>

      <!-- Quick Metrics -->
      <div class="metrics-grid">
        <div v-for="metric in getContent(activeMenu).metrics" :key="metric.label" class="metric-card">
          <h3>{{ metric.label }}</h3>
          <div class="metric-value">{{ metric.value }}</div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="actions-container">
        <button v-for="action in getContent(activeMenu).actions" :key="action"
          :class="['action-button', { active: selectedAction === action }]" @click="handleActionClick(action)">
          <i :class="getActionIcon(action)"></i>
          <span>{{ action }}</span>
        </button>
      </div>

      <!-- Main Content -->
      <div class="content-body">
        <!-- Remove All Instances Section -->
        <div v-if="selectedAction === 'Remove All Instances'" class="action-section">
          <div class="action-header">
            <i class="pi pi-exclamation-triangle warning-icon"></i>
            <h2>Confirm Remove Operation</h2>
          </div>

          <div class="action-content">
            <div class="warning-points">
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>All instances will be permanently deleted</span>
              </div>
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>This action cannot be undone</span>
              </div>
            </div>
          </div>

          <div class="action-footer">
            <button class="cancel-btn" @click="selectedAction = null" :disabled="isPurging">
              Cancel
            </button>
            <button class="confirm-btn danger" @click="handleRemoveAction" :disabled="isPurging">
              <i class="pi pi-spinner pi-spin" v-if="isPurging"></i>
              <span>{{ isPurging ? 'Purging...' : 'Confirm Remove' }}</span>
            </button>
          </div>
        </div>

        <!-- Set Global Privacy Section -->
        <div v-else-if="selectedAction === 'Set Global Privacy'" class="action-section">
          <div class="action-header">
            <i class="pi pi-lock warning-icon"></i>
            <h2>Confirm Privacy Settings Update</h2>
          </div>

          <div class="action-content">
            <div class="warning-points">
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>All instances will be set to private mode</span>
              </div>
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>Public access will be revoked for all instances</span>
              </div>
            </div>
          </div>

          <div class="action-footer">
            <button class="cancel-btn" @click="selectedAction = null" :disabled="isSettingPrivacy">
              Cancel
            </button>
            <button class="confirm-btn" @click="handleSetPrivacyAction" :disabled="isSettingPrivacy">
              <i class="pi pi-spinner pi-spin" v-if="isSettingPrivacy"></i>
              <span>{{ isSettingPrivacy ? 'Updating...' : 'Confirm Update' }}</span>
            </button>
          </div>
        </div>

        <!-- Remove By Server Section -->
        <div v-else-if="selectedAction === 'Remove By Server'" class="action-section">
          <div class="action-header">
            <i class="pi pi-server warning-icon"></i>
            <h2>Remove Instances by Server</h2>
          </div>

          <div class="action-content">
            <p>Enter the server IP address to remove all associated instances:</p>
            <div class="input-container">
              <input type="text" v-model="serverIpToRemove" placeholder="e.g., *************" :disabled="isRemoving"
                class="server-ip-input" />
            </div>
            <div class="warning-points">
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>All instances from this server will be permanently deleted</span>
              </div>
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>This action cannot be undone</span>
              </div>
            </div>
          </div>

          <div class="action-footer">
            <button class="cancel-btn" @click="selectedAction = null" :disabled="isRemoving">
              Cancel
            </button>
            <button class="confirm-btn danger" @click="handleRemoveByServerAction" :disabled="isRemoving">
              <i class="pi pi-spinner pi-spin" v-if="isRemoving"></i>
              <span>{{ isRemoving ? 'Removing...' : 'Remove Instances' }}</span>
            </button>
          </div>
        </div>

        <!-- Server List Section -->
        <div v-else-if="selectedAction === 'View Server List'" class="action-section">
          <div class="action-header">
            <i class="pi pi-server"></i>
            <h2>Server List</h2>
          </div>

          <div class="action-content">
            <div v-if="isLoadingServers" class="loading-state">
              <i class="pi pi-spinner pi-spin"></i>
              <span>Loading servers...</span>
            </div>

            <div v-else-if="servers.length" class="servers-grid">
              <div v-for="server in servers" :key="server.id" class="server-card">
                <div class="server-header">
                  <i :class="server.server_type === 'master' ? 'pi pi-database' : 'pi pi-server'"></i>
                  <span class="server-type-badge" :class="server.server_type">
                    {{ server.server_type }}
                  </span>
                </div>

                <h3>{{ server.server_name }}</h3>
                <p class="server-ip">{{ server.server_ip }}</p>

                <div class="server-stats">
                  <div class="stat-item">
                    <span class="label">Instances:</span>
                    <span class="value">{{ server.stats.instances }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="label">Health Records:</span>
                    <span class="value">{{ server.stats.health_records }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="empty-state">
              <i class="pi pi-info-circle"></i>
              <span>No servers found</span>
            </div>
          </div>

          <div class="action-footer">
            <button class="cancel-btn" @click="selectedAction = null">
              Close
            </button>
          </div>
        </div>

        <!-- Remove Server Section -->
        <div v-else-if="selectedAction === 'Remove Server'" class="action-section">
          <div class="action-header">
            <i class="pi pi-times-circle warning-icon"></i>
            <h2>Remove Server</h2>
          </div>

          <div class="action-content">
            <p>Enter the server IP address to remove:</p>
            <div class="input-container">
              <input type="text" v-model="serverToRemove" placeholder="e.g., *************" :disabled="isRemovingServer"
                class="server-ip-input" />
            </div>
            <div class="warning-points">
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>All instances and health records for this server will be permanently deleted</span>
              </div>
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>This action cannot be undone</span>
              </div>
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>Master servers cannot be removed</span>
              </div>
            </div>
          </div>

          <div class="action-footer">
            <button class="cancel-btn" @click="selectedAction = null" :disabled="isRemovingServer">
              Cancel
            </button>
            <button class="confirm-btn danger" @click="handleRemoveServerAction" :disabled="isRemovingServer">
              <i class="pi pi-spinner pi-spin" v-if="isRemovingServer"></i>
              <span>{{ isRemovingServer ? 'Removing...' : 'Remove Server' }}</span>
            </button>
          </div>
        </div>

        <!-- Register New Server Section -->
        <div v-else-if="selectedAction === 'Register New Server'" class="action-section">
          <div class="action-header">
            <i class="pi pi-plus-circle warning-icon"></i>
            <h2>Register New Slave Server</h2>
          </div>

          <div class="action-content">
            <p>Enter the server details to register:</p>
            <div class="input-container">
              <input type="text" v-model="newServer.server_ip" placeholder="e.g., *************"
                :disabled="isRegisteringServer" class="server-ip-input" />
            </div>
            <div class="input-container">
              <input type="text" v-model="newServer.server_name" placeholder="e.g., Production Slave 1"
                :disabled="isRegisteringServer" class="server-ip-input" />
            </div>
            <div class="warning-points">
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>Ensure the server is running and accessible</span>
              </div>
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>Server must have slave service installed</span>
              </div>
            </div>
          </div>

          <div class="action-footer">
            <button class="cancel-btn" @click="selectedAction = null" :disabled="isRegisteringServer">
              Cancel
            </button>
            <button class="confirm-btn" @click="handleRegisterServerAction" :disabled="isRegisteringServer">
              <i class="pi pi-spinner pi-spin" v-if="isRegisteringServer"></i>
              <span>{{ isRegisteringServer ? 'Registering...' : 'Register Server' }}</span>
            </button>
          </div>
        </div>

        <!-- Remove All Health Data Section -->
        <div v-else-if="selectedAction === 'Remove All Health Data'" class="action-section">
          <div class="action-header">
            <i class="pi pi-exclamation-triangle warning-icon"></i>
            <h2>Confirm Remove All Health Data</h2>
          </div>

          <div class="action-content">
            <div class="warning-points">
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>All server health records will be permanently deleted</span>
              </div>
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>Historical performance data will be lost</span>
              </div>
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>This action cannot be undone</span>
              </div>
            </div>
          </div>

          <div class="action-footer">
            <button class="cancel-btn" @click="selectedAction = null" :disabled="isRemovingHealthData">
              Cancel
            </button>
            <button class="confirm-btn danger" @click="handleRemoveAllHealthData" :disabled="isRemovingHealthData">
              <i class="pi pi-spinner pi-spin" v-if="isRemovingHealthData"></i>
              <span>{{ isRemovingHealthData ? 'Removing...' : 'Remove All Health Data' }}</span>
            </button>
          </div>
        </div>

        <!-- Remove Health By Server Section -->
        <div v-else-if="selectedAction === 'Remove Health By Server'" class="action-section">
          <div class="action-header">
            <i class="pi pi-exclamation-triangle warning-icon"></i>
            <h2>Remove Server Health Data</h2>
          </div>

          <div class="action-content">
            <p>Enter the server IP address to remove health data:</p>
            <div class="input-container">
              <input type="text" v-model="serverIpForHealthRemoval" placeholder="e.g., *************"
                :disabled="isRemovingServerHealth" class="server-ip-input" />
            </div>
            <div class="warning-points">
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>All health records for this server will be permanently deleted</span>
              </div>
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>Historical performance data for this server will be lost</span>
              </div>
              <div class="warning-point">
                <i class="pi pi-exclamation-triangle"></i>
                <span>This action cannot be undone</span>
              </div>
            </div>
          </div>

          <div class="action-footer">
            <button class="cancel-btn" @click="selectedAction = null" :disabled="isRemovingServerHealth">
              Cancel
            </button>
            <button class="confirm-btn danger" @click="handleRemoveHealthByServer" :disabled="isRemovingServerHealth">
              <i class="pi pi-spinner pi-spin" v-if="isRemovingServerHealth"></i>
              <span>{{ isRemovingServerHealth ? 'Removing...' : 'Remove Health Data' }}</span>
            </button>
          </div>
        </div>

        <!-- View Table Schema Section -->
        <div v-else-if="selectedAction === 'View Table Schema'" class="action-section">
          <div class="action-header">
            <i class="pi pi-table schema-icon"></i>
            <h2>Health Data Table Schema</h2>
          </div>

          <div class="action-content schema-content">
            <div class="schema-table">
              <div class="schema-header">
                <div class="column">Column Name</div>
                <div class="type">Data Type</div>
                <div class="description">Description</div>
              </div>

              <div class="schema-row">
                <div class="column">server_ip</div>
                <div class="type">VARCHAR(15)</div>
                <div class="description">IP address of the monitored server</div>
              </div>

              <div class="schema-row">
                <div class="column">timestamp</div>
                <div class="type">TIMESTAMP</div>
                <div class="description">Time when health data was recorded</div>
              </div>

              <div class="schema-row">
                <div class="column">cpu_usage</div>
                <div class="type">FLOAT</div>
                <div class="description">CPU utilization percentage</div>
              </div>

              <div class="schema-row">
                <div class="column">memory_usage</div>
                <div class="type">FLOAT</div>
                <div class="description">Memory usage in percentage</div>
              </div>

              <div class="schema-row">
                <div class="column">disk_usage</div>
                <div class="type">FLOAT</div>
                <div class="description">Disk space utilization percentage</div>
              </div>

              <div class="schema-row">
                <div class="column">network_in</div>
                <div class="type">BIGINT</div>
                <div class="description">Incoming network traffic (bytes/sec)</div>
              </div>

              <div class="schema-row">
                <div class="column">network_out</div>
                <div class="type">BIGINT</div>
                <div class="description">Outgoing network traffic (bytes/sec)</div>
              </div>

              <div class="schema-row">
                <div class="column">active_connections</div>
                <div class="type">INTEGER</div>
                <div class="description">Number of active database connections</div>
              </div>

              <div class="schema-row">
                <div class="column">server_status</div>
                <div class="type">VARCHAR(20)</div>
                <div class="description">Current server health status</div>
              </div>
            </div>
          </div>

          <div class="action-footer">
            <button class="confirm-btn" @click="selectedAction = null">
              Close
            </button>
          </div>
        </div>

        <!-- View All Users Section -->
        <div v-else-if="selectedAction === 'View All Users'" class="action-section">
          <div class="action-header">
            <i class="pi pi-users"></i>
            <h2>User List</h2>
          </div>

          <div class="action-content scrollable-content">
            <div class="users-table-container">
              <table class="users-table">
                <thead>
                  <tr>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Created At</th>
                    <th>Role</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="user in users" :key="user.id">
                    <td>
                      <template v-if="editingUser && editingUser.id === user.id">
                        <input type="text" v-model="editedData.username" class="edit-input">
                      </template>
                      <template v-else>
                        {{ user.username }}
                      </template>
                    </td>
                    <td>
                      <template v-if="editingUser && editingUser.id === user.id">
                        <input type="email" v-model="editedData.email" class="edit-input">
                      </template>
                      <template v-else>
                        {{ user.email }}
                      </template>
                    </td>
                    <td>{{ user.createdAt }}</td>
                    <td>
                      <template v-if="editingUser && editingUser.id === user.id">
                        <select v-model="editedData.role" class="role-select">
                          <option v-for="role in roles" :key="role" :value="role">
                            {{ role }}
                          </option>
                        </select>
                      </template>
                      <template v-else>
                        <span :class="['role-badge', user.role.toLowerCase().replace(' ', '-')]">
                          {{ user.role }}
                        </span>
                      </template>
                    </td>
                    <td>
                      <template v-if="editingUser && editingUser.id === user.id">
                        <button class="table-action-btn confirm" @click="saveUserChanges(user)" title="Save changes">
                          <i class="pi pi-check"></i>
                        </button>
                        <button class="table-action-btn cancel" @click="cancelEditing" title="Cancel changes">
                          <i class="pi pi-times"></i>
                        </button>
                      </template>
                      <template v-else>
                        <button class="table-action-btn" @click="startEditing(user)" :disabled="user.isAdmin"
                          :title="user.isAdmin ? 'Admin cannot be modified' : 'Edit user'">
                          <i class="pi pi-pencil"></i>
                        </button>
                        <button class="table-action-btn delete" :disabled="user.isAdmin"
                          :title="user.isAdmin ? 'Admin cannot be deleted' : 'Delete user'">
                          <i class="pi pi-trash"></i>
                        </button>
                      </template>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class="action-footer">
            <button class="confirm-btn" @click="selectedAction = null">
              <i class="pi pi-times"></i>
              <span>Close</span>
            </button>
          </div>
        </div>

        <!-- Add New User Section -->
        <div v-if="selectedAction === 'Add New User'" class="action-section">
          <div class="action-header">
            <i class="pi pi-user-plus"></i>
            <h2>Add New User</h2>
          </div>

          <div class="action-content">
            <div class="content-body">
              <div class="content-section">
                <div class="section-header">
                  <i class="pi pi-info-circle"></i>
                  <span>User Information</span>
                </div>

                <div class="form-group">
                  <label for="username">Username</label>
                  <input type="text" id="username" v-model="newUser.username" class="form-control"
                    placeholder="Enter username">
                </div>

                <div class="form-group">
                  <label for="email">Email</label>
                  <input type="email" id="email" v-model="newUser.email" class="form-control"
                    placeholder="Enter email address">
                </div>

                <div class="form-group">
                  <label for="password">Password</label>
                  <div class="input-container">
                    <input :type="showPassword ? 'text' : 'password'" id="password" v-model="newUser.password"
                      class="form-control" placeholder="Enter password">
                    <button type="button" class="icon-button" @click="showPassword = !showPassword">
                      <i :class="showPassword ? 'pi pi-eye-slash' : 'pi pi-eye'"></i>
                    </button>
                  </div>
                </div>

                <div class="form-group">
                  <label for="role">Role</label>
                  <select id="role" v-model="newUser.role" class="form-control">
                    <option value="">Select a role</option>
                    <option value="admin">Admin</option>
                    <option value="user">Database Admin</option>
                    <option value="viewer">User</option>
                    <option value="user">Owner</option>
                    <option value="viewer">Marketing</option>
                  </select>
                </div>
              </div>

              <div class="content-section">
                <div class="section-header">
                  <i class="pi pi-cog"></i>
                  <span>Additional Settings</span>
                </div>

                <div class="form-group">
                  <label class="checkbox-container">
                    <input type="checkbox" v-model="newUser.sendEmail">
                    <span class="checkbox-text">Send welcome email to user</span>
                  </label>
                  <span class="help-text">User will receive login credentials via email</span>
                </div>
              </div>
            </div>
          </div>

          <div class="action-footer">
            <button class="cancel-btn" @click="selectedAction = null">
              <i class="pi pi-times"></i>
              <span>Cancel</span>
            </button>
            <button class="confirm-btn" @click="handleAddUser" :disabled="!isFormValid">
              <i class="pi pi-check"></i>
              <span>Add User</span>
            </button>
          </div>
        </div>

        <!-- Default content when no action is selected -->
        <!-- <div v-else class="placeholder-message">
            <i class="pi pi-database"></i>
            <span>Select an action above to manage {{ getContent(activeMenu).title.toLowerCase() }}</span>
          </div> -->
      </div>
    </div>
  </div>
  <ConfirmationDialog ref="confirmationDialog" @confirmed="handleConfirmation" />
</template>

<style scoped>
.database-container {
  display: flex;
  height: calc(100vh - 60px);
  background: var(--bg-primary);
}

.sidebar {
  width: 240px;
  /* Reduced from 280px */
  background: white;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 1.25rem;
  /* Reduced from 1.5rem */
  border-bottom: 1px solid var(--border-color);
}

.sidebar-header h2 {
  color: var(--primary-dark);
  font-size: 1.125rem;
  /* Reduced from 1.25rem */
  font-weight: 600;
}

.menu-items {
  padding: 0.75rem 0;
  /* Reduced from 1rem */
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 0.625rem 1.25rem;
  /* Reduced from 0.75rem 1.5rem */
  width: 100%;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.menu-item i {
  margin-right: 0.625rem;
  /* Reduced from 0.75rem */
  font-size: 1rem;
  /* Reduced from 1.1rem */
}

.menu-item span {
  font-size: 0.875rem;
  /* Reduced from 1rem */
  font-weight: 500;
  /* Added for better readability */
}

.menu-item:hover {
  background: var(--bg-hover);
  color: var(--primary-light);
}

.menu-item.active {
  background: var(--primary-light);
  color: white;
}

.content-area {
  flex: 1;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  height: 100%;
  /* Add this */
  overflow: hidden;
  /* Add this */
}

.content-header {
  margin-bottom: 0;
}

.content-header h1 {
  color: var(--primary-dark);
  font-size: 1.5rem;
  font-weight: 700;
  /* margin-bottom: 0.5rem; */
}

.content-header p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  max-width: 600px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1rem;
  flex-shrink: 0;
  /* Add this */
}

.metric-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: 0 4px 8px #2117611a;
  transition: transform 0.2s, box-shadow 0.2s;
}

/* .metric-card:hover {
                                            transform: translateY(-2px);
                                              box-shadow: 0 4px 8px rgba(33, 23, 97, 0.1);
                                            */
.metric-card h3 {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.metric-value {
  color: var(--primary-dark);
  font-size: 1.5rem;
  font-weight: 600;
}

.actions-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  padding: 0rem;
  /* background: white; */
  /* border-radius: 0.75rem; */
  /* box-shadow: 0 2px 4px rgba(33, 23, 97, 0.06); */
  flex-shrink: 0;
  /* Add this */
}

.action-button {
  padding: 0.425rem 0.8rem;
  border-radius: 2rem;
  border: 1px solid var(--border-color);
  background: white;
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-button:hover {
  background: var(--primary-light);
  color: white;
  border-color: var(--primary-light);
}

.action-button.active {
  background: var(--primary-dark);
  color: white;
  border-color: var(--primary-dark);
}

.content-body {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 6px 20px rgba(82, 55, 204, 0.12);
  /* Updated to match metric card hover shadow */
  flex: 1;
  overflow-x: auto;
  min-height: 0;
  scroll-behavior: smooth;
}

.placeholder-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-align: center;
  gap: 1rem;
}

.placeholder-message i {
  font-size: 2rem;
  color: var(--text-secondary);
  opacity: 0.5;
}

.action-section {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  /* Add this */
}

.action-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  /* padding-bottom: 1rem; */
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
  /* Add this */
}

.action-header h2 {
  margin: 0;
  color: var(--primary-dark);
  font-size: 1rem;
  font-weight: 600;
}

.action-content {
  flex: 1;
  /* padding: 1.5rem 0; */
  overflow-y: auto;
  min-height: 0;
  /* Add this */
}

.action-footer {
  display: flex;
  justify-content: flex-end;
  /* gap: 0.75rem; */
  /* padding-top: 1rem; */
  border-top: 1px solid var(--border-color);
  flex-shrink: 0;
  /* Add this */
  background: white;
  /* Add this */
  position: sticky;
  /* Add this */
  bottom: 0;
  /* Add this */
  margin-top: auto;
  /* Add this */
}

.confirm-btn {
  padding: 0.375rem 0.75rem;
  /* Reduced from 0.5rem 1rem */
  border-radius: 0.375rem;
  border: none;
  background: var(--primary-light);
  color: white;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  /* Reduced from 0.5rem */
  font-size: 0.813rem;
  /* Added smaller font size */
}

.confirm-btn.danger {
  background: #ff4444;
}

.confirm-btn:hover:not(:disabled) {
  opacity: 0.9;
}

.cancel-btn {
  padding: 0.375rem 0.75rem;
  /* Reduced from 0.5rem 1rem */
  border-radius: 0.375rem;
  border: 1px solid var(--border-color);
  background: white;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  font-size: 0.813rem;
  /* Added smaller font size */
}

.cancel-btn:hover:not(:disabled) {
  border-color: var(--primary-light);
  color: var(--primary-light);
}

.warning-points {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.warning-point {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.warning-point i {
  color: #ff4444;
}

.input-container {
  margin: 1rem 0;
}

.server-ip-input {
  width: 100%;
  padding: 0.425rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.server-ip-input:focus {
  outline: none;
  border-color: var(--primary-light);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content-area {
    padding: 1rem;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .actions-container {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .content-area {
    padding: 0.75rem;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .action-button {
    width: 100%;
    justify-content: center;
  }
}

/* Update responsive styles for smaller screens */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .menu-item {
    padding: 0.5rem 1rem;
  }

  .sidebar-header {
    padding: 1rem;
  }

  .sidebar-header h2 {
    font-size: 1rem;
  }
}

/* Optional: Add smooth scrolling to content */
.action-content {
  /* scroll-behavior: smooth; */
  padding-right: 1rem;
  /* Add space for scrollbar */
}

/* Optional: Style the scrollbar */
.action-content::-webkit-scrollbar {
  width: 8px;
}

.action-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.action-content::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.action-content::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.content-area {
  flex: 1;
  /* padding: 1.5rem; */
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Keep metrics and actions fixed */
.metrics-grid {
  flex-shrink: 0;
}

.actions-container {
  flex-shrink: 0;
}

/* Make content body scrollable */
.content-body {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 4px 8px rgba(33, 23, 97, 0.1);
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  /* Important for Firefox */
  scroll-behavior: smooth;
}

/* Action section styles */
.action-section {
  display: flex;
  flex-direction: column;
  min-height: 100px;
}

.action-header {
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.action-content {
  padding: 1rem 0;
}

.action-footer {
  padding-top: 0.75rem;
  /* Reduced from 1rem */
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  /* Reduced from 0.75rem */
}

/* Scrollbar styling */
.content-body::-webkit-scrollbar {
  width: 8px;
}

.content-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.content-body::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.content-body::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Keep your existing responsive styles */
@media (max-width: 768px) {
  .content-area {
    padding: 1rem;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 480px) {
  .content-area {
    padding: 0.75rem;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }
}

.servers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  padding: 0.5rem;
}

.server-card {
  background: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
}

.server-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.server-header i {
  font-size: 1.25rem;
  color: var(--primary-dark);
}

.server-type-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  text-transform: capitalize;
}

.server-type-badge.master {
  background: #e1f5fe;
  color: #0288d1;
}

.server-type-badge.slave {
  background: #f3e5f5;
  color: #7b1fa2;
}

.server-card h3 {
  font-size: 1rem;
  margin: 0 0 0.1rem 0;
  color: var(--text-primary);
}

.server-ip {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
}

.server-stats {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.813rem;
}

.stat-item .label {
  color: var(--text-secondary);
}

.stat-item .value {
  font-weight: 500;
  color: var(--text-primary);
}

.loading-state,
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.loading-state i,
.empty-state i {
  font-size: 1.25rem;
}

/* Schema Table Styles */
.schema-content {
  padding: 1rem 0;
}

.schema-table {
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  overflow: hidden;
}

.schema-header {
  display: grid;
  grid-template-columns: 180px 120px 1fr;
  background: var(--primary-light);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1rem;
}

.schema-row {
  display: grid;
  grid-template-columns: 180px 120px 1fr;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  background: white;
}

.schema-row:last-child {
  border-bottom: none;
}

.schema-row:hover {
  background: var(--hover-bg);
}

.schema-icon {
  color: var(--primary-dark);
  font-size: 1.25rem;
}

.column {
  font-family: monospace;
  color: var(--primary-dark);
}

.type {
  font-family: monospace;
  color: var(--text-secondary);
}

.description {
  color: var(--text-secondary);
}

.schema-row .column {
  font-weight: 500;
}

/* Schema Styles */
.schema-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.5rem 0;
}

.schema-item {
  background: var(--hover-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  padding: 0.75rem;
}

.schema-item-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.375rem;
}

.column-name {
  font-family: monospace;
  font-weight: 600;
  color: white;
  font-size: 0.875rem;
}

.data-type {
  font-family: monospace;
  color: var(--text-secondary);
  font-size: 0.813rem;
  padding: 0.25rem 0.5rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 0.25rem;
}

.schema-item-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .schema-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Users Table Styles */
.users-table-container {
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  overflow: hidden;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.users-table th,
.users-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.users-table th {
  background: var(--bg-primary);
  font-weight: 600;
  color: var(--primary-dark);
  font-size: 0.875rem;
  white-space: nowrap;
}

.users-table td {
  font-size: 0.875rem;
  color: var(--text-primary);
}

.users-table tbody tr:hover {
  background: var(--bg-hover);
}

.role-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.role-badge.admin {
  background: #e8eaf6;
  color: #3f51b5;
}

.role-badge.database-admin {
  background: #fff3e0;
  color: #ff9800;
}

.role-badge.user {
  background: #e8f5e9;
  color: #4caf50;
}

.role-badge.owner {
  background: #e1f5fe;
  color: #03a9f4;
}

.role-badge.marketing {
  background: #f3e5f5;
  color: #9c27b0;
}

.table-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  margin: 0 0.25rem;
  border-radius: 4px;
  transition: all 0.2s;
}

.table-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.table-action-btn.confirm {
  color: #4caf50;
}

.table-action-btn.confirm:hover {
  background: #e8f5e9;
}

.table-action-btn.cancel {
  color: #f44336;
}

.table-action-btn.cancel:hover {
  background: #ffebee;
}

.table-action-btn.delete {
  color: #f44336;
}

.table-action-btn.delete:hover {
  background: #ffebee;
}

/* Ensure the table header stays fixed while scrolling */
.users-table thead {
  position: sticky;
  top: 0;
  z-index: 1;
  background: var(--bg-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .users-table {
    display: block;
    overflow-x: auto;
  }

  .users-table th,
  .users-table td {
    padding: 0.5rem 0.75rem;
  }
}

.content-section {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(82, 55, 204, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.25rem;
  color: var(--text-primary);
  font-weight: 500;
}

.section-header i {
  color: var(--primary-light);
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.875rem;
}

.form-control {
  width: 100%;
  padding: 0.625rem 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-light);
}

.input-container {
  position: relative;
}

.icon-button {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.checkbox-text {
  font-size: 0.875rem;
  color: var(--text-primary);
}

.help-text {
  display: block;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
  margin-left: 1.75rem;
}

/* Add these new styles */
.edit-input {
  width: 100%;
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.role-select {
  width: 100%;
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.table-action-btn.confirm {
  color: #4caf50;
}

.table-action-btn.confirm:hover {
  background: #e8f5e9;
}

.table-action-btn.cancel {
  color: #f44336;
}

.table-action-btn.cancel:hover {
  background: #ffebee;
}

.add-user-form {
  max-width: 600px;
  margin: 1rem auto;
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: 0.625rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: all 0.2s;
  background: white;
}

.form-input:focus {
  border-color: var(--primary-light);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

.password-input-container {
  position: relative;
}

.toggle-password-btn {
  position: absolute;
  right: 0.625rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
}

.toggle-password-btn:hover {
  color: var(--text-primary);
}

.checkbox-group {
  margin-top: 1.5rem;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox-label {
  font-size: 0.875rem;
  color: var(--text-primary);
}

.action-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem;
  border-top: 1px solid var(--border-color);
}

.cancel-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: 1px solid var(--border-color);
  background: white;
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.cancel-btn:hover {
  background: var(--bg-secondary);
}

.confirm-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: none;
  background: var(--primary-light);
  color: white;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.confirm-btn:hover {
  background: var(--primary-dark);
}

.confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>


