const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// Ensure database directory exists
const dbDir = path.resolve(__dirname, '..', 'database');
if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
}

// Set default database path
const dbPath = process.env.DATABASE_PATH || path.join(dbDir, 'database.sqlite');

// Create database connection
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error("❌ Error connecting to the database:", err.message);
        process.exit(1);
    }
    console.log("📡 Connected to the SQLite database");
    initializeTables();
});

// Initialize all tables
function initializeTables() {
    db.serialize(() => {
        // Create users table
        db.run(createUsersTable, logTableCreation('users'));

        // Create servers table
        db.run(createServersTable, logTableCreation('servers'));

        // Create tags table
        db.run(createTagsTable, logTableCreation('tags'));

        // Create applications table
        db.run(createApplicationsTable, logTableCreation('applications'));

        // Create application_tags junction table
        db.run(createApplicationTagsTable, logTableCreation('application_tags'));

        // Create Aras-related tables
        db.run(createArasDatabasesTable, logTableCreation('aras_databases'));
        db.run(createArasVersionsTable, logTableCreation('aras_versions'));
        db.run(createArasCredentialsTable, logTableCreation('aras_credentials'));

        // Create servers health table
        db.run(createHealthDataTable, logTableCreation('servers_health'));

        // Create server services table
        db.run(createServerServicesTable, logTableCreation('server_services'));

        // Create indexes after tables are created
        createIndexes();
    });
}

// Helper function for logging table creation
function logTableCreation(tableName) {
    return (err) => {
        if (err) {
            console.error(`❌ Error creating ${tableName} table:`, err.message);
        } else {
            console.log(`✅ ${tableName} table is ready`);
        }
    };
}

// Create indexes for better performance
function createIndexes() {
    const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_applications_server_id ON applications(server_id)',
        'CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status)',
        'CREATE INDEX IF NOT EXISTS idx_servers_ip ON servers(ip)',
        'CREATE INDEX IF NOT EXISTS idx_aras_databases_application_id ON aras_databases(application_id)',
        'CREATE INDEX IF NOT EXISTS idx_aras_credentials_application_id ON aras_credentials(application_id)',
        'CREATE INDEX IF NOT EXISTS idx_aras_credentials_database_id ON aras_credentials(database_id)',
        'CREATE INDEX IF NOT EXISTS idx_servers_health_server_id ON servers_health(server_id)',
        'CREATE INDEX IF NOT EXISTS idx_server_services_server_id ON server_services(server_id)'
    ];

    indexes.forEach(index => {
        db.run(index, (err) => {
            if (err) {
                console.error('❌ Error creating index:', err.message);
            }
        });
    });
}

// SQL table definitions
const createUsersTable = `
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT NOT NULL UNIQUE,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
)`;

const createServersTable = `
CREATE TABLE IF NOT EXISTS servers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ip TEXT NOT NULL UNIQUE,
    server_name TEXT,
    maintainer TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
)`;

const createTagsTable = `
CREATE TABLE IF NOT EXISTS tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL
)`;

const createApplicationsTable = `
CREATE TABLE IF NOT EXISTS applications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    path TEXT,
    server_id INTEGER,
    owner TEXT,
    purpose TEXT,
    status TEXT,
    expected_deletion_date DATE,
    external_url TEXT,
    public BOOLEAN DEFAULT 0,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE
)`;

const createApplicationTagsTable = `
CREATE TABLE IF NOT EXISTS application_tags (
    application_id INTEGER,
    tag_id INTEGER,
    PRIMARY KEY (application_id, tag_id),
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
)`;

const createArasDatabasesTable = `
CREATE TABLE IF NOT EXISTS aras_databases (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    application_id INTEGER NOT NULL,
    database_name TEXT,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
)`;

const createArasVersionsTable = `
CREATE TABLE IF NOT EXISTS aras_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    application_id INTEGER NOT NULL UNIQUE,
    aras_version TEXT,
    build_number TEXT,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
)`;

const createArasCredentialsTable = `
CREATE TABLE IF NOT EXISTS aras_credentials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    application_id INTEGER NOT NULL,
    database_id INTEGER NOT NULL,
    username TEXT,
    password TEXT,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (database_id) REFERENCES aras_databases(id) ON DELETE CASCADE
)`;

const createHealthDataTable = `
CREATE TABLE IF NOT EXISTS servers_health (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER NOT NULL,
    date DATE NOT NULL,
    time TIME NOT NULL,
    cpu_usage REAL,
    memory_used INTEGER,
    memory_total INTEGER,
    disk_used INTEGER,
    disk_total INTEGER,
    read_speed REAL,
    write_speed REAL,
    network_download REAL,
    network_upload REAL,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE
)`;

const createServerServicesTable = `
CREATE TABLE IF NOT EXISTS server_services (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER NOT NULL,
    service_name TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    UNIQUE(server_id, service_name)
)`;


module.exports = db;
