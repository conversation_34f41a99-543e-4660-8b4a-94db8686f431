const WebSocket = require('ws');
const { getHealthData } = require('./healthService');
const REAL_TIME_HEALTH_INTERVAL = process.env.REAL_TIME_HEALTH_INTERVAL;
class WebSocketServer {
    constructor(port) {
        this.port = port;
        this.healthUpdateInterval = null;
        this.initialize();
    }
    initialize() {
        this.wss = new WebSocket.Server({ port: this.port });
        this.wss.on('connection', (ws, req) => {
            console.log('Master server connected');
            this.startHealthUpdates(ws);
            ws.on('close', () => {
                console.log('Master server disconnected');
                this.stopHealthUpdates();
            });
            ws.on('error', (error) => {
                console.error('WebSocket error:', error);
                this.stopHealthUpdates();
            });
            ws.on('message', async (message) => {
                try {
                    const data = JSON.parse(message);
                    if (data.type === 'get_health_data') {
                        const healthData = await getHealthData();
                        ws.send(JSON.stringify({
                            type: 'health_update',
                            healthData
                        }));
                    }
                } catch (error) {
                    console.error('Error processing message:', error);
                }
            });
        });
    }
    startHealthUpdates(ws) {
        this.stopHealthUpdates();
        this.healthUpdateInterval = setInterval(async () => {
            if (ws.readyState === WebSocket.OPEN) {
                try {
                    const healthData = await getHealthData();
                    ws.send(JSON.stringify({
                        type: 'health_update',
                        healthData
                    }));
                } catch (error) {
                    console.error('Error sending health update:', error);
                }
            }
        }, REAL_TIME_HEALTH_INTERVAL);
    }
    stopHealthUpdates() {
        if (this.healthUpdateInterval) {
            clearInterval(this.healthUpdateInterval);
            this.healthUpdateInterval = null;
        }
    }
}
module.exports = WebSocketServer;
