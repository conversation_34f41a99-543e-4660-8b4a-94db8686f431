const db = require('../db');

async function getServerList(req, res) {
    db.all(
        'SELECT server_ip, server_name FROM servers ORDER BY server_type DESC', 
        (err, servers) => {
            if (err) {
                console.error("Error fetching servers:", err.message);
                return res.status(500).json({ error: "Internal server error" });
            }
            res.json({ servers });
        }
    );
}

module.exports = { getServerList };
