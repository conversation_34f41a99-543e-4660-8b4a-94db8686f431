const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

/**
 * Get the status of a Windows service
 */
async function getServiceStatus(serviceName) {
    try {
        const { stdout } = await execAsync(`powershell -Command "Get-Service '${serviceName}' | Select-Object -ExpandProperty Status"`);
        const status = stdout.trim().toLowerCase();
        return {
            service_name: serviceName,
            status,
            running: status === 'running',
            stopped: status === 'stopped'
        };
    } catch (error) {
        console.error(`Error getting status for service ${serviceName}:`, error.message);
        return {
            service_name: serviceName,
            status: 'not_found',
            running: false,
            stopped: false,
            error: `Service not found on this server`
        };
    }
}

/**
 * Execute a command on a Windows service
 */
async function executeServiceCommand(serviceName, action) {
    const validActions = ['start', 'stop', 'restart'];
    if (!validActions.includes(action)) {
        throw new Error('Invalid action. Must be start, stop, or restart');
    }
    
    let command;
    switch (action) {
        case 'start':
            command = `Start-Service -Name '${serviceName}'`;
            break;
        case 'stop':
            command = `Stop-Service -Name '${serviceName}'`;
            break;
        case 'restart':
            command = `Restart-Service -Name '${serviceName}'`;
            break;
    }
    
    try {
        await execAsync(`powershell -Command "${command}"`);
        const status = await getServiceStatus(serviceName);
        return { 
            success: true, 
            message: `${serviceName} service ${action} completed successfully`,
            status: status.status,
            running: status.running
        };
    } catch (error) {
        console.error(`Error ${action} service ${serviceName}:`, error.message);
        return {
            success: false,
            error: `Failed to ${action} service: ${error.message}`,
            service_name: serviceName
        };
    }
}

module.exports = {
    getServiceStatus,
    executeServiceCommand
};



