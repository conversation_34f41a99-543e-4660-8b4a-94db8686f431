const db = require('../db');

// Helper function to create ASCII bar
function createBar(value, maxWidth = 50) {
    const barWidth = Math.round((value / 100) * maxWidth);
    return '█'.repeat(barWidth) + '░'.repeat(maxWidth - barWidth);
}

// Helper function to format timestamp
function formatTime(date, time) {
    return `${date.split('-')[2]}/${date.split('-')[1]} ${time.slice(0, 5)}`;
}

console.log('🔄 Loading CPU usage history...');

// Get the last 10 entries for each server type
const cpuQuery = `
    WITH RankedData AS (
        SELECT 
            server_type,
            date,
            time,
            cpu_usage,
            ROW_NUMBER() OVER (PARTITION BY server_type ORDER BY date DESC, time DESC) as rn
        FROM health_data
        WHERE server_type IN ('master', 'slave')
    )
    SELECT *
    FROM RankedData
    WHERE rn <= 10
    ORDER BY server_type, date DESC, time DESC
`;

db.all(cpuQuery, [], (err, results) => {
    if (err) {
        console.error('❌ Error fetching CPU data:', err.message);
        db.close(() => process.exit(1));
        return;
    }

    // Group results by server type
    const masterData = results.filter(r => r.server_type === 'master');
    const slaveData = results.filter(r => r.server_type === 'slave');

    console.log('\n📊 CPU Usage History (Last 10 entries)');
    console.log('====================================');

    // Display master data
    console.log('\n🖥️  Master Server');
    console.log('─'.repeat(80));
    masterData.forEach(entry => {
        console.log(
            `${formatTime(entry.date, entry.time)} | ` +
            `${createBar(entry.cpu_usage)} ` +
            `${entry.cpu_usage.toFixed(1)}%`
        );
    });

    // Display slave data
    console.log('\n💻 Slave Server');
    console.log('─'.repeat(80));
    slaveData.forEach(entry => {
        console.log(
            `${formatTime(entry.date, entry.time)} | ` +
            `${createBar(entry.cpu_usage)} ` +
            `${entry.cpu_usage.toFixed(1)}%`
        );
    });

    // Calculate averages
    const masterAvg = masterData.reduce((acc, curr) => acc + curr.cpu_usage, 0) / masterData.length;
    const slaveAvg = slaveData.reduce((acc, curr) => acc + curr.cpu_usage, 0) / slaveData.length;

    console.log('\n📈 Average CPU Usage');
    console.log('─'.repeat(80));
    console.log(`Master: ${masterAvg.toFixed(1)}%`);
    console.log(`Slave:  ${slaveAvg.toFixed(1)}%`);

    db.close(() => {
        console.log('\n📡 Database connection closed');
        process.exit(0);
    });
});

