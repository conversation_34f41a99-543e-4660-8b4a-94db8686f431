const db = require('../db');

console.log('🔄 Starting migration to rename instance columns...');

db.serialize(() => {
    // 1. Create new table with renamed columns
    db.run(`
        CREATE TABLE INSTANCE_DATA_NEW (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            server_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            title TEXT,
            url TEXT NOT NULL,
            description TEXT,
            owner TEXT,
            is_public INTEGER DEFAULT 0,
            FOREIGN KEY (server_id) REFERENCES servers(id)
        )
    `, (err) => {
        if (err) {
            console.error('❌ Error creating new table:', err.message);
            process.exit(1);
        }
        console.log('✅ Created new table with renamed columns');
    });

    // 2. Copy data with renamed columns
    db.run(`
        INSERT INTO INSTANCE_DATA_NEW (
            ID, server_id, name, title,
            url, description, owner, is_public
        )
        SELECT 
            ID,
            server_id,
            instance_name,
            instance_title,
            instance_url,
            description,
            instance_owner,
            is_public
        FROM INSTANCE_DATA
    `, (err) => {
        if (err) {
            console.error('❌ Error migrating data:', err.message);
            process.exit(1);
        }
        console.log('✅ Migrated existing data');
    });

    // 3. Drop old table
    db.run(`DROP TABLE INSTANCE_DATA`, (err) => {
        if (err) {
            console.error('❌ Error dropping old table:', err.message);
            process.exit(1);
        }
        console.log('✅ Dropped old table');
    });

    // 4. Rename new table
    db.run(`ALTER TABLE INSTANCE_DATA_NEW RENAME TO INSTANCE_DATA`, (err) => {
        if (err) {
            console.error('❌ Error renaming table:', err.message);
            process.exit(1);
        }
        console.log('✅ Renamed new table');

        // 5. Recreate index
        db.run(`CREATE INDEX idx_instance_server_id ON INSTANCE_DATA(server_id)`, (err) => {
            if (err) {
                console.error('❌ Error creating index:', err.message);
                process.exit(1);
            }
            console.log('✅ Created index on server_id');
        });

        // 6. Verify migration
        db.all(`SELECT * FROM INSTANCE_DATA LIMIT 5`, (err, rows) => {
            if (err) {
                console.error('❌ Error verifying migration:', err.message);
                process.exit(1);
            }
            console.log('📝 Sample of migrated data:', rows);
            console.log('✅ Migration completed successfully');
            db.close();
        });
    });
});