const db = require('../db');

console.log('🔄 Starting migration to add email column to users table...');

db.serialize(() => {
    // Create a new table with email column
    db.run(`
        CREATE TABLE users_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            email TEXT UNIQUE,
            password TEXT NOT NULL,
            role TEXT NOT NULL,
            created_at DATETIME
        )
    `, (err) => {
        if (err) {
            console.error('❌ Error creating new table:', err.message);
            process.exit(1);
        }
        console.log('✅ Created temporary table');

        // Copy existing data
        db.run(`
            INSERT INTO users_new (id, username, password, role, created_at)
            SELECT id, username, password, role, created_at
            FROM users
        `, (err) => {
            if (err) {
                console.error('❌ Error copying data:', err.message);
                process.exit(1);
            }
            console.log('✅ Copied existing data');

            // Drop old table
            db.run(`DROP TABLE users`, (err) => {
                if (err) {
                    console.error('❌ Error dropping old table:', err.message);
                    process.exit(1);
                }
                console.log('✅ Dropped old table');

                // Rename new table
                db.run(`ALTER TABLE users_new RENAME TO users`, (err) => {
                    if (err) {
                        console.error('❌ Error renaming table:', err.message);
                        process.exit(1);
                    }
                    console.log('✅ Renamed new table');
                    db.close();
                });
            });
        });
    });
});