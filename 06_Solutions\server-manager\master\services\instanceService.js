// services/instanceService.js
const db = require('../db');

async function updateInstancesForServer(serverId, instancesData) {
    return new Promise((resolve, reject) => {
        const updatePromises = instancesData.map(instance => {
            return new Promise((resolveUpdate, rejectUpdate) => {
                const { name, url } = instance;
                db.get(
                    'SELECT * FROM INSTANCE_DATA WHERE name = ? AND server_id = ?',
                    [name, serverId],
                    (err, row) => {
                        if (err) {
                            console.error(`DB error: ${err.message}`);
                            return rejectUpdate(err);
                        }
                        if (row) {
                            db.run(
                                'UPDATE INSTANCE_DATA SET url = ? WHERE id = ?',
                                [url, row.ID],
                                (err) => {
                                    if (err) {
                                        console.error(`Error updating instance: ${err.message}`);
                                        return rejectUpdate(err);
                                    }
                                    resolveUpdate();
                                }
                            );
                        } else {
                            db.run(
                                `INSERT INTO INSTANCE_DATA (server_id, name, url)
                                VALUES (?, ?, ?)`,
                                [serverId, name, url],
                                (err) => {
                                    if (err) {
                                        console.error(`Error inserting instance: ${err.message}`);
                                        return rejectUpdate(err);
                                    }
                                    resolveUpdate();
                                }
                            );
                        }
                    }
                );
            });
        });

        Promise.all(updatePromises)
            .then(() => {
                const newInstanceNames = instancesData.map(inst => inst.name);
                db.all('SELECT * FROM INSTANCE_DATA WHERE server_id = ?', [serverId], (err, rows) => {
                    if (err) {
                        console.error(`DB error during deletion check: ${err.message}`);
                        return reject(err);
                    }
                    const deletionPromises = rows.map(row => {
                        if (!newInstanceNames.includes(row.name)) {
                            return new Promise((resolveDel, rejectDel) => {
                                db.run('DELETE FROM INSTANCE_DATA WHERE id = ?', [row.ID], (err) => {
                                    if (err) {
                                        console.error(`Error deleting instance: ${err.message}`);
                                        return rejectDel(err);
                                    }
                                    resolveDel();
                                });
                            });
                        }
                        return Promise.resolve();
                    });
                    Promise.all(deletionPromises)
                        .then(() => resolve())
                        .catch(err => reject(err));
                });
            })
            .catch(err => reject(err));
    });
}

// Helper function to get server ID from IP
async function getServerIdFromIP(serverIP) {
    return new Promise((resolve, reject) => {
        db.get('SELECT id FROM servers WHERE server_ip = ?', [serverIP], (err, row) => {
            if (err) reject(err);
            if (!row) reject(new Error(`No server found with IP ${serverIP}`));
            resolve(row.id);
        });
    });
}

module.exports = {
    updateInstancesForServer,
    getServerIdFromIP
}

// module.exports = { updateInstancesForServer };
