const IISControlService = require('../services/iisControlService');

async function getIISStatus(req, res) {
    const { server_ip } = req.query;

    if (!server_ip) {
        return res.status(400).json({ error: 'server_ip is required' });
    }

    try {
        const status = await IISControlService.getIISStatus(server_ip);
        res.json(status);
    } catch (error) {
        console.error('IIS Status Error:', error);
        res.status(500).json({ error: error.message });
    }
}

async function controlIIS(req, res) {
    const { server_ip, action } = req.body;

    if (!server_ip || !action) {
        return res.status(400).json({ error: 'server_ip and action are required' });
    }

    try {
        const result = await IISControlService.controlIIS(server_ip, action);
        res.json(result);
    } catch (error) {
        console.error('IIS Control Error:', error);
        res.status(500).json({ error: error.message });
    }
}

module.exports = { controlIIS, getIISStatus };
