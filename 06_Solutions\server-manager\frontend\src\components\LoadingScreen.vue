<script setup>
import { ref, onMounted, computed } from 'vue';
import { isInitialLoad } from '../router';
import { APP_VERSION } from '../main';

const mounted = ref(false);
const progress = ref(0);
const version = APP_VERSION;

const loadingClass = computed(() => ({
  mounted: mounted.value
}));

onMounted(() => {
  mounted.value = true;
  simulateInitialLoading();
});

const simulateInitialLoading = () => {
  if (!isInitialLoad.value) return;

  progress.value = 0;
  const duration = 1000; // Total duration in milliseconds
  const steps = 50; // Increased steps for smoother animation
  const interval = duration / steps;

  const incrementProgress = () => {
    if (progress.value < 101) {
      // Using easeOut function for smoother progression
      const remaining = 101 - progress.value;
      const increment = remaining * 0.1; // Take 10% of remaining each time
      progress.value = Math.min(progress.value + increment, 100);
    }
  };

  const progressInterval = setInterval(incrementProgress, interval);

  // Ensure we reach exactly 100% at the end
  setTimeout(() => {
    clearInterval(progressInterval);
    progress.value = 100;
  }, duration);
};
</script>

<template>
  <div v-if="isInitialLoad" class="loading-container" :class="loadingClass">
    <div class="backdrop">
      <div class="background-grid"></div>
    </div>

    <div class="loading-wrapper">
      <div class="w-[300px] h-[100px] flex justify-center items-center">
        <img src="/servertap-logo.svg" alt="Logo" class="main-logo" type="image/svg+xml" />
      </div>

      <div class="flex flex-col items-center gap-2 w-[300px]">
        <div class="w-full h-1 bg-gray-300 rounded overflow-hidden">
          <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
        </div>
        <div class="text-black text-[0.9rem]">Loading... {{ Math.round(progress) }}%</div>
      </div>
    </div>
    <div class="version-tag">{{ version }}</div>
  </div>
</template>

<style scoped>
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;

}

.loading-container.mounted {
  opacity: 1;
}

.backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  backdrop-filter: blur(8px);
}

.background-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(82, 55, 204, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(82, 55, 204, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: moveGrid 20s linear infinite;
  opacity: 0.5;
}

.loading-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  z-index: 2;
}

/* .logo-container {
  width: 300px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}

*/
.main-logo {
  width: 100%;
  height: auto;
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
  filter: grayscale(100%);
}

/* .progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 300px;
}
*/
/* .progress-bar {
  width: 100%;
  height: 4px;
  background: lightgrey;
  border-radius: 2px;
  overflow: hidden;
}

*/ .progress-fill {
  height: 100%;
  background: black;
  transition: width 0.2s ease;
}

/* .loading-text {
  color: black;
  font-size: 0.9rem;
}

*/
/* Animations */
@keyframes fadeIn {
  from {
      opacity: 0;
    }
  
    to {
      opacity: 1;
    }
  }
  
  @keyframes moveGrid {
    0% {
      transform: translate(0, 0);
    }
  
    100% {
      transform: translate(-20px, -20px);
    }
  }
  
  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .logo-container {
      width: 200px;
      height: 80px;
    }
  
    .progress-container {
      width: 200px;
    }
  }
  
  .version-tag {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    font-size: 0.8rem;
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    font-weight: 600;
    opacity: 0.8;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .version-tag {
      font-size: 0.7rem;
    }
  }
</style>
