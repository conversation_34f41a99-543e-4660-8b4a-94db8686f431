<script setup>
import { ref, provide, getCurrentInstance } from 'vue';

const app = getCurrentInstance();
const alerts = ref([]); // Array to store multiple alerts
let nextId = 0;

const show = (msg, alertType = 'info', alertDuration = 5000) => {
  const id = nextId++;
  const alert = {
    id,
    message: msg,
    type: alertType,
    isVisible: true
  };
  
  // Add new alerts to the beginning of the array
  alerts.value.unshift(alert);

  // Limit maximum number of visible alerts
  if (alerts.value.length > 10) {
    const oldestAlert = alerts.value.pop();
    if (oldestAlert) hide(oldestAlert.id);
  }

  if (alertDuration > 0) {
    setTimeout(() => {
      hide(id);
    }, alertDuration);
  }
};

const hide = (id) => {
  const index = alerts.value.findIndex(alert => alert.id === id);
  if (index !== -1) {
    alerts.value[index].isVisible = false;
    setTimeout(() => {
      alerts.value = alerts.value.filter(alert => alert.id !== id);
    }, 300);
  }
};

// Set the alert methods on the global property
if (app) {
  app.appContext.config.globalProperties.$alert = { show, hide };
}

// Provide the alert functionality to child components
provide('alert', { show, hide });
</script>

<template>
  <Teleport to="body">
    <div class="alerts-container">
      <transition-group
        name="alert"
        tag="div"
        class="alerts-wrapper"
      >
        <div 
          v-for="alert in alerts" 
          :key="alert.id" 
          v-show="alert.isVisible" 
          class="alert-overlay"
        >
          <div 
            class="alert-modal" 
            :class="[alert.type, { 'fade-out': !alert.isVisible }]"
          >
            <div class="alert-content">
              <i :class="[
                'pi',
                {
                  'pi-check-circle': alert.type === 'success',
                  'pi-times-circle': alert.type === 'error',
                  'pi-exclamation-triangle': alert.type === 'warning',
                  'pi-info-circle': alert.type === 'info'
                }
              ]"></i>
              <span class="alert-message">{{ alert.message }}</span>
            </div>
            <button class="close-button" @click="() => hide(alert.id)">
              <i class="pi pi-times"></i>
            </button>
          </div>
        </div>
      </transition-group>
    </div>
  </Teleport>
</template>

<style scoped>
.alerts-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 2000;
  pointer-events: none;
  max-height: calc(100vh - 2rem);
  max-width: 500px;
  width: 100%;
}

.alerts-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow: hidden;
  padding: 0.5rem;
}

.alert-overlay {
  pointer-events: auto;
  display: flex;
  justify-content: flex-end;
  transform-origin: top right;
}

.alert-modal {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 300px;
  max-width: 450px;
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.4rem;
  background-color: #211761;
  transition: all 0.3s ease;
  border: 1px solid #ffffff1a;
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  flex: 1;
}

.alert-message {
  font-size: 0.875rem;
  line-height: 1.4;
  font-weight: 500;
  font-family: 'Montserrat', sans-serif;
  word-break: break-word;
  color: #ffffff;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border-radius: 0.4rem;
  margin-left: 0.5rem;
  width: 24px;
  height: 24px;
  color: #ffffff;
}

/* Alert types with colored borders and icons */
.success {
  border-left: 4px solid #11dd11;
}

.success i {
  color: #11dd11;
}

.error {
  border-left: 4px solid #ff0000;
}

.error i {
  color: #ff0000;
}

.warning {
  border-left: 4px solid #ffff00;
}

.warning i {
  color: #ffff00;
}

.info {
  border-left: 4px solid #00ffff;
}

.info i {
  color: #00ffff;
}

/* Keep existing animation styles */
.alert-enter-active,
.alert-leave-active {
  transition: all 0.3s ease;
}

.alert-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.alert-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.alert-move {
  transition: transform 0.3s ease;
}

.fade-out {
  opacity: 0;
  transform: translateX(100%);
}
</style>







