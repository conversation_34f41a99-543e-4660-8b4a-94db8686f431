const { getAllHealthHistory } = require('../models/healthData');

async function getServerHealthHistory(req, res) {
    try {
        const healthData = await getAllHealthHistory();
        
        // Transform GPU data from string back to object for all servers
        const transformedData = {};

        for (const [serverIp, records] of Object.entries(healthData)) {
            transformedData[serverIp] = records.map(record => ({
                ...record,
                gpu_data: record.gpu_data ? JSON.parse(record.gpu_data) : null
            }));
        }

        res.json(transformedData);
    } catch (error) {
        console.error('Error fetching health history:', error);
        res.status(500).json({ error: 'Failed to fetch health history' });
    }
}

module.exports = { getServerHealthHistory };




