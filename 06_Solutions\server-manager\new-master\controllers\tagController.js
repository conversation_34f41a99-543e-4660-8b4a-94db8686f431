const db = require('../db/db');

/**
 * Get all tags
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllTags = (req, res) => {
    const query = `
        SELECT id, name, description
        FROM tags
        ORDER BY name ASC
    `;

    db.all(query, [], (err, tags) => {
        if (err) {
            console.error('Error fetching tags:', err.message);
            return res.status(500).json({ 
                success: false, 
                error: 'Failed to fetch tags' 
            });
        }

        res.json({
            success: true,
            count: tags.length,
            data: tags
        });
    });
};

/**
 * Get tag by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTagById = (req, res) => {
    const { id } = req.params;
    
    db.get(
        'SELECT id, name, description FROM tags WHERE id = ?', 
        [id], 
        (err, tag) => {
            if (err) {
                console.error('Error fetching tag:', err.message);
                return res.status(500).json({ 
                    success: false, 
                    error: 'Failed to fetch tag' 
                });
            }
            
            if (!tag) {
                return res.status(404).json({
                    success: false,
                    error: 'Tag not found'
                });
            }

            res.json({
                success: true,
                data: tag
            });
        }
    );
};

/**
 * Create a new tag
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createTag = (req, res) => {
    const { name, description } = req.body;

    if (!name || name.trim() === '') {
        return res.status(400).json({
            success: false,
            error: 'Tag name is required'
        });
    }

    const query = `INSERT INTO tags (name, description) VALUES (?, ?)`;

    db.run(query, [name, description || null], function (err) {
        if (err) {
            // Check for unique constraint violation
            if (err.message.includes('UNIQUE constraint failed')) {
                return res.status(409).json({
                    success: false,
                    error: 'A tag with this name already exists'
                });
            }

            console.error('Error creating tag:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to create tag'
            });
        }

        // Get the newly created tag
        db.get('SELECT id, name, description FROM tags WHERE id = ?', [this.lastID], (err, tag) => {
            if (err) {
                console.error('Error fetching created tag:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Tag created but failed to retrieve it'
                });
            }

            res.status(201).json({
                success: true,
                message: 'Tag created successfully',
                data: tag
            });
        });
    });
};

/**
 * Update an existing tag
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateTag = (req, res) => {
    const { id } = req.params;
    const { name, description } = req.body;

    // Check if at least one field to update is provided
    if (!name && description === undefined) {
        return res.status(400).json({
            success: false,
            error: 'At least one field (name or description) must be provided'
        });
    }

    // First check if the tag exists and get current values
    db.get('SELECT id, name, description FROM tags WHERE id = ?', [id], (err, tag) => {
        if (err) {
            console.error('Error checking tag existence:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to update tag'
            });
        }

        if (!tag) {
            return res.status(404).json({
                success: false,
                error: 'Tag not found'
            });
        }

        // Use existing values if not provided in the request
        const updatedName = name || tag.name;
        const updatedDescription = description !== undefined ? description : tag.description;

        // Validate name if it's being updated
        if (name && name.trim() === '') {
            return res.status(400).json({
                success: false,
                error: 'Tag name cannot be empty'
            });
        }

        // Update the tag
        const query = `UPDATE tags SET name = ?, description = ? WHERE id = ?`;

        db.run(query, [updatedName, updatedDescription, id], function (err) {
            if (err) {
                // Check for unique constraint violation
                if (err.message.includes('UNIQUE constraint failed')) {
                    return res.status(409).json({
                        success: false,
                        error: 'A tag with this name already exists'
                    });
                }

                console.error('Error updating tag:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to update tag'
                });
            }

            // If no rows were affected but no error occurred
            if (this.changes === 0) {
                return res.json({
                    success: true,
                    message: 'No changes were made to the tag',
                    data: tag
                });
            }

            // Get the updated tag
            db.get('SELECT id, name, description FROM tags WHERE id = ?', [id], (err, updatedTag) => {
                if (err) {
                    console.error('Error fetching updated tag:', err.message);
                    return res.status(500).json({
                        success: false,
                        error: 'Tag updated but failed to retrieve it'
                    });
                }

                res.json({
                    success: true,
                    message: 'Tag updated successfully',
                    data: updatedTag
                });
            });
        });
    });
};

/**
 * Delete a tag
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteTag = (req, res) => {
    const { id } = req.params;

    // First check if the tag exists
    db.get('SELECT id FROM tags WHERE id = ?', [id], (err, tag) => {
        if (err) {
            console.error('Error checking tag existence:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to delete tag'
            });
        }

        if (!tag) {
            return res.status(404).json({
                success: false,
                error: 'Tag not found'
            });
        }

        // Delete the tag
        const query = `DELETE FROM tags WHERE id = ?`;

        db.run(query, [id], function (err) {
            if (err) {
                console.error('Error deleting tag:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to delete tag'
                });
            }

            res.json({
                success: true,
                message: 'Tag deleted successfully',
                id: parseInt(id)
            });
        });
    });
};

module.exports = {
    getAllTags,
    getTagById,
    createTag,
    updateTag,
    deleteTag
};


