import { createApp, watch } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import PrimeVue from 'primevue/config';
import Aura from '@primeuix/themes/aura';
import 'primeicons/primeicons.css';
import { isLoading, isInitialLoad } from './router';

// Export app version
export const APP_VERSION = 'v1.0.26'; // You can update this based on your package.json

// Create a reactive user state
import { reactive } from 'vue';
export const userState = reactive({
    isLoggedIn: false,
    user: null
});

// Add this to help with debugging
watch(() => userState.isLoggedIn, (newValue) => {
    console.log('userState.isLoggedIn changed:', newValue);
});

// Set initial loading state
isLoading.value = true;
isInitialLoad.value = true;

const app = createApp(App);

// Create a global alert service
const alert = {
    show: (message, type = 'info', duration = 3000) => {
        app.config.globalProperties.$alert?.show(message, type, duration);
    }
};

app.config.globalProperties.$alert = alert;
app.provide('userState', userState);

app.use(router);
app.use(PrimeVue, {
    theme: {
        preset: Aura,
        options: {
            prefix: 'p',
            darkModeSelector: 'light',
            cssLayer: false
        }
    }
});

app.mount('#app');

