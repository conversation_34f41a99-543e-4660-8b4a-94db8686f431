const db = require('../db');

// Create a temporary table with id 0 as default
db.serialize(() => {
    console.log('🔄 Starting master server ID migration...');

    // Create new table with correct schema
    db.run(`
        CREATE TABLE IF NOT EXISTS master_server_new (
            id INTEGER PRIMARY KEY DEFAULT 0,
            server_name TEXT NOT NULL
        )
    `, (err) => {
        if (err) {
            console.error('❌ Error creating new table:', err.message);
            process.exit(1);
        }
        console.log('✅ Created temporary table');
    });

    // Copy data with new ID
    db.run(`
        INSERT INTO master_server_new (id, server_name)
        SELECT 
            0 as id,
            server_name
        FROM master_server
        WHERE id = 1
    `, (err) => {
        if (err) {
            console.error('❌ Error copying data:', err.message);
            process.exit(1);
        }
        console.log('✅ Copied data with new ID');
    });

    // Drop old table
    db.run(`DROP TABLE master_server`, (err) => {
        if (err) {
            console.error('❌ Error dropping old table:', err.message);
            process.exit(1);
        }
        console.log('✅ Dropped old table');
    });

    // Rename new table
    db.run(`ALTER TABLE master_server_new RENAME TO master_server`, (err) => {
        if (err) {
            console.error('❌ Error renaming table:', err.message);
            process.exit(1);
        }
        console.log('✅ Renamed new table');

        // Verify the migration
        db.get('SELECT * FROM master_server', (err, row) => {
            if (err) {
                console.error('❌ Error verifying migration:', err.message);
                process.exit(1);
            }
            console.log('📝 Verification - Current master server data:', row);
            console.log('✅ Migration completed successfully');
            db.close();
        });
    });
});