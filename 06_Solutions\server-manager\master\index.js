const express = require('express');
const http = require('http');
const morgan = require('morgan');
const cors = require('cors');
const cron = require('node-cron');
const axios = require('axios');
require('dotenv').config();
const refreshRouter = require('./routes/refresh');
const authRouter = require('./auth');
const adminRouter = require('./routes/admin');
const instanceRouter = require('./routes/instance');
const serverListRouter = require('./routes/serverList');
const healthHistoryRouter = require('./routes/healthHistory');
const databaseRouter = require('./routes/database');
const realtimeHealthRouter = require('./routes/realtimeHealth');
const { collectAllHealthData } = require('./services/healthService');
const { initializeHealthTables } = require('./models/healthData');
const WebSocketService = require('./services/websocketService');
const iisControlRouter = require('./routes/iisControl');

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 3000;
const WS_PORT = process.env.WS_PORT || 3001;

// Initialize WebSocket service with the HTTP server
const wsService = new WebSocketService(server);
wsService.startMasterHealthUpdates();

app.use(morgan('dev'));
app.use(cors());
app.use(express.json());

app.use('/', authRouter, serverListRouter, refreshRouter, adminRouter, instanceRouter, healthHistoryRouter, realtimeHealthRouter, iisControlRouter);
app.use('/database', databaseRouter);

// Initialize health tables when server starts
initializeHealthTables()
    .then(() => {
        console.log('✅ Health tables initialization complete');
    })
    .catch(err => {
        console.error('❌ Failed to initialize health tables:', err);
        process.exit(1);  // Exit if tables can't be created
    });

// Cron job for auto refresh (runs every hour)
cron.schedule('0 * * * *', async () => {
    console.log("🔄 Running hourly auto refresh job...");
    try {
        // Collect instances data
        const instancesResponse = await axios.get(`http://localhost:${PORT}/refresh/auto`, { timeout: 20000 });
        console.log("📜 Hourly auto refresh result:", instancesResponse.data);

        // Collect health data
        const healthData = await collectAllHealthData();
        console.log("💓 Health data collected:", {
            master: healthData.master ? 'OK' : 'Failed',
            slaves: Object.keys(healthData.slaves).length,
            errors: healthData.errors.length
        });
    } catch (error) {
        console.error("❌ Scheduled job error:", error.message);
    }
});

// Add monthly cleanup cron job (runs at midnight on the 1st of each month)
cron.schedule('0 0 1 * *', async () => {
    console.log("🧹 Running monthly health data cleanup...");
    try {
        const results = await cleanupOldHealthData(30); // 30 days retention
        console.log("📊 Cleanup Results:");
        results.forEach(result => {
            console.log(`   Server ${result.server_ip}: ${result.recordsRemoved} records removed`);
        });
    } catch (error) {
        console.error("❌ Monthly health data cleanup error:", error.message);
    }
});

// Remove or comment out the existing /health endpoint since you don't want it
// app.get('/health', async (req, res) => { ... });

server.listen(PORT, () => {
    console.log(`👑 Master server backend is running on port ${PORT}`);
    console.log(`🔌 WebSocket Server running on port ${WS_PORT}`);
});
