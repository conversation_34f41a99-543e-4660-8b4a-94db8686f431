const axios = require('axios');
const readline = require('readline');
const dotenv = require('dotenv');
const crypto = require('crypto');
const db = require('../db/db');

// Load environment variables
dotenv.config();

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// API base URL (default to localhost if not specified in .env)
const API_URL = process.env.API_URL || 'http://localhost:5000';

console.log('🔄 Login Flow Test Tool');
console.log('====================');
console.log(`🌐 Using API URL: ${API_URL}`);

// Prompt for identifier (username or email)
rl.question('👤 Enter username or email: ', async (identifier) => {
    if (!identifier) {
        console.error('❌ Username or email is required');
        rl.close();
        return;
    }

    try {
        // Step 1: Login initialization (get challenge)
        console.log('\n📤 STEP 1: Sending login-init request...');
        const initResponse = await axios.post(`${API_URL}/auth/login-init`, {
            identifier
        });
        
        const { challenge, email } = initResponse.data;
        
        console.log('✅ Login initialization successful');
        console.log('📋 Login-init Response:');
        console.log('---------------------------');
        console.log(JSON.stringify(initResponse.data, null, 2));
        console.log('---------------------------');
        
        // Now prompt for password (just for display, we'll get the hash from DB)
        rl.question('🔑 Enter password (for verification only): ', async (password) => {
            try {
                // Get the stored password hash from the database
                db.get(
                    'SELECT password FROM users WHERE username = ? OR email = ?',
                    [identifier, identifier],
                    async (err, user) => {
                        if (err || !user) {
                            console.error('❌ Error fetching user:', err?.message || 'User not found');
                            rl.close();
                            return;
                        }
                        
                        try {
                            // Step 2: Complete login (send response)
                            console.log('\n📤 STEP 2: Sending login request...');
                            
                            // Calculate response using HMAC with the stored hash
                            const storedHash = user.password;
                            const response = crypto
                                .createHmac('sha256', storedHash)
                                .update(challenge)
                                .digest('hex');
                            
                            console.log('📋 Request payload:');
                            console.log('---------------------------');
                            console.log(JSON.stringify({ identifier, response }, null, 2));
                            console.log('---------------------------');
                            
                            console.log('🔍 Debug Info:');
                            console.log('---------------------------');
                            console.log(`Stored Hash: ${storedHash}`);
                            console.log(`Challenge: ${challenge}`);
                            console.log(`Calculated Response: ${response}`);
                            console.log('---------------------------');
                            
                            const loginResponse = await axios.post(`${API_URL}/auth/login`, {
                                identifier,
                                response
                            });
                            
                            console.log('✅ Login successful');
                            console.log('📋 Login Response:');
                            console.log('---------------------------');
                            console.log(JSON.stringify(loginResponse.data, null, 2));
                            console.log('---------------------------');
                            
                            // Display JWT token details if available
                            if (loginResponse.data.token) {
                                const token = loginResponse.data.token;
                                console.log('\n🔐 JWT Token Details:');
                                console.log('---------------------------');
                                
                                // Split the token to show header and payload (don't decode signature)
                                const [headerB64, payloadB64] = token.split('.');
                                const header = JSON.parse(Buffer.from(headerB64, 'base64').toString());
                                const payload = JSON.parse(Buffer.from(payloadB64, 'base64').toString());
                                
                                console.log('Header:', JSON.stringify(header, null, 2));
                                console.log('Payload:', JSON.stringify(payload, null, 2));
                                console.log('---------------------------');
                                
                                // Show how to use the token in future requests
                                console.log('\n📝 For authenticated requests, add this header:');
                                console.log(`Authorization: Bearer ${token}`);
                            }
                        } catch (error) {
                            console.error('❌ Error during login:');
                            if (error.response) {
                                // Server responded with an error
                                console.error(`Status: ${error.response.status}`);
                                console.error('Response:', JSON.stringify(error.response.data, null, 2));
                            } else if (error.request) {
                                // Request was made but no response
                                console.error('No response received from server. Is the server running?');
                            } else {
                                // Error in setting up the request
                                console.error('Error:', error.message);
                            }
                        } finally {
                            rl.close();
                            db.close();
                        }
                    }
                );
            } catch (error) {
                console.error('❌ Error:', error.message);
                rl.close();
            }
        });
        
    } catch (error) {
        console.error('❌ Error during login initialization:');
        if (error.response) {
            // Server responded with an error
            console.error(`Status: ${error.response.status}`);
            console.error('Response:', JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            // Request was made but no response
            console.error('No response received from server. Is the server running?');
        } else {
            // Error in setting up the request
            console.error('Error:', error.message);
        }
        rl.close();
    }
});

