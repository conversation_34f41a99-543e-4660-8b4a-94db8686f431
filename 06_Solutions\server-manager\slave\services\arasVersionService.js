const axios = require('axios');
const crypto = require('crypto');

function generateRandomString(length) {
  return crypto.randomBytes(length).toString('hex').slice(0, length);
}

function base64URLEncode(str) {
  return str.toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
}

function sha256(buffer) {
  return crypto.createHash('sha256').update(buffer).digest();
}

function generatePKCE() {
  const codeVerifier = generateRandomString(64);
  const codeChallenge = base64URLEncode(sha256(codeVerifier));
  return { codeVerifier, codeChallenge };
}

async function getArasVersionInfo(appPath) {
  try {
    console.log(`Received request for Aras version at path: ${appPath}`);

    // Extract the main path by removing any subpaths like /Client
    let mainPath = appPath;

    // Check if this is a second-level path by looking for a second slash
    // First normalize the path to ensure consistent handling
    const normalizedPath = appPath.toLowerCase();

    // Find the position of the second slash (if any)
    const firstSlashPos = normalizedPath.indexOf('/');
    const secondSlashPos = firstSlashPos !== -1 ? normalizedPath.indexOf('/', firstSlashPos + 1) : -1;

    // If there's a second slash, extract the first-level path
    if (secondSlashPos !== -1) {
      mainPath = appPath.substring(0, secondSlashPos);
      console.log(`Found second-level path, using first-level path: ${mainPath}`);
    }

    console.log(`Using main path for version detection: ${mainPath}`);

    // Make a single request to the OAuth endpoint
    const baseUrl = `http://localhost${mainPath}`;
    const { codeVerifier, codeChallenge } = generatePKCE();

    const params = new URLSearchParams({
      client_id: 'InnovatorClient',
      redirect_uri: 'http://localhost/redirect-handler',
      response_type: 'code',
      scope: 'openid Innovator offline_access',
      state: generateRandomString(32),
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
      response_mode: 'query'
    });

    const url = `${baseUrl}/OAuthServer/connect/authorize?${params}`;
    console.log(`Requesting URL: ${url}`);

    const response = await axios.get(url, {
      maxRedirects: 5,
      validateStatus: status => status >= 200 && status < 500,
      timeout: 10000,
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    const html = response.data;
    console.log(`Received HTML response length: ${typeof html === 'string' ? html.length : 'not a string'}`);

    if (typeof html !== 'string') {
      throw new Error('Response is not HTML');
    }

    // Extract version and build using regex
    let version = null;
    let build = null;
    
    // Extract version/release number only (without "Release"/"Version" text)
    const type2Match = html.match(/<span id="login\.version_html">Release<\/span>\s*<span class="version-value">(.*?)<\/span>/);
    const type3Match = html.match(/<span id="login\.version_html">Version:<\/span>\s*<span class="version-value">(.*?)<\/span>/);
    const type1Match = html.match(/<span id="login\.version_html">(.*?)<\/span>/);

    if (type2Match && type2Match[1]) {
      version = type2Match[1].trim();
      console.log(`Detected Type-2 version number: ${version}`);
    }
    else if (type3Match && type3Match[1]) {
      version = type3Match[1].trim();
      console.log(`Detected Type-3 version number: ${version}`);
    }
    else if (type1Match && type1Match[1] && !type1Match[1].includes('Release')) {
      version = type1Match[1].trim(); // fallback only if no "Release" keyword
      console.log(`Detected fallback version number from Type-1: ${version}`);
    }
    else if (type1Match && type1Match[1] && type1Match[1].includes('Release')) {
      version = type1Match[1].replace(/Release/i, '').trim();
      console.log(`Detected Type-1 release number: ${version}`);
    }
    else {
      console.log('Version not found in known formats.');
    }

    // Get build number from build-value span
    const buildMatch = html.match(/<span class="build-value">(.*?)<\/span>/);
    if (buildMatch && buildMatch[1]) {
      build = buildMatch[1].trim();
      console.log(`Found build: ${build}`);
    }

    return {
      version: version || "Unknown",
      build: build || "Unknown"
    };

  } catch (error) {
    console.error(`Error fetching Aras version for ${appPath}:`, error.message);
    return { version: null, build: null, error: error.message };
  }
}

module.exports = { getArasVersionInfo };





