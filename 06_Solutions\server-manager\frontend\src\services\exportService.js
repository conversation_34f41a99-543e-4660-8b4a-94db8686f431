import * as XLSX from 'xlsx';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';  // Adds autoTable method to jsPDF prototype

function formatInstanceData(instance) {
  // Add debug log
  console.log('Formatting instance:', instance);
  
  // Safely access nested properties
  const serverName = instance.server?.name || instance.serverName || '';
  const serverIp = instance.server_ip || instance.serverIp || '';
  const instanceUrl = instance.instance_url || instance.instance || '';
  
  return {
    'Server Name': serverName,
    'Server IP': serverIp,
    'Instance Title': instance.instance_title || instance.instanceTitle || '',
    // instance name
    'Instance Name': instance.instance_name || instance.instanceName || '',
    'Instance URL': {
      text: instanceUrl,
      hyperlink: instanceUrl,
      // Style for Excel
      font: { color: { rgb: "000000" }, underline: true }
    },
    'Instance Owner': instance.instance_owner || instance.instanceOwner || '',
    'Description': instance.description || ''
  };
}

export async function exportToExcel(instances, exportType, selectedServers) {
  try {
    console.log('Starting Excel export:', { exportType, instancesCount: instances.length });
    
    // Get data based on export type
    let dataToExport = [...instances];
    
    if (exportType === 'selected' && selectedServers) {
      dataToExport = instances.filter(instance => {
        const serverIp = instance.server_ip || instance.serverIp;
        return selectedServers.has(serverIp);
      });
    }

    // Format data for export
    const formattedData = dataToExport.map(formatInstanceData);
    
    if (formattedData.length === 0) {
      throw new Error('No data available for export');
    }

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(formattedData);

    // Add hyperlinks to Instance URL column
    formattedData.forEach((row, idx) => {
      // Find the correct column index for 'Instance URL'
      const columnHeaders = Object.keys(formattedData[0]);
      const urlColumnIndex = columnHeaders.indexOf('Instance URL');
      
      // Calculate the cell reference using the correct column index
      const cellRef = XLSX.utils.encode_cell({ r: idx + 1, c: urlColumnIndex });
      
      // Set the cell value and hyperlink
      worksheet[cellRef] = {
        t: 's', // Text type
        v: row['Instance URL'].text, // Visible text
        l: { Target: row['Instance URL'].hyperlink }, // Hyperlink
        s: { // Style
          font: {
            color: { rgb: "0000FF" }, // Blue color
            underline: true
          }
        }
      };
    });

    // Set column widths
    const cols = Object.keys(formattedData[0]).map(key => ({
      wch: Math.max(
        key.length,
        ...formattedData.map(row => {
          const value = row[key];
          return typeof value === 'object' ? String(value.text).length : String(value).length;
        }),
        15
      )
    }));
    worksheet['!cols'] = cols;

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Servers');

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `server-export-${timestamp}.xlsx`;

    // Write file
    XLSX.writeFile(workbook, filename);
    return true;
  } catch (error) {
    console.error('Excel export error:', error);
    throw error;
  }
}

/**
 * Exports instance data to PDF format with tables grouped by server
 * @param {Array} instances - Array of server instances to export
 * @param {string} exportType - Type of export ('all' or 'selected')
 * @param {Set} selectedServers - Set of selected server IPs (used when exportType is 'selected')
 * @returns {Promise<boolean>} - Returns true if export is successful
 */
export async function exportToPDF(instances, exportType, selectedServers) {
  try {
    // Log export start for debugging
    console.log('Starting PDF export:', { exportType, instancesCount: instances.length });
    
    // Filter instances based on export type
    let dataToExport = [...instances];
    if (exportType === 'selected' && selectedServers) {
      dataToExport = instances.filter(instance => {
        const serverIp = instance.server_ip || instance.serverIp;
        return selectedServers.has(serverIp);
      });
    }

    // Validate if there's data to export
    if (dataToExport.length === 0) {
      throw new Error('No data available for export');
    }

    // Group instances by server IP for separate tables
    const groupedByIP = dataToExport.reduce((acc, instance) => {
      const serverIp = instance.server_ip || instance.serverIp;
      if (!acc[serverIp]) {
        acc[serverIp] = [];
      }
      acc[serverIp].push(instance);
      return acc;
    }, {});

    // Initialize PDF document in landscape mode
    const doc = new jsPDF({
      orientation: 'landscape', // Better for wide tables
      unit: 'pt',              // Use points for consistent sizing
      format: 'a4'             // Standard A4 paper size
    });

    // Add document title
    doc.setFontSize(16);
    doc.text('Server Instances Report', 40, 40);

    // Add generation timestamp
    const timestamp = new Date().toLocaleString();
    doc.setFontSize(10);
    doc.text(`Generated: ${timestamp}`, 40, 60);

    // Define table headers
    const headers = [
      'Instance Title',
      'Instance URL',
      'Owner',
      'Description'
    ];

    // Track vertical position for multiple tables
    let yPosition = 100;

    // Create tables for each server
    Object.entries(groupedByIP).forEach(([serverIp, serverInstances], index) => {
      // Add server information as section header
      const serverName = serverInstances[0]?.server?.name || 'Unknown Server';
      doc.setFontSize(12);
      doc.setTextColor(33, 23, 97); // Dark blue color for server headers
      doc.text(`${serverName} (${serverIp})`, 40, yPosition);
      
      // Format instance data for this server's table
      const data = serverInstances.map(instance => [
        instance.instance_title || instance.instanceTitle || '',
        instance.instance_url || instance.instance || '',
        instance.instance_owner || instance.instanceOwner || '',
        instance.description || ''
      ]);

      // Create table for current server
      doc.autoTable({
        startY: yPosition + 10,
        head: [headers],
        body: data,
        // Style for table headers
        headStyles: {
          fillColor: [33, 23, 97],  // Dark blue background
          textColor: [255, 255, 255], // White text
          fontSize: 10,
          fontStyle: 'bold'
        },
        // Style for table body
        bodyStyles: {
          fontSize: 9
        },
        // Special styling for URL column
        columnStyles: {
          1: { // Instance URL column (0-based index)
            textColor: [0, 0, 255], // Blue color for links
            fontStyle: 'bold underline'
          }
        },
        // Process each cell for special formatting
        didParseCell: function(data) {
          // Add hyperlinks to Instance URL column
          if (data.column.index === 1 && data.row.index > 0) {
            const url = serverInstances[data.row.index - 1].instance_url || 
                       serverInstances[data.row.index - 1].instance;
            if (url) {
              data.cell.link = { url }; // Make cell clickable
            }
          }
        },
        margin: { left: 40, right: 40 }, // Page margins
        tableWidth: 'auto' // Automatically size table
      });

      // Update position for next table
      yPosition = doc.previousAutoTable.finalY + 40;

      // Add new page if needed
      if (index < Object.keys(groupedByIP).length - 1) { // Skip for last table
        if (yPosition > doc.internal.pageSize.height - 100) {
          doc.addPage();
          yPosition = 60; // Reset position on new page
        }
      }
    });

    // Generate unique filename with timestamp
    const filename = `server-export-${new Date().getTime()}.pdf`;
    
    // Save the PDF file
    doc.save(filename);
    return true;
  } catch (error) {
    // Log any errors during export
    console.error('PDF export error:', error);
    throw error; // Re-throw to handle in calling component
  }
}





