const WebSocket = require('ws');
const db = require('../db/db');

class WebSocketService {
    constructor(server) {
        // Create a WebSocket server with path routing
        this.wss = new WebSocket.Server({ 
            server,
            path: '/realtime-health' // Add specific path for health monitoring
        });
        this.clients = new Map(); // Store connected frontend clients
        this.slaveConnections = new Map(); // Store connections to slave servers
        this.initialize();
    }

    initialize() {
        console.log('📡 Initializing WebSocket Service on path /realtime-health');
        
        this.wss.on('connection', (ws, req) => {
            const clientIp = req.socket.remoteAddress;
            console.log(`🔌 New client connected: ${clientIp}`);
            
            // Automatically handle subscription without requiring a message
            this.handleClientSubscription(ws, clientIp);
            
            ws.on('message', (message) => {
                try {
                    const data = JSON.parse(message);
                    console.log(`Received message from client ${clientIp}:`, data);
                    
                    if (data.type === 'subscribe') {
                        // Handle explicit subscription if sent
                        this.handleClientSubscription(ws, clientIp);
                    }
                } catch (error) {
                    console.error('Error handling client message:', error);
                }
            });
            
            ws.on('close', () => {
                console.log(`🔌 Client disconnected: ${clientIp}`);
                this.clients.delete(clientIp);
                
                // If no clients are connected, disconnect from slaves
                if (this.clients.size === 0) {
                    this.disconnectFromAllSlaves();
                }
            });
        });
    }
    
    handleClientSubscription(ws, clientIp) {
        // Don't re-subscribe if already subscribed
        if (this.clients.has(clientIp)) {
            return;
        }
        
        // Store the client connection
        this.clients.set(clientIp, ws);
        console.log(`Client subscribed: ${clientIp}`);
        
        // Send initial connection acknowledgment
        ws.send(JSON.stringify({
            type: 'connection_ack',
            timestamp: new Date().toISOString()
        }));
        
        // Connect to all slave servers if this is the first client
        if (this.clients.size === 1) {
            this.connectToAllSlaves();
        }
    }
    
    async connectToAllSlaves() {
        try {
            // Get all slave servers from the database
            db.all('SELECT id, ip FROM servers', (err, servers) => {
                if (err) {
                    console.error('Error fetching servers:', err);
                    return;
                }
                
                if (!servers || servers.length === 0) {
                    console.log('No servers found in database');
                    return;
                }
                
                console.log(`Connecting to ${servers.length} slave servers...`);
                
                // Connect to each slave server
                servers.forEach(server => {
                    this.connectToSlave(server.ip, server.id);
                });
            });
        } catch (error) {
            console.error('Error connecting to slaves:', error);
        }
    }
    
    connectToSlave(slaveIp, serverId) {
        // Don't create duplicate connections
        if (this.slaveConnections.has(serverId)) {
            return;
        }
        
        const SLAVE_WS_PORT = process.env.SLAVE_WS_PORT;
        const wsUrl = `ws://${slaveIp}:${SLAVE_WS_PORT}/health`;
        console.log(`Attempting to connect to slave at ${wsUrl}`);
        
        try {
            const ws = new WebSocket(wsUrl);
            
            ws.on('open', () => {
                console.log(`Connected to slave: ${serverId} (${slaveIp})`);
                this.slaveConnections.set(serverId, { ws, ip: slaveIp, lastUpdate: new Date() });
                
                // Send a message to the slave to start sending health data
                ws.send(JSON.stringify({ type: 'subscribe' }));
            });
            
            ws.on('message', (message) => {
                try {
                    const data = JSON.parse(message);
                    console.log(`Received health data from slave ${serverId}`);
                    this.broadcastHealthData(serverId, data);
                } catch (error) {
                    console.error(`Error handling message from slave ${serverId}:`, error);
                }
            });
            
            ws.on('close', () => {
                console.log(`Disconnected from slave: ${serverId} (${slaveIp})`);
                this.slaveConnections.delete(serverId);
                
                // Try to reconnect after a delay
                setTimeout(() => {
                    if (this.clients.size > 0) {
                        console.log(`Attempting to reconnect to slave: ${serverId} (${slaveIp})`);
                        this.connectToSlave(slaveIp, serverId);
                    }
                }, 8000);
            });
            
            ws.on('error', (error) => {
                console.error(`Error in slave connection ${serverId}:`, error);
                // Connection error will trigger the close event
            });
        } catch (error) {
            console.error(`Error connecting to slave ${serverId}:`, error);
        }
    }
    
    broadcastHealthData(serverId, healthData) {
        if (this.clients.size === 0) return; // Don't process if no clients
        
        const serverData = {
            // type: 'health_update',
            // timestamp: new Date().toISOString(),
            serverId: serverId,
            healthData: healthData
        };
        
        // Broadcast to all connected clients
        this.clients.forEach((ws) => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(serverData));
            }
        });
    }
    
    disconnectFromAllSlaves() {
        console.log('No clients connected. Disconnecting from all slaves...');
        this.slaveConnections.forEach((connection, serverId) => {
            console.log(`Disconnecting from slave: ${serverId}`);
            if (connection.ws.readyState === WebSocket.OPEN) {
                connection.ws.close();
            }
        });
        this.slaveConnections.clear();
    }
}

module.exports = WebSocketService;
