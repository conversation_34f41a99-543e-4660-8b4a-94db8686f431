const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const db = require('../db/db');

// In-memory session storage (consider Redis for production)
const sessions = new Map();

const initiateLogin = async (req, res) => {
    const { identifier } = req.body;

    // Generate random challenge
    const challenge = crypto.randomBytes(32).toString('hex');

    try {
        // Get user from database (both username or email)
        const user = await new Promise((resolve, reject) => {
            db.get(
                'SELECT username, email, password FROM users WHERE username = ? OR email = ?',
                [identifier, identifier],
                (err, user) => {
                    if (err || !user) return reject('Invalid credentials');
                    resolve(user);
                }
            );
        });

        // Store challenge with timestamp
        sessions.set(identifier, { challenge, timestamp: Date.now() });

        // Send challenge and email
        res.json({ challenge, email: user.email });
    } catch (err) {
        res.status(401).json({ error: err || 'Invalid credentials' });
    }
};

const completeLogin = async (req, res) => {
    const { identifier, response } = req.body;

    try {
        console.log(`Login attempt for: ${identifier}`);
        
        const session = sessions.get(identifier);
        if (!session) {
            console.log(`No session found for: ${identifier}`);
            return res.status(401).json({ error: 'Invalid session' });
        }

        console.log(`Session found with challenge: ${session.challenge}`);
        
        // Delete challenge immediately to prevent reuse
        sessions.delete(identifier);

        // Get user from database (both username or email)
        const user = await new Promise((resolve, reject) => {
            db.get(
                'SELECT * FROM users WHERE username = ? OR email = ?',
                [identifier, identifier],
                (err, user) => {
                    if (err) {
                        console.log(`Database error: ${err.message}`);
                        return reject('Database error');
                    }
                    if (!user) {
                        console.log(`User not found: ${identifier}`);
                        return reject('Invalid credentials');
                    }
                    resolve(user);
                }
            );
        });

        console.log(`User found: ${user.username}`);
        
        try {
            // Verify response using HMAC and stored password hash
            const expectedResponse = calculateResponse(user.password, session.challenge);
            
            console.log(`Expected response: ${expectedResponse}`);
            console.log(`Actual response: ${response}`);
            
            if (response === expectedResponse) {
                console.log(`Login successful for: ${user.username}`);
                
                const token = jwt.sign(
                    { id: user.id, username: user.username, email: user.email },
                    process.env.JWT_SECRET,
                    { expiresIn: '1h' }
                );
                
                return res.json({ token });
            } else {
                console.log(`Invalid response for: ${user.username}`);
                return res.status(401).json({ error: 'Invalid credentials' });
            }
        } catch (err) {
            console.log(`Error calculating response: ${err.message}`);
            return res.status(500).json({ error: 'Error calculating response' });
        }
    } catch (err) {
        console.log(`Login error: ${err}`);
        return res.status(401).json({ error: err || 'Invalid credentials' });
    }
};

function calculateResponse(storedHash, challenge) {
    try {
        return crypto
            .createHmac('sha256', storedHash)
            .update(challenge)
            .digest('hex');
    } catch (err) {
        console.log(`HMAC calculation error: ${err.message}`);
        throw err;
    }
}

module.exports = {
    initiateLogin,
    completeLogin
};
