const axios = require('axios');
const db = require('../db');

async function getMasterInstances() {
    try {
        // Get master server info from database
        const masterServer = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM servers WHERE server_type = "master"', (err, row) => {
                if (err) reject(err);
                resolve(row);
            });
        });

        if (!masterServer) {
            throw new Error("Master server not found in database");
        }

        // Fetch instances from local slave service
        const response = await axios.get(`http://${masterServer.server_ip}:3000/instances`, {
            timeout: parseInt(process.env.SLAVE_TIMEOUT_MS, 10) || 5000
        });

        return response.data;
    } catch (error) {
        console.error("❌ Error in getMasterInstances:", error.message);
        return { instances: [] }; // Return empty instances on error
    }
}

module.exports = { getMasterInstances };
