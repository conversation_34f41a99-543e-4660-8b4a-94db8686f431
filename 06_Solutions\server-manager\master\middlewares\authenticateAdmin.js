// middlewares/authenticateAdmin.js
const jwt = require('jsonwebtoken');

function authenticateAdmin(req, res, next) {
    // Check if Authorization header exists
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'No token provided' });
    }

    const token = authHeader.split(' ')[1];

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Explicitly check for admin role
        if (!decoded.role || decoded.role !== 'admin') {
            return res.status(403).json({ error: 'Access denied: Admin rights required' });
        }

        // Attach user info to request for further use
        req.user = decoded;
        next();
    } catch (error) {
        return res.status(401).json({ error: 'Invalid or expired token' });
    }
}

module.exports = authenticateAdmin;
