const axios = require('axios');
const db = require('../db/db');
const { updateApplicationsForServer } = require('../services/appRefreshService');

async function refreshAllServersAndUpdateApps(streamCallback = null) {
    const SLAVE_PORT = process.env.SLAVE_PORT;
    return new Promise((resolve, reject) => {
        db.all('SELECT * FROM servers', async (err, servers) => {
            if (err) {
                console.error("Error retrieving servers:", err.message);
                if (streamCallback) {
                    streamCallback({ error: err.message });
                }
                return reject(err);
            }
            const successful = [];
            const failed = [];
            const serverPromises = servers.map(async (server) => {
                const url = `http://${server.ip}:${SLAVE_PORT}/instances`;
                try {
                    const response = await axios.get(url, {
                        timeout: 10000
                    });
                    await updateApplicationsForServer(server.id, response.data.instances);
                    const successObj = {
                        server_ip: server.ip,
                        server_name: server.server_name || 'Unknown',
                        status: 'success'
                    };
                    if (streamCallback) streamCallback(successObj);
                    successful.push({
                        id: server.id,
                        ip: server.ip,
                        name: server.server_name || 'Unknown',
                        maintainer: server.maintainer
                    });
                } catch (error) {
                    const failObj = {
                        server_ip: server.ip,
                        server_name: server.server_name || 'Unknown',
                        status: 'failed',
                        error: error.message
                    };
                    if (streamCallback) streamCallback(failObj);
                    failed.push({
                        id: server.id,
                        ip: server.ip,
                        name: server.server_name || 'Unknown',
                        error: error.message
                    });
                }
            });
            await Promise.all(serverPromises);
            if (streamCallback) {
                streamCallback({ message: "Refresh process completed." });
            }
            resolve({ successful, failed });
        });
    });
}

const refreshInstances = async (req, res) => {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    try {
        await refreshAllServersAndUpdateApps((data) => {
            res.write(`data: ${JSON.stringify(data)}\n\n`);
        });
        res.end();
    } catch (error) {
        res.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
        res.end();
    }
};

const refreshInstancesAuto = async (req, res) => {
    try {
        const { successful, failed } = await refreshAllServersAndUpdateApps();
        res.json({
            success: true,
            message: "Auto refresh completed",
            successful,
            failed
        });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
};

module.exports = {
    refreshInstances,
    refreshInstancesAuto
};
