{"name": "slave", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "test-local": "node test-local.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.7", "express": "^4.21.2", "morgan": "^1.10.0", "node-powershell": "^5.0.1", "nodemon": "^3.1.9", "systeminformation": "^5.25.11", "ws": "^8.18.1", "xml2js": "^0.6.2"}}