<script setup>
import { ref, computed, inject, watch } from 'vue';
import { exportToExcel, exportToPDF } from '../services/exportService';

const alert = inject('alert');

const props = defineProps({
  selectedServers: {
    type: Set,
    default: () => new Set()
  },
  instances: {
    type: Array,
    required: true
  },
  allInstances: {  // Add this prop
    type: Array,
    required: true
  }
});

const isVisible = ref(false);
const selectedOption = ref('all');
const isExporting = ref(false);

// Add computed property to determine if export should be enabled
const canExport = computed(() => {
  if (selectedOption.value === 'selected') {
    return props.selectedServers.size > 0;
  }
  return props.instances.length > 0;
});

const exportOptions = computed(() => [
  { id: 'all', label: 'All Data', icon: 'pi-database', enabled: props.instances.length > 0 },
  { 
    id: 'selected', 
    label: `Selected Servers (${props.selectedServers.size})`, 
    icon: 'pi-check-square',
    enabled: props.selectedServers.size > 0
  },
  { 
    id: 'filtered', 
    label: `Searched Results (${props.instances.length})`, 
    icon: 'pi-search', 
    enabled: props.instances.length > 0 
  }
]);

const selectedFormat = ref('excel');
const formats = [
  { value: 'excel', label: 'Excel' },
  { value: 'pdf', label: 'PDF' }
];

const toggleDialog = () => {
  isVisible.value = !isVisible.value;
  if (!isVisible.value) {
    // Reset selections when closing
    selectedOption.value = 'all';
    selectedFormat.value = 'excel';
  }
};

const handleExport = async () => {
  try {
    // Use allInstances for 'all' option, and instances for 'filtered' option
    let dataToExport;
    
    switch (selectedOption.value) {
      case 'all':
        dataToExport = props.allInstances;
        break;
      case 'selected':
        dataToExport = props.allInstances.filter(instance => {
          const serverIp = instance.server_ip || instance.serverIp;
          return props.selectedServers.has(serverIp);
        });
        break;
      case 'filtered':
        dataToExport = props.instances;
        break;
      default:
        dataToExport = props.instances;
    }

    if (!dataToExport.length) {
      alert?.show('No data available to export. Please try again.', 'warn', 3000);
      return;
    }

    isExporting.value = true;
    
    let success;
    if (selectedFormat.value === 'excel') {
      success = await exportToExcel(dataToExport, selectedOption.value, props.selectedServers);
    } else {
      success = await exportToPDF(dataToExport, selectedOption.value, props.selectedServers);
    }

    if (success) {
      alert?.show('Export completed successfully. Please check your downloads folder.', 'success', 3000);
      toggleDialog();
    }
  } catch (error) {
    console.error('Export error:', error);
    alert?.show('Failed to export data: ' + error.message, 'error', 3000);
  } finally {
    isExporting.value = false;
  }
};

// Add a watcher to log when instances change
watch(() => props.instances, (newVal) => {
  console.log('Instances updated:', {
    length: newVal?.length || 0,
    isArray: Array.isArray(newVal),
    sample: newVal?.[0]
  });
}, { immediate: true });

defineExpose({ toggleDialog });
</script>

<template>
  <Teleport to="body">
    <transition
      enter-active-class="animate__animated animate__fadeIn"
      leave-active-class="animate__animated animate__fadeOut"
    >
      <div v-if="isVisible" class="dialog-overlay">
        <div class="export-dialog animate__animated animate__zoomIn">
          <div class="dialog-header">
            <h2>Export Data</h2>
            <button class="close-btn" @click="toggleDialog">
              <i class="pi pi-times"></i>
            </button>
          </div>
          
          <div class="dialog-content">
            <div class="section">
              <h3>What to export?</h3>
              <div class="export-options">
                <div 
                  v-for="option in exportOptions" 
                  :key="option.id"
                  class="export-option"
                  :class="{
                    'selected': selectedOption === option.id,
                    'disabled': !option.enabled
                  }"
                  @click="option.enabled && (selectedOption = option.id)"
                >
                  <i :class="['pi', option.icon]"></i>
                  <span>{{ option.label }}</span>
                  <i v-if="selectedOption === option.id" 
                     class="pi pi-check check-icon"></i>
                </div>
              </div>
            </div>

            <div class="section">
              <h3>Export Format</h3>
              <div class="format-options">
                <label 
                  v-for="format in formats" 
                  :key="format.value" 
                  class="format-option"
                >
                  <input 
                    type="radio" 
                    :value="format.value" 
                    v-model="selectedFormat"
                  >
                  <span>{{ format.label }}</span>
                </label>
              </div>
            </div>
          </div>

          <div class="dialog-footer">
            <button class="cancel-btn" @click="toggleDialog" :disabled="isExporting">Cancel</button>
            <button 
              class="export-btn" 
              @click="handleExport"
              :disabled="isExporting || !canExport"
            >
              <i :class="['pi', isExporting ? 'pi-spinner pi-spin' : 'pi-download']"></i>
              {{ isExporting ? 'Exporting...' : 'Export' }}
            </button>
          </div>
        </div>
      </div>
    </transition>
  </Teleport>
</template>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.export-dialog {
  background: white;
  border-radius: 0.75rem;
  width: 90%;
  max-width: 500px;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 16px rgba(33, 23, 97, 0.12);
}

.dialog-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #dddae4;
}

.dialog-header h2 {
  margin: 0;
  color: #211761;
  font-size: 1rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3rem;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: rgba(82, 55, 204, 0.05);
  color: #211761;
}

.dialog-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  min-height: 0;
}

.section {
  margin-bottom: 1rem;
}

.section h3 {
  color: #211761;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.export-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.4rem; /* Reduced from 0.5rem */
}

.export-option {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.4rem; /* Reduced from 0.5rem */
  padding: 0.35rem 0.75rem; /* Reduced height padding from 0.5rem to 0.35rem */
  border: 2px solid #dddae4;
  border-radius: 3rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  min-height: 32px; /* Reduced from 36px */
}

.export-option:not(.disabled):hover {
  border-color: #5237cc;
  background-color: rgba(82, 55, 204, 0.05);
}

.export-option.selected {
  border-color: #5237cc;
}

.export-option.selected:hover {
  background-color: rgba(82, 55, 204, 0.1);
}

.export-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #dddae4;
}

.export-option i {
  color: #5237cc;
  font-size: 0.8rem; /* Reduced from 0.875rem */
}

.check-icon {
  position: absolute;
  right: 0.75rem;
  color: #5237cc;
  font-size: 0.7rem; /* Reduced from 0.75rem */
}

.format-options {
  display: flex;
  gap: 1rem;
  padding: 0.5rem;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.format-option input {
  accent-color: #5237cc;
  width: 1rem;
  height: 1rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-top: 1px solid #dddae4;
}

.cancel-btn, .export-btn {
  padding: 0.5rem 1rem;
  border-radius: 3rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.cancel-btn {
  background: none;
  border: 2px solid #dddae4;
  color: #666;
}

.cancel-btn:hover {
  border-color: #211761;
  color: #211761;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #211761;
  border: none;
  color: white;
}

.export-btn:hover {
  background: #5237cc;
}

.export-btn i {
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-height: 600px) {
  .export-dialog {
    max-height: 90vh;
  }

  .dialog-content {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .export-dialog {
    width: 95%;
  }

  .dialog-header,
  .dialog-content,
  .dialog-footer {
    padding: 0.625rem;
  }

  .export-options {
    grid-template-columns: 1fr;
  }

  .export-option {
    padding: 0.3rem 0.625rem; /* Further reduced for mobile */
    font-size: 0.813rem;
    min-height: 28px; /* Reduced from 32px */
  }

  .format-option {
    font-size: 0.813rem;
  }

  .cancel-btn, .export-btn {
    padding: 0.5rem 0.875rem;
    font-size: 0.813rem;
  }
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>










