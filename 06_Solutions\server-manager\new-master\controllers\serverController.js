const db = require('../db/db');

/**
 * Get all servers
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllServers = (req, res) => {
    const query = `SELECT * FROM servers`;

    db.all(query, [], (err, servers) => {
        if (err) {
            console.error('Error fetching servers:', err.message);
            return res.status(500).json({ 
                success: false, 
                error: 'Failed to fetch servers' 
            });
        }

        res.json({
            success: true,
            count: servers.length,
            data: servers
        });
    });
};

/**
 * Get server by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getServerById = (req, res) => {
    const { id } = req.params;
    
    db.get('SELECT * FROM servers WHERE id = ?', [id], (err, server) => {
        if (err) {
            console.error('Error fetching server:', err.message);
            return res.status(500).json({ 
                success: false, 
                error: 'Failed to fetch server' 
            });
        }
        
        if (!server) {
            return res.status(404).json({
                success: false,
                error: 'Server not found'
            });
        }

        res.json({
            success: true,
            data: server
        });
    });
};

/**
 * Create a new server
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createServer = (req, res) => {
    const { ip, server_name, maintainer } = req.body;

    // Validate required fields
    if (!ip) {
        return res.status(400).json({
            success: false,
            error: 'Server IP is required'
        });
    }

    // Prepare query and parameters
    let query, params;

    if (server_name || maintainer) {
        query = `INSERT INTO servers (ip, server_name, maintainer) VALUES (?, ?, ?)`;
        params = [ip, server_name || null, maintainer || null];
    } else {
        query = `INSERT INTO servers (ip) VALUES (?)`;
        params = [ip];
    }

    db.run(query, params, function (err) {
        if (err) {
            // Check for unique constraint violations
            if (err.message.includes('UNIQUE constraint failed: servers.ip')) {
                return res.status(409).json({
                    success: false,
                    error: 'A server with this IP already exists'
                });
            }

            console.error('Error creating server:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to create server'
            });
        }

        // Get the newly created server
        db.get('SELECT * FROM servers WHERE id = ?', [this.lastID], (err, server) => {
            if (err) {
                console.error('Error fetching created server:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Server created but failed to retrieve details'
                });
            }

            res.status(201).json({
                success: true,
                message: 'Server created successfully',
                data: server
            });
        });
    });
};

/**
 * Update an existing server
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateServer = (req, res) => {
    const { id } = req.params;
    const { server_name, maintainer } = req.body;

    // Check if at least one field to update is provided
    if (!server_name && maintainer === undefined) {
        return res.status(400).json({
            success: false,
            error: 'At least one field (server_name or maintainer) must be provided'
        });
    }

    // First check if the server exists
    db.get('SELECT id, server_name, maintainer FROM servers WHERE id = ?', [id], (err, server) => {
        if (err) {
            console.error('Error checking server existence:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to update server'
            });
        }

        if (!server) {
            return res.status(404).json({
                success: false,
                error: 'Server not found'
            });
        }

        // Use existing values if not provided in the request
        const updatedName = server_name !== undefined ? server_name : server.server_name;
        const updatedMaintainer = maintainer !== undefined ? maintainer : server.maintainer;

        // Update the server
        const query = `UPDATE servers SET server_name = ?, maintainer = ? WHERE id = ?`;

        db.run(query, [updatedName, updatedMaintainer, id], function (err) {
            if (err) {
                console.error('Error updating server:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to update server'
                });
            }

            // If no rows were affected but no error occurred
            if (this.changes === 0) {
                return res.json({
                    success: true,
                    message: 'No changes were made to the server',
                    data: server
                });
            }

            // Get the updated server
            db.get('SELECT * FROM servers WHERE id = ?', [id], (err, updatedServer) => {
                if (err) {
                    console.error('Error fetching updated server:', err.message);
                    return res.status(500).json({
                        success: false,
                        error: 'Server updated but failed to retrieve details'
                    });
                }

                res.json({
                    success: true,
                    message: 'Server updated successfully',
                    data: updatedServer
                });
            });
        });
    });
};

/**
 * Delete a server
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteServer = (req, res) => {
    const { id } = req.params;

    // First check if the server exists
    db.get('SELECT id, ip FROM servers WHERE id = ?', [id], (err, server) => {
        if (err) {
            console.error('Error checking server existence:', err.message);
            return res.status(500).json({
                success: false,
                error: 'Failed to delete server'
            });
        }

        if (!server) {
            return res.status(404).json({
                success: false,
                error: 'Server not found'
            });
        }

        // Delete the server
        const query = `DELETE FROM servers WHERE id = ?`;

        db.run(query, [id], function (err) {
            if (err) {
                console.error('Error deleting server:', err.message);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to delete server'
                });
            }

            res.json({
                success: true,
                message: 'Server deleted successfully',
                data: {
                    id: parseInt(id),
                    ip: server.ip
                }
            });
        });
    });
};

module.exports = {
    getAllServers,
    getServerById,
    createServer,
    updateServer,
    deleteServer
};

