const db = require('../db');

console.log('🔄 Starting migration to replace server_ip with server_id in INSTANCE_DATA table...');

db.serialize(() => {
    // 1. Create new table with server_id
    db.run(`
        CREATE TABLE INSTANCE_DATA_NEW (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            server_id INTEGER NOT NULL,
            instance_name TEXT NOT NULL,
            instance_title TEXT,
            instance_url TEXT NOT NULL,
            description TEXT,
            instance_owner TEXT,
            is_public INTEGER DEFAULT 0,
            FOREIGN KEY (server_id) REFERENCES servers(id)
        )
    `, (err) => {
        if (err) {
            console.error('❌ Error creating new table:', err.message);
            process.exit(1);
        }
        console.log('✅ Created new table with server_id');
    });

    // 2. Copy data with server_id instead of server_ip
    db.run(`
        INSERT INTO INSTANCE_DATA_NEW (
            ID, server_id, instance_name, instance_title,
            instance_url, description, instance_owner, is_public
        )
        SELECT 
            i.ID,
            s.id as server_id,
            i.instance_name,
            i.instance_title,
            i.instance_url,
            i.description,
            i.instance_owner,
            i.is_public
        FROM INSTANCE_DATA i
        JOIN servers s ON i.server_ip = s.server_ip
    `, (err) => {
        if (err) {
            console.error('❌ Error migrating data:', err.message);
            process.exit(1);
        }
        console.log('✅ Migrated existing data');
    });

    // 3. Drop old table
    db.run(`DROP TABLE INSTANCE_DATA`, (err) => {
        if (err) {
            console.error('❌ Error dropping old table:', err.message);
            process.exit(1);
        }
        console.log('✅ Dropped old table');
    });

    // 4. Rename new table
    db.run(`ALTER TABLE INSTANCE_DATA_NEW RENAME TO INSTANCE_DATA`, (err) => {
        if (err) {
            console.error('❌ Error renaming table:', err.message);
            process.exit(1);
        }
        console.log('✅ Renamed new table');

        // 5. Create index for better performance
        db.run(`CREATE INDEX idx_instance_server_id ON INSTANCE_DATA(server_id)`, (err) => {
            if (err) {
                console.error('❌ Error creating index:', err.message);
                process.exit(1);
            }
            console.log('✅ Created index on server_id');
        });

        // 6. Verify migration
        db.all(`
            SELECT i.*, s.server_ip, s.server_name 
            FROM INSTANCE_DATA i 
            JOIN servers s ON i.server_id = s.id 
            LIMIT 5
        `, (err, rows) => {
            if (err) {
                console.error('❌ Error verifying migration:', err.message);
                process.exit(1);
            }
            console.log('📝 Sample of migrated data:', rows);
            console.log('✅ Migration completed successfully');
            db.close();
        });
    });
});