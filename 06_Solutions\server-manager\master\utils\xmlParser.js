// utils/xmlParser.js
const fs = require('fs');
const xml2js = require('xml2js');

async function parseXMLFile(filePath) {
    try {
        const xmlData = fs.readFileSync(filePath, "utf-8");
        const parser = new xml2js.Parser({
            explicitArray: false,
            mergeAttrs: true,
            explicitRoot: false
        });
        return await parser.parseStringPromise(xmlData);
    } catch (error) {
        console.error("❌ Error reading XML file:", error);
        throw error;
    }
}

module.exports = { parseXMLFile };
