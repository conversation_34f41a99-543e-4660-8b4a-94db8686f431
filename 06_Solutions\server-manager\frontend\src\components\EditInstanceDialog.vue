<script setup>
import { ref, watch, computed } from 'vue';
import DatePicker from 'primevue/datepicker';

const props = defineProps({
  instance: {
    type: Object,
    required: true
  }
});

const isVisible = ref(false);
const editedInstance = ref({});

// Add new refs for field validation
const fieldErrors = ref({
  instance_title: '',
  description: '',
  instance_owner: '',
  status: '',
  version: '',
  build_number: ''
});

// Updated character limits
const LIMITS = {
  instance_title: 40,
  description: 100,
  instance_owner: 40
};

// Computed properties for remaining characters
const remainingChars = computed(() => ({
  instance_title: LIMITS.instance_title - (editedInstance.value.instance_title?.length || 0),
  description: LIMITS.description - (editedInstance.value.description?.length || 0),
  instance_owner: LIMITS.instance_owner - (editedInstance.value.instance_owner?.length || 0)
}));

// Computed property to check if form is valid for public status
const canBePublic = computed(() => {
  return editedInstance.value.instance_title?.trim() &&
    editedInstance.value.description?.trim() &&
    editedInstance.value.instance_owner?.trim();
});

// Watch for instance changes
watch(() => props.instance, () => {
  if (props.instance) {
    editedInstance.value = {
      ...props.instance,
      instance_title: props.instance.instance_title || '',
      description: props.instance.description || '',
      instance_owner: props.instance.instance_owner || ''
    };
  }
}, { immediate: true });

const toggleDialog = () => {
  isVisible.value = !isVisible.value;
};

const handleSave = () => {
  clearFieldErrors();

  if (editedInstance.value.is_public === 1) {
    const emptyFields = validateRequiredFields();

    if (emptyFields.length > 0) {
      emptyFields.forEach(field => {
        fieldErrors.value[field.toLowerCase().replace(' ', '_')] =
          `${field} is required for public instances.`;
      });
      return;
    }
  }

  const updatedInstance = {
    ...editedInstance.value,
    instance_title: editedInstance.value.instance_title?.trim(),
    description: editedInstance.value.description?.trim(),
    instance_owner: editedInstance.value.instance_owner?.trim()
  };

  emit('save', updatedInstance);
};

const emit = defineEmits(['save', 'validation-error']);

const setVisibility = (value) => {
  if (value === 1) {
    const emptyFields = validateRequiredFields();
    emptyFields.forEach(field => {
      const key = field.toLowerCase().replace(' ', '_');
      if (key === 'status' && !editedInstance.value.status) {
        fieldErrors.value.status = 'Status is required for public instances.';
      } else if (key !== 'status') {
        fieldErrors.value[key] = `${field} is required for public instances.`;
      }
    });
    if (emptyFields.length === 0) {
      editedInstance.value.is_public = value;
    }
    return;
  }

  editedInstance.value.is_public = value;
  if (value === 0) {
    clearFieldErrors();
  }
};

// Add function to clear field errors
const clearFieldErrors = () => {
  Object.keys(fieldErrors.value).forEach(key => {
    fieldErrors.value[key] = '';
  });
};

// Input validation handlers
const validateInput = (field, value) => {
  if (value.length > LIMITS[field]) {
    editedInstance.value[field] = value.slice(0, LIMITS[field]);
  }
};

// Add input focus handler to clear error when field is focused
const handleFieldFocus = (field) => {
  fieldErrors.value[field] = '';
};

// Add validation function
const validateRequiredFields = () => {
  const requiredFields = {
    instance_title: 'Instance Title',
    description: 'Description',
    instance_owner: 'Instance Owner',
    status: 'Status',
    version: 'Version',
    build_number: 'Build Number'
  };

  const emptyFields = [];

  for (const [field, label] of Object.entries(requiredFields)) {
    if (!editedInstance.value[field]?.toString().trim()) {
      emptyFields.push(label);
    }
  }

  return emptyFields;
};

const newCredential = ref({ username: '', password: '' });

const addCredential = () => {
  if (newCredential.value.username && newCredential.value.password) {
    if (!editedInstance.value.credentials) editedInstance.value.credentials = [];
    editedInstance.value.credentials.push({
      username: newCredential.value.username,
      password: newCredential.value.password
    });
    newCredential.value.username = '';
    newCredential.value.password = '';
  }
};

const removeCredential = (idx) => {
  if (editedInstance.value.credentials) {
    editedInstance.value.credentials.splice(idx, 1);
  }
};

const availableTags = ref([
  'Production',
  'Staging',
  'Development',
  'QA',
  'Legacy',
  'Critical',
  'Test',
  'Internal',
  'External',
  'Cloud',
  'On-Prem',
]);

// Ensure tags array exists
watch(() => editedInstance.value, (val) => {
  if (val && !Array.isArray(val.tags)) {
    editedInstance.value.tags = [];
  }
}, { immediate: true });

// Add tag if selected and not already present
function addTag() {
  if (
    editedInstance.value.selectedTag &&
    !editedInstance.value.tags.includes(editedInstance.value.selectedTag)
  ) {
    editedInstance.value.tags.push(editedInstance.value.selectedTag);
    editedInstance.value.selectedTag = '';
  }
}

// Remove tag by index
function removeTag(idx) {
  editedInstance.value.tags.splice(idx, 1);
}

// Add tag directly by clicking
function addTagDirect(tag) {
  if (!editedInstance.value.tags.includes(tag)) {
    editedInstance.value.tags.push(tag);
  }
}

defineExpose({ toggleDialog });

const handleStatusClick = (value) => {
  if (fieldErrors.value.status) {
    // If error is present, keep it until valid selection
    if (!value) {
      fieldErrors.value.status = 'Status is required for public instances.';
      return;
    }
  }
  fieldErrors.value.status = '';
  editedInstance.value.status = value;
};
</script>

<template>
  <Teleport to="body">
    <transition enter-active-class="animate__animated animate__fadeIn"
      leave-active-class="animate__animated animate__fadeOut">
      <div v-if="isVisible" class="dialog-overlay">
        <div class="edit-dialog animate__animated animate__zoomIn">
          <div class="dialog-header">
            <h2>Edit Environment <span class="env-type-label">environment</span></h2>
            <span class="header-env-name">{{ editedInstance.instance_name }}</span>
            <button class="close-btn" @click="toggleDialog">
              <i class="pi pi-times"></i>
            </button>
          </div>

          <div class="instance-info">
            <div class="info-item">
              <span class="info-label">Server IP:</span>
              <span class="info-value">{{ editedInstance.server_ip || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Internal URL:</span>
              <a :href="editedInstance.instance_url" target="_blank" class="info-value url">
                {{ editedInstance.instance_url }}
              </a>
            </div>
          </div>

          <div class="dialog-content">
            <div class="form-group">
              <label>Environment Name</label>
              <div class="input-container">
                <input type="text" v-model="editedInstance.instance_title" class="form-control"
                  :class="{ 'error': fieldErrors.instance_title }" placeholder="Enter instance title"
                  @input="validateInput('instance_title', editedInstance.instance_title)"
                  @focus="handleFieldFocus('instance_title')" maxlength="70">
                <span class="char-counter" :class="{ 'warning': remainingChars.instance_title < 15 }">
                  {{ remainingChars.instance_title }}/{{ LIMITS.instance_title }}
                </span>
                <span v-if="fieldErrors.instance_title" class="error-message">
                  {{ fieldErrors.instance_title }}
                </span>
              </div>
            </div>

            <div class="form-group">
              <label>Owner</label>
              <div class="input-container">
                <input type="text" v-model="editedInstance.instance_owner" class="form-control"
                  :class="{ 'error': fieldErrors.instance_owner }" placeholder="Enter instance owner"
                  @input="validateInput('instance_owner', editedInstance.instance_owner)"
                  @focus="handleFieldFocus('instance_owner')" maxlength="40">
                <span class="char-counter" :class="{ 'warning': remainingChars.instance_owner < 10 }">
                  {{ remainingChars.instance_owner }}/{{ LIMITS.instance_owner }}
                </span>
                <span v-if="fieldErrors.instance_owner" class="error-message">
                  {{ fieldErrors.instance_owner }}
                </span>
              </div>
            </div>

            <div class="form-group">
              <label>Version</label>
              <div class="input-container">
                <input type="text" v-model="editedInstance.version" class="form-control"
                  :class="{ 'error': fieldErrors.version }" placeholder="Enter version">
                <span v-if="fieldErrors.version" class="error-message">{{ fieldErrors.version }}</span>
              </div>
            </div>

            <div class="form-group">
              <label>Build Number</label>
              <div class="input-container">
                <input type="text" v-model="editedInstance.build_number" class="form-control"
                  :class="{ 'error': fieldErrors.build_number }" placeholder="Enter build number">
                <span v-if="fieldErrors.build_number" class="error-message">{{ fieldErrors.build_number }}</span>
              </div>
            </div>

            <div class="form-group">
              <label>Databases</label>
              <div class="input-container">
                <div class="static-databases-list">
                  <span v-if="editedInstance.databases && editedInstance.databases.length">
                    <span v-for="(db, idx) in editedInstance.databases" :key="db.id || db" class="database-chip">{{
                      db.name || db }}</span>
                  </span>
                  <span v-else class="no-databases">No databases</span>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label>Description</label>
              <div class="input-container">
                <textarea v-model="editedInstance.description" class="form-control"
                  :class="{ 'error': fieldErrors.description }" rows="4" placeholder="Enter description"
                  @input="validateInput('description', editedInstance.description)"
                  @focus="handleFieldFocus('description')" maxlength="100"></textarea>
                <span class="char-counter" :class="{ 'warning': remainingChars.description < 20 }">
                  {{ remainingChars.description }}/{{ LIMITS.description }}
                </span>
                <span v-if="fieldErrors.description" class="error-message">
                  {{ fieldErrors.description }}
                </span>
              </div>
            </div>

            <div class="form-group">
              <label>Expected Deletion Date</label>
              <div class="input-container primevue-date-container">
                <DatePicker v-model="editedInstance.expected_deletion_date" class="form-control primevue-date-input"
                  inputId="expected-deletion-date" :showIcon="true" dateFormat="dd-mm-yy" placeholder="Select date"
                  :pt="{ input: { style: 'width: 100%;' } }" />
              </div>
            </div>

            <div class="form-group">
              <label>External URL</label>
              <div class="input-container">
                <input type="url" v-model="editedInstance.external_url" class="form-control"
                  placeholder="Enter external URL">
              </div>
            </div>

            <div class="form-group">
              <label>Status</label>
              <div class="status-options" :class="{ 'error': fieldErrors.status }">
                <button class="status-btn active-btn" :class="{ 'active': editedInstance.status === 'active' }"
                  @click="handleStatusClick('active')">
                  <div class="btn-content">
                    <i class="pi pi-check-circle"></i>
                    <span class="btn-text">Active</span>
                  </div>
                  <span class="btn-description">Currently in use</span>
                </button>
                <button class="status-btn obsolete-btn" :class="{ 'active': editedInstance.status === 'obsolete' }"
                  @click="handleStatusClick('obsolete')">
                  <div class="btn-content">
                    <i class="pi pi-times-circle"></i>
                    <span class="btn-text">Obsolete</span>
                  </div>
                  <span class="btn-description">No longer in use</span>
                </button>
              </div>
              <span v-if="fieldErrors.status" class="error-message">{{ fieldErrors.status }}</span>
            </div>

            <div class="form-group">
              <div class="visibility-container">
                <label class="visibility-label">Instance Visibility</label>
                <div class="visibility-options">
                  <button class="visibility-btn private-btn" :class="{ 'active': editedInstance.is_public === 0 }"
                    @click="() => setVisibility(0)">
                    <div class="btn-content">
                      <i class="pi pi-lock"></i>
                      <span class="btn-text">Private</span>
                    </div>
                    <span class="btn-description">Only admins can view</span>
                  </button>

                  <button class="visibility-btn public-btn" :class="{ 'active': editedInstance.is_public === 1 }"
                    @click="() => setVisibility(1)">
                    <div class="btn-content">
                      <i class="pi pi-globe"></i>
                      <span class="btn-text">Public</span>
                    </div>
                    <span class="btn-description">Everyone can view</span>
                  </button>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label>Tags</label>
              <div class="input-container">
                <select v-model="editedInstance.selectedTag" class="form-control tag-dropdown" @change="addTag">
                  <option disabled value="">Select tag</option>
                  <option v-for="tag in availableTags" :key="tag" :value="tag"
                    :disabled="editedInstance.tags.includes(tag)">{{ tag }}</option>
                </select>
                <div class="selected-tags-bar">
                  <span v-for="(tag, idx) in editedInstance.tags" :key="tag.id || tag" class="database-chip">
                    {{ tag.name || tag }}
                    <button class="remove-tag-btn" @click.prevent="removeTag(idx)">×</button>
                  </span>
                </div>
              </div>
            </div>

            <div class="form-group credentials-group">
              <label>Credentials</label>
              <div class="credentials-list">
                <!-- Add Credential Form -->
                <div class="add-credential-form">
                  <input type="text" v-model="newCredential.username" class="form-control" placeholder="Username">
                  <input type="text" v-model="newCredential.password" class="form-control" placeholder="Password">
                  <button class="save-cred-btn" @click.prevent="addCredential">Save</button>
                </div>
                <!-- List of Saved Credentials as a table -->
                <table class="credentials-table" v-if="editedInstance.credentials && editedInstance.credentials.length">
                  <thead>
                    <tr>
                      <th class="cred-th">Username</th>
                      <th class="cred-th">Password</th>
                      <th class="cred-th"></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(cred, idx) in editedInstance.credentials" :key="idx">
                      <td class="cred-username">{{ cred.username }}</td>
                      <td class="cred-password">{{ cred.password }}</td>
                      <td><button class="remove-cred-btn" @click.prevent="removeCredential(idx)">×</button></td>
                    </tr>
                  </tbody>
                </table>
                <!-- Fallback for no credentials -->
                <div v-else class="no-credentials">No credentials added</div>
              </div>
            </div>
          </div>

          <div class="dialog-footer">
            <button class="cancel-btn" @click="toggleDialog">Cancel</button>
            <button class="save-btn" @click="handleSave">Save Changes</button>
          </div>
        </div>
      </div>
    </transition>
  </Teleport>
</template>

<style scoped>
.instance-info {
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dddae4;
}

.info-item {
  margin-bottom: 0.4rem;
  display: flex;
  gap: 0.4rem;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
  font-weight: 500;
  min-width: 100px;
  font-size: 0.875rem;
}

.info-value {
  color: #211761;
  font-weight: 500;
  font-size: 0.875rem;
}

.info-value.url {
  color: #5237cc;
  text-decoration: none;
  font-size: 0.875rem;
}

.info-value.url:hover {
  text-decoration: underline;
}

.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 0.75rem;
}

.edit-dialog {
  background: white;
  border-radius: 0.75rem;
  width: 90%;
  max-width: 800px;
  /* Increased from 500px */
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 16px rgba(33, 23, 97, 0.12);
}

.dialog-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #dddae4;
}

.dialog-header h2 {
  margin: 0;
  color: #211761;
  font-size: 1rem;
  font-weight: 600;
}

.env-label {
  font-size: 0.8rem;
  color: #888;
  margin-left: 0.5rem;
  vertical-align: middle;
}

.env-type-label {
  margin-left: 0.5em;
  color: #888;
  font-size: 0.95em;
  font-weight: 400;
  letter-spacing: 0.5px;
}

.header-env-name {
  margin-left: 1.2rem;
  color: #5237cc;
  font-size: 1rem;
  font-weight: 500;
  align-self: center;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn i {
  font-size: 0.875rem;
}

.close-btn:hover {
  color: #211761;
}

.dialog-content {
  flex: 1;
  padding: 1rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  /* Create two columns */
  gap: 1rem;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 0.75rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.4rem;
  color: #211761;
  font-weight: 500;
  font-size: 0.875rem;
}

.form-control {
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
  padding: 0.625rem 0.75rem;
  border: 2px solid #dddae4;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
  height: 42px;
  /* Explicitly set input height for alignment */
}

.form-control.error {
  border-color: #dc3545;
}

.form-control::placeholder {
  color: #a8a4b7;
  font-size: 0.875rem;
}

.form-control:focus {
  outline: none;
  border-color: #5237cc;
}

.form-control.disabled {
  display: none;
}

textarea.form-control {
  resize: none;
  height: 85px;
  /* Increased height to fit 100 characters without scrolling */
  min-height: unset;
  max-height: unset;
  line-height: 1.5;
  padding: 0.625rem 0.75rem 30px 0.75rem;
  /* Keep bottom padding for counter */
  overflow-y: hidden;
  /* Remove scrolling */
  font-size: 0.875rem;
}

.dialog-footer {
  flex-shrink: 0;
  padding: 0.75rem 1rem;
  border-top: 1px solid #dddae4;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.cancel-btn,
.save-btn {
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.cancel-btn {
  background: none;
  border: 2px solid #dddae4;
  color: #666;
}

.cancel-btn:hover {
  border-color: #211761;
  color: #211761;
}

.save-btn {
  background: #211761;
  border: none;
  color: white;
}

.save-btn:hover {
  background: #5237cc;
}

.visibility-container {
  grid-column: 1 / -1;
  /* Make visibility container span both columns */
  /* margin-top: 0.5rem; */
}

.visibility-label {
  display: block;
  /* Added */
  margin-bottom: 0.4rem;
  /* Match form-group label */
  color: #211761;
  /* Match form-group label */
  font-weight: 500;
  /* Match form-group label */
  font-size: 0.875rem;
  /* Match form-group label */
}

.visibility-options {
  display: flex;
  gap: 0.75rem;
}

.visibility-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  border: 2px solid #dddae4;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  max-width: 200px;
}

.visibility-btn:hover {
  border-color: #5237cc;
}

.visibility-btn:hover i {
  color: #5237cc;
}

/* Change active state border color */
.visibility-btn.active {
  border-color: #5237cc;
}

.visibility-btn i {
  font-size: 1rem;
  color: #6c757d;
  transition: color 0.2s ease;
  line-height: 1;
  vertical-align: middle;
  display: inline-block;
}

.visibility-btn .btn-text {
  font-weight: 600;
  font-size: 0.9rem;
  color: #333;
  line-height: 1;
  vertical-align: middle;
  display: inline-block;
}

.btn-description {
  font-size: 0.7rem;
  /* Reduced from 0.75rem */
  color: #666;
  text-align: center;
  margin-top: 0.25rem;
}

.status-options {
  display: flex;
  gap: 0.75rem;
}

.status-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  border: 2px solid #dddae4;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  max-width: 200px;
}

.status-btn:hover {
  border-color: #5237cc;
}

.status-btn:hover i {
  color: #5237cc;
}

.status-btn.active {
  border-color: #5237cc;
}

.status-btn i {
  font-size: 1rem;
  color: #6c757d;
  transition: color 0.2s ease;
  line-height: 1;
  vertical-align: middle;
  display: inline-block;
}

.status-btn .btn-text {
  font-weight: 600;
  font-size: 0.9rem;
  color: #333;
  line-height: 1;
  vertical-align: middle;
  display: inline-block;
}

.status-btn .btn-description {
  font-size: 0.7rem;
  color: #666;
  text-align: center;
  margin-top: 0.25rem;
}

.status-btn .btn-content,
.visibility-btn .btn-content {
  display: flex;
  align-items: center;
  gap: 0.4em;
}

.error-message {
  color: #dc3545;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
}

.visibility-btn.public-btn {
  /* Remove disabled styling */
  opacity: 1;
  cursor: pointer;
}

.static-databases-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  min-height: 32px;
  align-items: center;
}

.database-chip {
  background: #f3f0ff;
  color: #5237cc;
  border-radius: 16px;
  padding: 0.25rem 0.75rem;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #e0e0e0;
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
  user-select: text;
}

.no-databases {
  color: #aaa;
  font-size: 0.85rem;
}

.add-credential-form {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.save-cred-btn {
  background: #211761;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 0 0.7rem;
  font-size: 0.85rem;
  cursor: pointer;
  transition: background 0.2s;
  min-width: 40px;
  width: 40px;
  height: 42px;
  /* Match input height exactly */
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.save-cred-btn:hover {
  background: #5237cc;
}

/* Add styles for credentials table */
.credentials-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 0.3rem;
  margin-top: 0.5rem;
  font-size: 0.92em;
}

.cred-th {
  text-align: left;
  color: #888;
  font-size: 0.78em;
  font-weight: 500;
  padding-bottom: 0.2rem;
}

.credentials-table td {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 0.3rem 0.7rem;
  vertical-align: middle;
}

.credentials-table .cred-username {
  color: #211761;
  font-weight: 500;
}

.credentials-table .cred-password {
  color: #888;
  font-size: 0.95em;
  letter-spacing: 2px;
}

.credentials-table .remove-cred-btn {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 1.1rem;
  cursor: pointer;
  margin-left: 0.5rem;
}

.credentials-table .remove-cred-btn:hover {
  color: #a80000;
}

.no-credentials {
  color: #aaa;
  font-size: 0.9em;
  margin-top: 0.5rem;
}

.primevue-date-container {
  position: relative;
}

.primevue-date-input {
  width: 100%;
  /* Remove custom border removal, let .form-control style apply */
  /* border: 2px solid #dddae4;
  border-radius: 0.375rem; */
  font-size: 0.875rem;
  /* box-shadow: 0 1px 2px rgba(33, 23, 97, 0.04); */
  background: #fff;
  transition: border-color 0.2s;
  height: 42px;
}

:deep(.p-inputtext) {
  border: 2px solid #dddae4 !important;
  /* border-radius: 0.375rem !important; */
  font-size: 0.875rem !important;
  /* box-shadow: 0 1px 2px rgba(33, 23, 97, 0.04) !important; */
  /* background: #fff !important; */
  transition: border-color 0.2s !important;
  /* height: 42px !important; */
}

:deep(.p-inputtext:focus) {
  border-color: #5237cc !important;
  outline: none !important;
}

:deep(.p-datepicker) {
  /* min-width: 180px !important; */
  width: 100% !important;
  height: 42px;
  max-width: 100% !important;
  font-size: 0.85rem !important;
  padding: 0.3rem 0.3rem 0.2rem 0.3rem !important;
  /* box-shadow: 0 2px 8px rgba(33, 23, 97, 0.08); */
  /* border: none !important; */
}

.input-container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
}

select.form-control.tag-dropdown {
  margin-bottom: 0.5rem;
}

.char-counter {
  position: absolute;
  right: 12px;
  bottom: 7px;
  font-size: 0.68rem;
  color: #888;
  background: transparent;
  padding: 0;
  border-radius: 0;
  pointer-events: none;
  z-index: 2;
}

.char-counter.warning {
  color: #e65100;
  font-weight: 600;
}

textarea.form-control {
  padding-bottom: 22px;
}

input.form-control {
  padding-right: 60px;
}

.selected-tags-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 0rem 0.1rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  /* row-gap: 0.2rem; */
}
</style>
