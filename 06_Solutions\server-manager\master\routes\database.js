const express = require('express');
const router = express.Router();
const { body, param, validationResult } = require('express-validator');
const authenticateAdmin = require('../middlewares/authenticateAdmin');
const databaseController = require('../controllers/databaseController');

// Apply admin authentication to ALL routes in this router
// router.use(authenticateAdmin);

// Validation middleware
const validateRequest = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }
    next();
};

// Remove all instances (Admin only)
router.delete('/instances/all', authenticateAdmin, databaseController.removeAllInstances);

// Remove instances by server IP (Admin only)
router.delete('/instances/ip/:server_ip', 
    param('server_ip').isIP(),
    validateRequest,
    databaseController.removeInstancesByIP
);

// Remove all health data (Admin only)
router.delete('/health/all', authenticateAdmin, databaseController.removeAllHealthData);

// Remove health data by server IP (Admin only)
router.delete('/health/ip/:server_ip',
    param('server_ip').isIP(),
    validateRequest,
    databaseController.removeHealthDataByIP
);

// Set all instances to private (Admin only)
router.put('/instances/private', authenticateAdmin, databaseController.setAllInstancesPrivate);

// Remove all non-admin users (Admin only)
router.delete('/users/non-admin', authenticateAdmin, databaseController.removeNonAdminUsers);

// Create new user (Admin only)
router.post('/users/new', authenticateAdmin, 
    [
        body('username').isString().notEmpty(),
        body('email').isEmail().optional(),  // Email is optional
        body('password').isString().isLength({ min: 6 }),
        body('role').isIn(['admin', 'user'])
    ],
    validateRequest,
    databaseController.createUser
);

// Remove server and related data
router.delete('/servers/:server_ip', authenticateAdmin,
    param('server_ip').isIP(),
    databaseController.removeServer
);

// Add new server
router.post('/servers', authenticateAdmin,
    [
        body('server_ip').isIP(),
        body('server_name').isString().notEmpty()
    ],
    databaseController.addServer
);

// Delete specific user (Admin only)
router.delete('/users/:username', authenticateAdmin,
    param('username').isString().notEmpty(),
    validateRequest,
    databaseController.deleteUser
);

// Get all users (Admin only)
router.get('/users', authenticateAdmin, databaseController.getAllUsers);

// Get all servers (Admin only)
router.get('/servers', authenticateAdmin, databaseController.getAllServers);

module.exports = router;




