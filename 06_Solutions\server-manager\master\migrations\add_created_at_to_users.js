const db = require('../db');

console.log('🔄 Starting migration to add created_at column to users table...');

db.serialize(() => {
    // Create a new table with the desired schema
    db.run(`
        CREATE TABLE users_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            role TEXT NOT NULL,
            created_at DATETIME
        )
    `, (err) => {
        if (err) {
            console.error('❌ Error creating new table:', err.message);
            process.exit(1);
        }
        console.log('✅ Created temporary table');

        // Copy data from old table to new table with specific created_at
        db.run(`
            INSERT INTO users_new (id, username, password, role, created_at)
            SELECT id, username, password, role, '2025-04-01 12:00:00'
            FROM users
        `, (err) => {
            if (err) {
                console.error('❌ Error copying data:', err.message);
                process.exit(1);
            }
            console.log('✅ Copied data with new created_at timestamp');

            // Drop old table
            db.run(`DROP TABLE users`, (err) => {
                if (err) {
                    console.error('❌ Error dropping old table:', err.message);
                    process.exit(1);
                }
                console.log('✅ Dropped old table');

                // Rename new table
                db.run(`ALTER TABLE users_new RENAME TO users`, (err) => {
                    if (err) {
                        console.error('❌ Error renaming table:', err.message);
                        process.exit(1);
                    }
                    console.log('✅ Renamed new table');

                    // Verify the migration
                    db.all('SELECT * FROM users', (err, rows) => {
                        if (err) {
                            console.error('❌ Error verifying migration:', err.message);
                            process.exit(1);
                        }
                        console.log('📝 Current users data:', rows);
                        console.log('✅ Migration completed successfully');
                        db.close();
                    });
                });
            });
        });
    });
});
