const axios = require('axios');
const db = require('../db/db');
const SLAVE_PORT = process.env.SLAVE_PORT;
const queryDb = (query, params = []) =>
    new Promise((resolve, reject) => {
        db.all(query, params, (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
        });
    });

const runDb = (query, params = []) =>
    new Promise((resolve, reject) => {
        db.run(query, params, function (err) {
            if (err) reject(err);
            else resolve();
        });
    });

async function updateArasVersions() {
    const applications = await queryDb(
        `SELECT 
            a.id as application_id, 
            a.path, 
            s.ip as server_ip,
            a.name as application_name,
            v.aras_version,
            v.build_number
        FROM applications a
        JOIN servers s ON a.server_id = s.id
        JOIN aras_versions v ON a.id = v.application_id
        WHERE a.path IS NOT NULL AND a.path != ''
          AND (v.aras_version IS NULL OR v.build_number IS NULL)`
    );

    if (!applications.length) {
        return { processed: 0, updated: 0, message: 'No applications with missing version/build found.' };
    }

    // Group applications by server IP
    const serverApplications = applications.reduce((acc, app) => {
        if (!acc[app.server_ip]) acc[app.server_ip] = [];
        acc[app.server_ip].push(app);
        return acc;
    }, {});

    let totalProcessed = 0;
    let totalUpdated = 0;

    for (const [serverIp, apps] of Object.entries(serverApplications)) {
        try {
            const appPathsToQuery = apps.map(app => app.path);

            const response = await axios.get(`http://${serverIp}:${SLAVE_PORT}/aras-version`, {
                params: { appPaths: JSON.stringify(appPathsToQuery) },
                timeout: 30000
            });

            const versionData = response.data;

            for (const app of apps) {
                const appData = versionData[app.path];

                if (!appData || appData.version === 'Unknown' || !appData.version) {
                    continue;
                }

                await runDb(
                    `UPDATE aras_versions 
                     SET aras_version = ?, build_number = ? 
                     WHERE application_id = ?`,
                    [appData.version, appData.build, app.application_id]
                );

                totalUpdated++;
            }

            totalProcessed += apps.length;
        } catch (error) {
            console.error(`Error processing server ${serverIp}:`, error.message);
            // Skip this server and continue with the next one
        }
    }

    return {
        processed: totalProcessed,
        updated: totalUpdated,
        message: 'Aras version update completed'
    };
}

module.exports = { updateArasVersions };
