const db = require('../db');

console.log('🔄 Starting migration to remove server_type column...');

db.serialize(() => {
    // Create new table without server_type
    db.run(`
        CREATE TABLE servers_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            server_ip TEXT NOT NULL UNIQUE,
            server_name TEXT NOT NULL
        )
    `, (err) => {
        if (err) {
            console.error('❌ Error creating new table:', err.message);
            process.exit(1);
        }
        console.log('✅ Created new table schema');
    });

    // Copy data without server_type
    db.run(`
        INSERT INTO servers_new (id, server_ip, server_name)
        SELECT id, server_ip, server_name FROM servers
    `, (err) => {
        if (err) {
            console.error('❌ Error migrating data:', err.message);
            process.exit(1);
        }
        console.log('✅ Migrated existing data');
    });

    // Drop old table
    db.run(`DROP TABLE servers`, (err) => {
        if (err) {
            console.error('❌ Error dropping old table:', err.message);
            process.exit(1);
        }
        console.log('✅ Dropped old table');
    });

    // Rename new table
    db.run(`ALTER TABLE servers_new RENAME TO servers`, (err) => {
        if (err) {
            console.error('❌ Error renaming table:', err.message);
            process.exit(1);
        }
        console.log('✅ Renamed new table');

        // Verify migration
        db.all('SELECT * FROM servers', (err, rows) => {
            if (err) {
                console.error('❌ Error verifying migration:', err.message);
                process.exit(1);
            }
            console.log('📝 Current servers:', rows);
            console.log('✅ Migration completed successfully');
            db.close();
        });
    });
});